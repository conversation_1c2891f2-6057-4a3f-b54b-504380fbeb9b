-- ===========================================
-- FUNCTIONS AND TRIGGERS
-- ===========================================

-- Sync auth.users to public.users
CREATE OR REPLACE FUNCTION public.handle_new_user()
RET<PERSON>NS trigger AS $$
DECLARE
  org_id UUID;
  fname TEXT;
  lname TEXT;
  user_org_role TEXT;
BEGIN
  -- Get organization ID and org_role for this email from allowed_emails table
  SELECT organization_id, org_role INTO org_id, user_org_role
  FROM public.allowed_emails
  WHERE email = NEW.email AND is_active = true
  LIMIT 1;

  -- Extract first and last name from raw_user_meta_data
  fname := NEW.raw_user_meta_data ->> 'first_name';
  lname := NEW.raw_user_meta_data ->> 'last_name';

  -- Insert into public.users with all required fields including org_role
  INSERT INTO public.users (id, email, organization_id, first_name, last_name, org_role)
  VALUES (NEW.id, NEW.email, org_id, fname, lname, COALESCE(user_org_role, 'admin'));

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER on_auth_user_created
AFTER INSERT ON auth.users
FOR EACH ROW
EXECUTE FUNCTION public.handle_new_user();

-- Email allowlist validation
CREATE OR REPLACE FUNCTION auth.check_if_email_is_allowed()
RETURNS trigger AS $$
DECLARE
  email_exists BOOLEAN;
  org_id UUID;
BEGIN
  -- Check if the email exists in the allowed_emails table
  SELECT EXISTS (
    SELECT 1
    FROM public.allowed_emails
    WHERE email = NEW.email
    AND is_active = true
    AND (expiry_date IS NULL OR expiry_date > now())
  ) INTO email_exists;

  -- If email doesn't exist in the allowed list, raise an exception
  IF NOT email_exists THEN
    RAISE EXCEPTION 'Email not authorized to sign up.';
  END IF;
  
  -- We don't need to set the organization_id here,
  -- as the handle_new_user trigger will handle that
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger on auth.users to run before insert
CREATE TRIGGER check_email_before_signup
BEFORE INSERT ON auth.users
FOR EACH ROW
EXECUTE FUNCTION auth.check_if_email_is_allowed();

-- Function to update updated_at column when a record is updated
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update organizations.updated_at
CREATE TRIGGER update_organizations_updated_at
BEFORE UPDATE ON organizations
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column(); 