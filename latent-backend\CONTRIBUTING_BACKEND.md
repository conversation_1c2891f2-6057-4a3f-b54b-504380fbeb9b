# Contributing to Latent Backend

This repo powers the FastAPI backend for the Latent platform.

---

## 🔄 Branching Strategy

- All development happens on feature branches off `dev`
- Only PRs should be used to merge changes into `dev` or `main`

### Branch Naming

```
feature/user-auth
fix/missing-endpoint
chore/update-docs
```

---

## ⚙️ Setup (Local Dev)

```bash
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
uvicorn app.main:app --reload
```

Visit: [http://localhost:8000/docs](http://localhost:8000/docs)

---

## 🔐 Environment Variables

Create a `.env` file at the root:

```env
SUPABASE_URL=your-supabase-url
SUPABASE_SERVICE_KEY=your-service-key
```

Do **not** commit this file.

---

## ✅ Pull Request Checklist

- All endpoints tested locally
- Follows structure inside `app/api/`, `app/services/`, etc.
- `main.py` and routing are clean
- Code is formatted with `black` or equivalent
- Includes docstrings/comments if logic is non-trivial

---

## 🔁 CI/CD

- Push to `dev` → deploys to `latent-backend-dev`
- Push to `main` → deploys to `latent-backend-prod`
