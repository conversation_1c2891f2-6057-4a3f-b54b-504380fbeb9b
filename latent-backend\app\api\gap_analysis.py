from fastapi import APIRouter, Depends, HTTPException, Request, Query, BackgroundTasks
from typing import Dict, Any, Optional, List
import os
import httpx
import json
from app.services.auth_utils import verify_token
from fastapi.responses import JSONResponse
from app.services.gap_analysis_utils.document_converter import get_document_text
from app.services.gap_analysis_utils.gap_analysis_wrapper import analyze_sop
import tempfile
from app.services.error_logger import log_user_request, log_interaction
import uuid
import asyncio
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

# analysis_stub_data = {
#     "gaps": [
#       {
#         "critique": "The SOP lacks a clear definition of what constitutes an OOS result, making it difficult to determine when an investigation is required. Additionally, the SOP does not explicitly state that the Quality Control Unit (QCU) must review and approve the OOS investigation before a batch is released or rejected, which is a critical requirement. The SOP also does not address the need to identify and correct the root cause of the OOS result to prevent recurrence through Corrective and Preventive Actions (CAPA).",
#         "guidelines_reference": "Sec. 211.192 All drug product production and control records, including those for packaging and labeling, shall be reviewed and approved by the quality control unit to determine compliance with all established, approved written procedures before a batch is released or distributed. Any unexplained discrepancy (including a percentage of theoretical yield exceeding the maximum or minimum percentages established in master production and control records) or the failure of a batch or any of its components to meet any of its specifications shall be thoroughly investigated, whether or not the batch has already been distributed. The investigation shall extend to other batches of the same drug product and other drug products that may have been associated with the specific failure or discrepancy. A written record of the investigation shall be made and shall include the conclusions and followup.",
#         "sop_reference": "The purpose of this SOP (Standard Operating Procedure) is to describe the procedure for handling out of specification results obtained during analysis.\n5.16 Final conclusion shall be made in the OOS investigation form for rejection/ approval by Quality Head.\n5.2 QC In charge or designee shall carry out the investigation and fill in the form (Stage A).",
#         "priority": "High"
#       },
#       {
#         "critique": "The SOP mentions re-analyzing the same sample by another analyst if an error is reported in Stage A, but it does not specify the criteria for determining if an error has occurred or how the error should be documented. The SOP also does not specify the number of retests or re-sampling allowed. Multiple retests without justification can lead to data manipulation and masking of true OOS results.",
#         "guidelines_reference": "Sec. 211.100 (b) Written production and process control procedures shall be followed in the execution of the various production and process control functions and shall be documented at the time of performance. Any deviation from the written procedures shall be recorded and justified.\nSec. 211.160 (a) The establishment of any specifications, standards, sampling plans, test procedures, or other laboratory control mechanisms required by this subpart, including any change in such specifications, standards, sampling plans, test procedures, or other laboratory control mechanisms, shall be drafted by the appropriate organizational unit and reviewed and approved by the quality control unit.",
#         "sop_reference": "5.3 If it is found that the parameter of stage A is not satisfactory, any error is reported, correct the parameter and the same sample shall be reanalyzed by another analyst.\n5.6 If (Stage B) result is within specification (passes), the material shall be reanalyzed by the first analyst with same sample material and to investigate the first analysts.",
#         "priority": "Medium"
#       },
#       {
#         "critique": "The SOP does not include a requirement to assess the impact of the OOS result on other batches or products that may have been manufactured using the same components or processes.",
#         "guidelines_reference": "Sec. 211.192 Any unexplained discrepancy (including a percentage of theoretical yield exceeding the maximum or minimum percentages established in master production and control records) or the failure of a batch or any of its components to meet any of its specifications shall be thoroughly investigated, whether or not the batch has already been distributed. The investigation shall extend to other batches of the same drug product and other drug products that may have been associated with the specific failure or discrepancy.",
#         "sop_reference": "5.2 QC In charge or designee shall carry out the investigation and fill in the form (Stage A).",
#         "priority": "Medium"
#       },
#       {
#         "critique": "The SOP does not specify the retention period for OOS investigation records. All records must be retained for at least one year after the expiration date of the batch or three years after distribution if there is no expiration date.",
#         "guidelines_reference": "Sec. 211.180 (a) Any production, control, or distribution record that is required to be maintained in compliance with this part and is specifically associated with a batch of a drug product shall be retained for at least 1 year after the expiration date of the batch or, in the case of certain OTC drug products lacking expiration dating because they meet the criteria for exemption under § 211.137, 3 years after distribution of the batch.",
#         "sop_reference": "No mention of retention period.",
#         "priority": "High"
#       }
#     ],
#     "gap_steps": [
#       {
#         "critique": "The SOP lacks a clear definition of what constitutes an OOS result, making it difficult to determine when an investigation is required. Additionally, the SOP does not explicitly state that the Quality Control Unit (QCU) must review and approve the OOS investigation before a batch is released or rejected, which is a critical requirement. The SOP also does not address the need to identify and correct the root cause of the OOS result to prevent recurrence through Corrective and Preventive Actions (CAPA).",
#         "guidelines_reference": "Sec. 211.192 All drug product production and control records, including those for packaging and labeling, shall be reviewed and approved by the quality control unit to determine compliance with all established, approved written procedures before a batch is released or distributed. Any unexplained discrepancy (including a percentage of theoretical yield exceeding the maximum or minimum percentages established in master production and control records) or the failure of a batch or any of its components to meet any of its specifications shall be thoroughly investigated, whether or not the batch has already been distributed. The investigation shall extend to other batches of the same drug product and other drug products that may have been associated with the specific failure or discrepancy. A written record of the investigation shall be made and shall include the conclusions and followup.",
#         "sop_reference": "The purpose of this SOP (Standard Operating Procedure) is to describe the procedure for handling out of specification results obtained during analysis.\n5.16 Final conclusion shall be made in the OOS investigation form for rejection/ approval by Quality Head.\n5.2 QC In charge or designee shall carry out the investigation and fill in the form (Stage A).",
#         "priority": "High",
#         "missing_step": "Add a new section '3.0 Definitions' after '2.0 SCOPE' to define 'Out-of-Specification (OOS) result' as: 'An OOS result is any test result that falls outside the established acceptance criteria or specifications detailed in the relevant pharmacopoeia, regulatory filings, or internal specifications. This includes results that are numerically outside the specification limits or exhibit atypical or aberrant characteristics that are not within the expected normal variation of the test method.'"
#       },
#       {
#         "critique": "The SOP lacks a clear definition of what constitutes an OOS result, making it difficult to determine when an investigation is required. Additionally, the SOP does not explicitly state that the Quality Control Unit (QCU) must review and approve the OOS investigation before a batch is released or rejected, which is a critical requirement. The SOP also does not address the need to identify and correct the root cause of the OOS result to prevent recurrence through Corrective and Preventive Actions (CAPA).",
#         "guidelines_reference": "Sec. 211.192 All drug product production and control records, including those for packaging and labeling, shall be reviewed and approved by the quality control unit to determine compliance with all established, approved written procedures before a batch is released or distributed. Any unexplained discrepancy (including a percentage of theoretical yield exceeding the maximum or minimum percentages established in master production and control records) or the failure of a batch or any of its components to meet any of its specifications shall be thoroughly investigated, whether or not the batch has already been distributed. The investigation shall extend to other batches of the same drug product and other drug products that may have been associated with the specific failure or discrepancy. A written record of the investigation shall be made and shall include the conclusions and followup.",
#         "sop_reference": "The purpose of this SOP (Standard Operating Procedure) is to describe the procedure for handling out of specification results obtained during analysis.\n5.16 Final conclusion shall be made in the OOS investigation form for rejection/ approval by Quality Head.\n5.2 QC In charge or designee shall carry out the investigation and fill in the form (Stage A).",
#         "priority": "High",
#         "missing_step": "Add a new step '5.17 Quality Control Unit (QCU) Review and Approval:' after step '5.16 Final conclusion shall be made in the OOS investigation form for rejection/ approval by Quality Head.' This step should state: '5.17.1 The completed OOS investigation form, along with all supporting documentation, shall be submitted to the Quality Control Unit (QCU) for review. 5.17.2 The QCU shall review the investigation to ensure completeness, scientific justification, and compliance with this SOP and relevant regulatory guidelines. 5.17.3 The QCU Head or designee shall approve the OOS investigation and the proposed batch disposition (release or reject) before any batch is released or rejected. This approval shall be documented on the OOS investigation form.'"
#       },
#       {
#         "critique": "The SOP lacks a clear definition of what constitutes an OOS result, making it difficult to determine when an investigation is required. Additionally, the SOP does not explicitly state that the Quality Control Unit (QCU) must review and approve the OOS investigation before a batch is released or rejected, which is a critical requirement. The SOP also does not address the need to identify and correct the root cause of the OOS result to prevent recurrence through Corrective and Preventive Actions (CAPA).",
#         "guidelines_reference": "Sec. 211.192 All drug product production and control records, including those for packaging and labeling, shall be reviewed and approved by the quality control unit to determine compliance with all established, approved written procedures before a batch is released or distributed. Any unexplained discrepancy (including a percentage of theoretical yield exceeding the maximum or minimum percentages established in master production and control records) or the failure of a batch or any of its components to meet any of its specifications shall be thoroughly investigated, whether or not the batch has already been distributed. The investigation shall extend to other batches of the same drug product and other drug products that may have been associated with the specific failure or discrepancy. A written record of the investigation shall be made and shall include the conclusions and followup.",
#         "sop_reference": "The purpose of this SOP (Standard Operating Procedure) is to describe the procedure for handling out of specification results obtained during analysis.\n5.16 Final conclusion shall be made in the OOS investigation form for rejection/ approval by Quality Head.\n5.2 QC In charge or designee shall carry out the investigation and fill in the form (Stage A).",
#         "priority": "High",
#         "missing_step": "Add a new step '5.18 Corrective and Preventive Action (CAPA):' after step '5.17 Quality Control Unit (QCU) Review and Approval:'. This step should state: '5.18.1 If the root cause of the OOS result is identified as assignable (e.g., analyst error, equipment malfunction, procedural deviation), appropriate Corrective Actions (CA) shall be implemented to address the immediate issue and prevent recurrence. 5.18.2 If the root cause is identified as systemic or potentially impacting other products or processes, Preventive Actions (PA) shall be initiated. 5.18.3 CAPA actions shall be documented in the OOS investigation form and tracked to completion. 5.18.4 The effectiveness of implemented CAPA actions shall be evaluated and documented.'"
#       },
#       {
#         "critique": "The SOP mentions re-analyzing the same sample by another analyst if an error is reported in Stage A, but it does not specify the criteria for determining if an error has occurred or how the error should be documented. The SOP also does not specify the number of retests or re-sampling allowed. Multiple retests without justification can lead to data manipulation and masking of true OOS results.",
#         "guidelines_reference": "Sec. 211.100 (b) Written production and process control procedures shall be followed in the execution of the various production and process control functions and shall be documented at the time of performance. Any deviation from the written procedures shall be recorded and justified.\nSec. 211.160 (a) The establishment of any specifications, standards, sampling plans, test procedures, or other laboratory control mechanisms required by this subpart, including any change in such specifications, standards, sampling plans, test procedures, or other laboratory control mechanisms, shall be drafted by the appropriate organizational unit and reviewed and approved by the quality control unit.",
#         "sop_reference": "5.3 If it is found that the parameter of stage A is not satisfactory, any error is reported, correct the parameter and the same sample shall be reanalyzed by another analyst.\n5.6 If (Stage B) result is within specification (passes), the material shall be reanalyzed by the first analyst with same sample material and to investigate the first analysts.",
#         "priority": "Medium",
#         "missing_step": "Modify step '5.3' to include specific criteria and documentation for errors in Stage A. Revised step '5.3' should read: '5.3 If during Stage A investigation, a clear assignable cause for the OOS result is identified, such as a calculation error, instrument malfunction, or sample preparation error demonstrably linked to the initial analysis, this must be documented in detail in the OOS investigation form, including objective evidence (e.g., instrument logs, calibration records). The parameter shall be corrected, and the same sample may be re-analyzed by another analyst. If no assignable cause is identified in Stage A, proceed to Stage B investigation (step 5.5).'"
#       },
#       {
#         "critique": "The SOP mentions re-analyzing the same sample by another analyst if an error is reported in Stage A, but it does not specify the criteria for determining if an error has occurred or how the error should be documented. The SOP also does not specify the number of retests or re-sampling allowed. Multiple retests without justification can lead to data manipulation and masking of true OOS results.",
#         "guidelines_reference": "Sec. 211.100 (b) Written production and process control procedures shall be followed in the execution of the various production and process control functions and shall be documented at the time of performance. Any deviation from the written procedures shall be recorded and justified.\nSec. 211.160 (a) The establishment of any specifications, standards, sampling plans, test procedures, or other laboratory control mechanisms required by this subpart, including any change in such specifications, standards, sampling plans, test procedures, or other laboratory control mechanisms, shall be drafted by the appropriate organizational unit and reviewed and approved by the quality control unit.",
#         "sop_reference": "5.3 If it is found that the parameter of stage A is not satisfactory, any error is reported, correct the parameter and the same sample shall be reanalyzed by another analyst.\n5.6 If (Stage B) result is within specification (passes), the material shall be reanalyzed by the first analyst with same sample material and to investigate the first analysts.",
#         "priority": "Medium",
#         "missing_step": "Add a new step '5.19 Retesting and Resampling Limits:' after step '5.18 Corrective and Preventive Action (CAPA):'. This step should state: '5.19.1  Retesting (analysis of the original sample solution or a new solution prepared from the original sample composite) is permitted only when a clear assignable cause is identified and documented in Stage A.  5.19.2 Resampling (collecting a new sample from the batch) is permitted only if the Stage B or Stage C investigation indicates a sampling error or batch homogeneity issue, and must be justified and authorized by the Quality Head. 5.19.3  In general, the number of retests should be limited to a predefined number (e.g., no more than two retests per original sample, excluding Stage C resampling). Any deviation from this limit requires documented justification and QCU approval. 5.19.4 All original and retest/resample results must be reported in the OOS investigation and considered in the final batch disposition decision.'"
#       },
#       {
#         "critique": "The SOP does not include a requirement to assess the impact of the OOS result on other batches or products that may have been manufactured using the same components or processes.",
#         "guidelines_reference": "Sec. 211.192 Any unexplained discrepancy (including a percentage of theoretical yield exceeding the maximum or minimum percentages established in master production and control records) or the failure of a batch or any of its components to meet any of its specifications shall be thoroughly investigated, whether or not the batch has already been distributed. The investigation shall extend to other batches of the same drug product and other drug products that may have been associated with the specific failure or discrepancy.",
#         "sop_reference": "5.2 QC In charge or designee shall carry out the investigation and fill in the form (Stage A).",
#         "priority": "Medium",
#         "missing_step": "Add a new step '5.20 Batch Impact Assessment:' after step '5.19 Retesting and Resampling Limits:'. This step should state: '5.20.1 As part of the OOS investigation, assess the potential impact of the OOS result on other batches of the same drug product and other drug products that may have been manufactured using the same raw materials, equipment, processes, or analytical methods. 5.20.2 This assessment should consider factors such as the nature of the OOS result, the root cause, and the manufacturing/testing history of related products. 5.20.3 The batch impact assessment shall be documented in the OOS investigation form, including the rationale for the scope of the assessment and the conclusions.'"
#       },
#       {
#         "critique": "The SOP does not specify the retention period for OOS investigation records. All records must be retained for at least one year after the expiration date of the batch or three years after distribution if there is no expiration date.",
#         "guidelines_reference": "Sec. 211.180 (a) Any production, control, or distribution record that is required to be maintained in compliance with this part and is specifically associated with a batch of a drug product shall be retained for at least 1 year after the expiration date of the batch or, in the case of certain OTC drug products lacking expiration dating because they meet the criteria for exemption under § 211.137, 3 years after distribution of the batch.",
#         "sop_reference": "No mention of retention period.",
#         "priority": "High",
#         "missing_step": "Add a new section '7.0 Record Retention' after '6.0 ABBREVIATIONS'. This section should state: '7.0 RECORD RETENTION: 7.1 All OOS investigation records, including the OOS investigation form, attachments, and supporting documentation, shall be retained for at least one year after the expiration date of the batch associated with the investigation, or three years after distribution of the batch if there is no expiration date, whichever is longer. 7.2 Records shall be stored in a secure manner, protected from unauthorized access, and readily retrievable for audits and inspections.'"
#       }
#     ]
#   }


router = APIRouter()

SUPABASE_URL = os.getenv("SUPABASE_URL") or ''
SUPABASE_PUBLIC_KEY = os.getenv("SUPABASE_PUBLIC_KEY") or ''

if not SUPABASE_URL or not SUPABASE_PUBLIC_KEY:
    raise RuntimeError("SUPABASE_URL and SUPABASE_PUBLIC_KEY environment variables must be set.")

def get_headers(payload: Dict[Any, Any]):
    """Generate headers for Supabase API calls with safe defaults"""
    return {
        "apikey": SUPABASE_PUBLIC_KEY or '',
        "Authorization": f"Bearer {payload.get('access_token', '') or ''}",
        "Content-Type": "application/json",
        "Prefer": "return=representation"
    }


@router.get('/')
def health_check(request: Request):
    # Simple health check with minimal logging
    request_id = str(uuid.uuid4())
    ip_address = request.client.host
    endpoint = str(request.url)

    # Log health check (non-blocking)
    asyncio.create_task(log_interaction(request_id, "health_check", f"Health check from IP: {ip_address}",
                                      params={"endpoint": endpoint, "ip_address": ip_address}))

    return {"result": "OK"}

@router.post('/analyze/{sop_id}')
async def analyze_sop_with_regulations(
    request: Request,
    sop_id: str,
    regulation_ids: List[str],
    payload: Dict[Any, Any] = Depends(verify_token),
    background_tasks: BackgroundTasks = None
):
    # Create request_id at the start of the API call
    import uuid
    request_id = str(uuid.uuid4())
    user_id = payload.get("sub")
    user_email = payload.get("email", "")
    ip_address = request.client.host
    endpoint = str(request.url)
    method = request.method
    request_params = {"sop_id": sop_id, "regulation_ids": regulation_ids}

    # Log the user request (non-blocking)
    await log_user_request(
        user_id, user_email, ip_address, endpoint, method, request_params, request_id=request_id
    )
    # Log the initial interaction (non-blocking)
    asyncio.create_task(log_interaction(request_id, "start", "Started analyze_sop_with_regulations", params=request_params))

    # Add request_id to payload for downstream use
    payload["request_id"] = request_id

    try:
        # Get base URL from request
        base_url = str(request.base_url).rstrip('/')
        
        # Get the access token from the payload
        access_token = payload.get('access_token', '')
        
        # Prepare headers with the token
        headers = {
            "Authorization": f"Bearer {access_token}"
        }
        
        # Create an initial record with "processing" status
        try:
            asyncio.create_task(log_interaction(request_id, "check_gap_analysis_result_attempt", "Checking for existing gap analysis result", params={"sop_id": sop_id}))
            async with httpx.AsyncClient() as client:
                check_response = await client.get(
                    f"{SUPABASE_URL}/rest/v1/gap_analysis_results",
                    headers=get_headers(payload),
                    params={"sop_id": f"eq.{sop_id}", "select": "id"}
                )
            asyncio.create_task(log_interaction(request_id, "check_gap_analysis_result_success", "Checked for existing gap analysis result", response=check_response.json()))
            initial_data = {
                "sop_id": sop_id,
                "metadata": {"regulation_ids": regulation_ids, "status": "processing"}
            }
            if check_response.status_code == 200 and check_response.json():
                record_id = check_response.json()[0].get("id")
                asyncio.create_task(log_interaction(request_id, "update_gap_analysis_result_attempt", "Updating existing gap analysis result", params={"record_id": record_id}))
                async with httpx.AsyncClient() as client:
                    db_response = await client.patch(
                        f"{SUPABASE_URL}/rest/v1/gap_analysis_results",
                        headers=get_headers(payload),
                        params={"id": f"eq.{record_id}"},
                        json=initial_data
                    )
                asyncio.create_task(log_interaction(request_id, "update_gap_analysis_result_success", "Updated existing gap analysis result", response=db_response.json()))
            else:
                asyncio.create_task(log_interaction(request_id, "create_gap_analysis_result_attempt", "Creating new gap analysis result", params=initial_data))
                async with httpx.AsyncClient() as client:
                    db_response = await client.post(
                        f"{SUPABASE_URL}/rest/v1/gap_analysis_results",
                        headers=get_headers(payload),
                        json=initial_data
                    )
                asyncio.create_task(log_interaction(request_id, "create_gap_analysis_result_success", "Created new gap analysis result", response=db_response.json()))
            if db_response.status_code not in (200, 201):
                if background_tasks:
                    background_tasks.add_task(log_interaction, request_id, "gap_analysis_result_db_failure", "Failed to store initial processing status", status="error", response={"text": db_response.text})
        except Exception as db_error:
            if background_tasks:
                background_tasks.add_task(log_interaction, request_id, "gap_analysis_result_db_exception", "Exception while storing initial processing status", status="error", response={"error": str(db_error)})

        # Download the SOP file by calling the download endpoint directly
        asyncio.create_task(log_interaction(request_id, "download_sop_attempt", "Attempting to download SOP file", params={"sop_id": sop_id}))
        async with httpx.AsyncClient(follow_redirects=True) as client:
            download_response = await client.get(
                f"{base_url}/sops/download/{sop_id}",
                headers=headers
            )
        if download_response.status_code != 200:
            if background_tasks:
                background_tasks.add_task(log_interaction, request_id, "download_sop_failure", "Failed to download SOP file", status="error", response={"status_code": download_response.status_code, "text": download_response.text})
            await update_analysis_status(sop_id, "failed", "Failed to download SOP file", payload, request_id)
            raise HTTPException(
                status_code=download_response.status_code,
                detail="We couldn't download the requested SOP file. Please try again later."
            )
        asyncio.create_task(log_interaction(request_id, "download_sop_success", "Downloaded SOP file successfully"))
        file_content = download_response.content

        # Get SOP metadata for the response and to determine file type
        asyncio.create_task(log_interaction(request_id, "fetch_sop_metadata_attempt", "Attempting to fetch SOP metadata", params={"sop_id": sop_id}))
        async with httpx.AsyncClient(follow_redirects=True) as client:
            sop_response = await client.get(
                f"{base_url}/sops/{sop_id}",
                headers=headers
            )
        if sop_response.status_code != 200:
            if background_tasks:
                background_tasks.add_task(log_interaction, request_id, "fetch_sop_metadata_failure", "Failed to fetch SOP metadata", status="error", response={"status_code": sop_response.status_code, "text": sop_response.text})
            await update_analysis_status(sop_id, "failed", "Failed to fetch SOP details", payload, request_id)
            raise HTTPException(status_code=sop_response.status_code, 
                detail="We couldn't retrieve the SOP details. Please try again later.")
        asyncio.create_task(log_interaction(request_id, "fetch_sop_metadata_success", "Fetched SOP metadata successfully", response=sop_response.json()))
        sop = sop_response.json()
        file_name = sop.get("file_name")

        # Get regulation details by calling the existing endpoint
        asyncio.create_task(log_interaction(request_id, "fetch_regulations_attempt", "Attempting to fetch enabled organization regulations", params={"regulation_ids": regulation_ids}))
        async with httpx.AsyncClient(follow_redirects=True) as client:
            regulations_response = await client.get(
                f"{base_url}/regulations/organization",
                headers=headers
            )
        if regulations_response.status_code != 200:
            if background_tasks:
                background_tasks.add_task(log_interaction, request_id, "fetch_regulations_failure", "Failed to fetch enabled organization regulations", status="error", response={"status_code": regulations_response.status_code, "text": regulations_response.text})
            await update_analysis_status(sop_id, "failed", "Failed to fetch regulations", payload, request_id)
            raise HTTPException(status_code=regulations_response.status_code,
                detail="We couldn't retrieve the enabled regulations for your organization. Please try again later.")
        asyncio.create_task(log_interaction(request_id, "fetch_regulations_success", "Fetched enabled organization regulations successfully", response=regulations_response.json()))
        enabled_org_regulations = regulations_response.json()
        
        
        


        

        matched_regulations = [
            r for r in enabled_org_regulations 
            if r.get("regulation_id") in regulation_ids and r.get("regulation_details")
        ]
        if not matched_regulations:
            if background_tasks:
                background_tasks.add_task(log_interaction, request_id, "no_enabled_regulations", "No enabled regulations found matching the provided IDs", status="error", response={"regulation_ids": regulation_ids})
            await update_analysis_status(sop_id, "failed", "No enabled regulations found matching the provided IDs", payload, request_id)
            raise HTTPException(status_code=404, detail="No enabled regulations were found for your request. Please check your selection or try again later.")
        
        
        # Check the file extension and extract text content

        # Check the file extension and extract text content
        extension = file_name.split('.')[-1].lower() if '.' in file_name else ''
        try:
            asyncio.create_task(log_interaction(request_id, "extract_text_attempt", "Attempting to extract text from SOP file", params={"file_name": file_name}))
            with tempfile.NamedTemporaryFile(suffix=f".{extension}", delete=False) as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name
            sop_text = get_document_text(temp_file_path, request_id=request_id)
            asyncio.create_task(log_interaction(request_id, "extract_text_success", "Extracted text from SOP file successfully"))
            regulation_documents = []
            for regulation in matched_regulations:
                details = regulation.get("regulation_details")
                if details and "source" in details:
                    regulation_documents.append(details["source"])
            if len(regulation_documents) == 0:
                if background_tasks:
                    background_tasks.add_task(log_interaction, request_id, "no_regulation_content", "No content found for the selected regulations", status="error", response={"regulation_ids": regulation_ids})
                await update_analysis_status(sop_id, "failed", "No content found for the specified enabled regulations", payload, request_id)
                raise HTTPException(status_code=400, detail="No content was found for the selected regulations. Please check your selection or try again later.")
            asyncio.create_task(log_interaction(request_id, "call_llm_attempt", "Calling LLM for gap analysis", params={"sop_id": sop_id, "regulation_ids": regulation_ids}))
            analysis_result = await analyze_sop(
                sop_text,
                regulation_documents,
                request_id=request_id
            )
            asyncio.create_task(log_interaction(request_id, "call_llm_success", "LLM gap analysis completed successfully"))
            gap_count = len(analysis_result.get("gap_steps", []))
            compliance_score = max(0, 100 - (gap_count * 10))
            try:
                regulation_ids_list = [regulation.get("regulation_id") for regulation in matched_regulations]
                asyncio.create_task(log_interaction(request_id, "final_db_check_attempt", "Checking for existing gap analysis result for final update", params={"sop_id": sop_id}))
                async with httpx.AsyncClient() as client:
                    check_response = await client.get(
                        f"{SUPABASE_URL}/rest/v1/gap_analysis_results",
                        headers=get_headers(payload),
                        params={"sop_id": f"eq.{sop_id}", "select": "id"}
                    )
                asyncio.create_task(log_interaction(request_id, "final_db_check_success", "Checked for existing gap analysis result for final update", response={"found_existing": bool(check_response.status_code == 200 and check_response.json())}))

                analysis_data = {
                    "sop_id": sop_id,
                    "gap_details": analysis_result.get("gap_steps", []),
                    "compliance_score": compliance_score,
                    "metadata": {
                        "regulation_ids": regulation_ids_list,
                        "status": "processed"
                    }
                }
                asyncio.create_task(log_interaction(request_id, "prepare_final_analysis_data", "Prepared final analysis data for storage", params={"gap_steps_count": len(analysis_result.get("gap_steps", [])), "compliance_score": compliance_score}))

                if check_response.status_code == 200 and check_response.json():
                    record_id = check_response.json()[0].get("id")
                    asyncio.create_task(log_interaction(request_id, "final_db_update_attempt", "Updating existing gap analysis result with final data", params={"record_id": record_id}))
                    async with httpx.AsyncClient() as client:
                        db_response = await client.patch(
                            f"{SUPABASE_URL}/rest/v1/gap_analysis_results",
                            headers=get_headers(payload),
                            params={"id": f"eq.{record_id}"},
                            json=analysis_data
                        )
                    asyncio.create_task(log_interaction(request_id, "final_db_update_success", "Updated existing gap analysis result with final data", response={"record_id": record_id, "status_code": db_response.status_code}))
                else:
                    asyncio.create_task(log_interaction(request_id, "final_db_create_attempt", "Creating new gap analysis result with final data", params=analysis_data))
                    async with httpx.AsyncClient() as client:
                        db_response = await client.post(
                            f"{SUPABASE_URL}/rest/v1/gap_analysis_results",
                            headers=get_headers(payload),
                            json=analysis_data
                        )
                    asyncio.create_task(log_interaction(request_id, "final_db_create_success", "Created new gap analysis result with final data", response={"status_code": db_response.status_code}))

                if db_response.status_code not in (200, 201):
                    asyncio.create_task(log_interaction(request_id, "final_db_failure", "Failed to store final gap analysis result", status="error", response={"status_code": db_response.status_code, "text": db_response.text}))
                else:
                    asyncio.create_task(log_interaction(request_id, "final_db_success", "Successfully stored final gap analysis result", response={"status_code": db_response.status_code}))
            except Exception as db_error:
                asyncio.create_task(log_interaction(request_id, "final_db_exception", "Exception while storing final gap analysis result", status="error", response={"error": str(db_error)}))
            asyncio.create_task(log_interaction(request_id, "analyze_sop_complete", "Gap analysis completed successfully", response={"sop_id": sop_id, "gap_steps_count": len(analysis_result.get('gap_steps', [])), "regulations_count": len(matched_regulations)}))
            return {
                "sop": {**sop, "text_content": sop_text},
                "regulations": [r.get("regulation_id") for r in matched_regulations],
                "analysis": analysis_result['gap_steps']
            }
        except Exception as e:
            print(f"Unhandled exception in analyze_sop_with_regulations: {e}")
            print(f"Real error (repr): {repr(e)}")
            asyncio.create_task(log_interaction(request_id, "analyze_sop_inner_exception", "Unhandled exception in inner try block", status="error", response={"detail": str(e), "real_error": repr(e)}))
            await update_analysis_status(sop_id, "failed", f"A temporary issue occurred while analyzing the SOP: {e}", payload, request_id)
            raise HTTPException(status_code=503, detail="A temporary issue occurred while analyzing the SOP. Please try again later.")
    except HTTPException as http_error:
        asyncio.create_task(log_interaction(request_id, "analyze_sop_http_exception", "HTTPException occurred", status="error", response={"detail": str(http_error.detail), "status_code": http_error.status_code}))
        raise http_error
    except Exception as e:
        asyncio.create_task(log_interaction(request_id, "analyze_sop_outer_exception", "Unhandled exception in outer try block", status="error", response={"detail": str(e), "real_error": repr(e)}))
        await update_analysis_status(sop_id, "failed", "A temporary issue occurred while analyzing the SOP.", payload, request_id)
        raise HTTPException(status_code=503, detail="A temporary issue occurred while analyzing the SOP. Please try again later.")

async def update_analysis_status(sop_id: str, status: str, message: str, payload: Dict[Any, Any], request_id: str = None):
    """Helper function to update gap analysis status"""
    try:
        if request_id:
            await log_interaction(request_id, "update_analysis_status_start", "Starting analysis status update",
                                params={"sop_id": sop_id, "status": status, "message": message})

        # Check if a record exists for this SOP
        if request_id:
            await log_interaction(request_id, "update_analysis_status_check", "Checking for existing analysis record",
                                params={"sop_id": sop_id})

        async with httpx.AsyncClient() as client:
            check_response = await client.get(
                f"{SUPABASE_URL}/rest/v1/gap_analysis_results",
                headers=get_headers(payload),
                params={
                    "sop_id": f"eq.{sop_id}",
                    "select": "id,metadata"
                }
            )

        if check_response.status_code == 200 and check_response.json():
            # Update the existing record with the new status
            record_id = check_response.json()[0].get("id")
            current_metadata = check_response.json()[0].get("metadata", {})

            if request_id:
                await log_interaction(request_id, "update_analysis_status_found", "Found existing analysis record",
                                    params={"record_id": record_id, "current_status": current_metadata.get("status")})

            # Update metadata with new status and error message
            if not current_metadata:
                current_metadata = {}
            current_metadata["status"] = status
            current_metadata["last_error"] = message

            if request_id:
                await log_interaction(request_id, "update_analysis_status_updating", "Updating analysis status",
                                    params={"record_id": record_id, "new_status": status})

            async with httpx.AsyncClient() as client:
                db_response = await client.patch(
                    f"{SUPABASE_URL}/rest/v1/gap_analysis_results",
                    headers=get_headers(payload),
                    params={"id": f"eq.{record_id}"},
                    json={
                        "metadata": current_metadata
                    }
                )

            if db_response.status_code != 200:
                error_msg = f"Failed to update analysis status: {db_response.text}"
                print(error_msg)
                if request_id:
                    await log_interaction(request_id, "update_analysis_status_db_error", "Database update failed",
                                        status="error", response={"status_code": db_response.status_code, "error": db_response.text})
            else:
                if request_id:
                    await log_interaction(request_id, "update_analysis_status_success", "Successfully updated analysis status",
                                        response={"record_id": record_id, "status": status})
        else:
            if request_id:
                await log_interaction(request_id, "update_analysis_status_not_found", "No existing analysis record found",
                                    params={"sop_id": sop_id, "check_status_code": check_response.status_code})
    except Exception as e:
        error_msg = f"Error updating analysis status: {str(e)}"
        print(error_msg)
        if request_id:
            await log_interaction(request_id, "update_analysis_status_exception", "Exception in update_analysis_status",
                                status="error", response={"error": str(e)})

@router.post('/results')
async def get_multiple_gap_analysis_results(
    request: Request,
    sop_ids: List[str],
    payload: Dict[Any, Any] = Depends(verify_token)
):
    """
    Get existing gap analysis results for multiple SOP IDs.
    Returns all results matching the provided SOP IDs.
    """
    # Create request_id for logging
    request_id = str(uuid.uuid4())
    user_id = payload.get("sub")
    user_email = payload.get("email", "")
    ip_address = request.client.host
    endpoint = str(request.url)
    method = request.method
    request_params = {"sop_ids": sop_ids, "sop_count": len(sop_ids)}

    # Log the user request
    await log_user_request(
        user_id, user_email, ip_address, endpoint, method, request_params, request_id=request_id
    )

    # Log start of function
    asyncio.create_task(log_interaction(request_id, "get_multiple_results_start", "Starting get_multiple_gap_analysis_results", params=request_params))

    try:
        formatted_sop_ids = ','.join([f'"{sop_id}"' for sop_id in sop_ids])

        asyncio.create_task(log_interaction(request_id, "fetch_gap_results_attempt", f"Fetching gap analysis results for {len(sop_ids)} SOPs",
                                          params={"formatted_sop_ids": formatted_sop_ids}))

        async with httpx.AsyncClient() as client:
            # Fetch gap analysis results
            gap_response = await client.get(
                f"{SUPABASE_URL}/rest/v1/gap_analysis_results",
                headers=get_headers(payload),
                params={
                    "sop_id": f"in.({formatted_sop_ids})",
                    "select": "*"
                }
            )

            if gap_response.status_code != 200:
                asyncio.create_task(log_interaction(request_id, "fetch_gap_results_failure", "Failed to fetch gap analysis results",
                                                  status="error", response={"status_code": gap_response.status_code, "error": gap_response.text}))
                raise HTTPException(
                    status_code=gap_response.status_code,
                    detail=f"Failed to query gap analysis results: {gap_response.text}"
                )

            results = gap_response.json()
            asyncio.create_task(log_interaction(request_id, "fetch_gap_results_success", f"Successfully fetched gap analysis results",
                                              response={"results_count": len(results)}))

            # Collect all unique regulation IDs
            asyncio.create_task(log_interaction(request_id, "collect_regulation_ids_start", "Collecting unique regulation IDs from results"))
            all_reg_ids = set()
            for result in results:
                metadata = result.get("metadata", {})
                reg_ids = metadata.get("regulation_ids", [])
                all_reg_ids.update(reg_ids)

            asyncio.create_task(log_interaction(request_id, "collect_regulation_ids_complete", f"Collected regulation IDs",
                                              params={"unique_regulation_ids_count": len(all_reg_ids)}))

            if not all_reg_ids:
                asyncio.create_task(log_interaction(request_id, "no_regulation_ids_found", "No regulation IDs found, returning results without regulation details"))
                return {
                    "count": len(results),
                    "results": results
                }

            formatted_reg_ids = ','.join([f'"{reg_id}"' for reg_id in all_reg_ids])

            # Fetch regulation details
            asyncio.create_task(log_interaction(request_id, "fetch_regulations_attempt", f"Fetching regulation details for {len(all_reg_ids)} regulations",
                                              params={"formatted_reg_ids": formatted_reg_ids}))

            reg_response = await client.get(
                f"{SUPABASE_URL}/rest/v1/regulations",
                headers=get_headers(payload),
                params={
                    "id": f"in.({formatted_reg_ids})",
                    "select": "*"
                }
            )

            if reg_response.status_code != 200:
                asyncio.create_task(log_interaction(request_id, "fetch_regulations_failure", "Failed to fetch regulation details",
                                                  status="error", response={"status_code": reg_response.status_code, "error": reg_response.text}))
                raise HTTPException(
                    status_code=reg_response.status_code,
                    detail=f"Failed to fetch regulations: {reg_response.text}"
                )

            regulations = reg_response.json()
            asyncio.create_task(log_interaction(request_id, "fetch_regulations_success", f"Successfully fetched regulation details",
                                              response={"regulations_count": len(regulations)}))

            reg_dict = {reg["id"]: reg for reg in regulations}

            # Attach relevant regulation data to each result
            asyncio.create_task(log_interaction(request_id, "attach_regulations_start", "Attaching regulation data to results"))
            for result in results:
                metadata = result.get("metadata", {})
                reg_ids = metadata.get("regulation_ids", [])
                result["regulations"] = [reg_dict[reg_id] for reg_id in reg_ids if reg_id in reg_dict]

        asyncio.create_task(log_interaction(request_id, "get_multiple_results_success", "Successfully completed get_multiple_gap_analysis_results",
                                          response={"total_results": len(results), "regulations_attached": len(all_reg_ids)}))

        return {
            "count": len(results),
            "results": results
        }

    except HTTPException as http_error:
        asyncio.create_task(log_interaction(request_id, "get_multiple_results_http_error", "HTTPException in get_multiple_gap_analysis_results",
                                          status="error", response={"detail": str(http_error.detail)}))
        raise http_error
    except Exception as e:
        asyncio.create_task(log_interaction(request_id, "get_multiple_results_exception", "Unexpected exception in get_multiple_gap_analysis_results",
                                          status="error", response={"error": str(e)}))
        raise HTTPException(status_code=500, detail=f"Something went wrong: {str(e)}")

@router.patch('/results/{analysis_id}')
async def update_gap_analysis_metadata(
    request: Request,
    analysis_id: str,
    metadata: Dict[Any, Any],
    payload: Dict[Any, Any] = Depends(verify_token)
):
    """
    Update metadata for a specific gap analysis result.
    """
    # Create request_id for logging
    request_id = str(uuid.uuid4())
    user_id = payload.get("sub")
    user_email = payload.get("email", "")
    ip_address = request.client.host
    endpoint = str(request.url)
    method = request.method
    request_params = {"analysis_id": analysis_id, "metadata_keys": list(metadata.keys()) if metadata else []}

    # Log the user request
    await log_user_request(
        user_id, user_email, ip_address, endpoint, method, request_params, request_id=request_id
    )
    

    # Log start of function
    asyncio.create_task(log_interaction(request_id, "update_metadata_start", f"Starting update_gap_analysis_metadata for analysis: {analysis_id}", params=request_params))

    print(metadata)
    try:
        # Verify the analysis exists and get current data
        asyncio.create_task(log_interaction(request_id, "verify_analysis_attempt", f"Verifying analysis exists: {analysis_id}",
                                          params={"analysis_id": analysis_id}))

        async with httpx.AsyncClient() as client:
            verify_response = await client.get(
                f"{SUPABASE_URL}/rest/v1/gap_analysis_results",
                headers=get_headers(payload),
                params={
                    "id": f"eq.{analysis_id}",
                    "select": "*"
                }
            )

        if verify_response.status_code != 200:
            asyncio.create_task(log_interaction(request_id, "verify_analysis_failure", f"Failed to verify analysis: {analysis_id}",
                                              status="error", response={"status_code": verify_response.status_code, "error": verify_response.text}))
            raise HTTPException(
                status_code=verify_response.status_code,
                detail=f"Failed to verify analysis: {verify_response.text}"
            )

        print(verify_response.json())
        results = verify_response.json()

        if not results:
            asyncio.create_task(log_interaction(request_id, "analysis_not_found", f"Gap analysis result not found: {analysis_id}",
                                              status="error", params={"analysis_id": analysis_id}))
            raise HTTPException(status_code=404, detail="Gap analysis result not found")

        # Get current metadata and preserve regulation_ids while updating everything else
        current_metadata = results[0].get("metadata", {})
        preserved_regulation_ids = current_metadata.get("regulation_ids")

        # Update everything with new metadata, but preserve regulation_ids if it exists
        updated_metadata = metadata.copy()
        if preserved_regulation_ids is not None:
            updated_metadata["regulation_ids"] = preserved_regulation_ids

        asyncio.create_task(log_interaction(request_id, "verify_analysis_success", f"Successfully verified analysis exists: {analysis_id}",
                                          response={"current_metadata": current_metadata}))

        # Update the metadata
        asyncio.create_task(log_interaction(request_id, "update_metadata_attempt", f"Updating metadata for analysis: {analysis_id}",
                                          params={"current_metadata": current_metadata, "new_metadata": metadata, "final_metadata": updated_metadata, "preserved_regulation_ids": preserved_regulation_ids}))

        async with httpx.AsyncClient() as client:
            update_response = await client.patch(
                f"{SUPABASE_URL}/rest/v1/gap_analysis_results",
                headers=get_headers(payload),
                params={"id": f"eq.{analysis_id}"},
                json={"metadata": updated_metadata}
            )
            print(update_response.status_code, 'update_response')

        if update_response.status_code != 200:
            asyncio.create_task(log_interaction(request_id, "update_metadata_failure", f"Failed to update metadata for analysis: {analysis_id}",
                                              status="error", response={"status_code": update_response.status_code, "error": update_response.text}))
            raise HTTPException(
                status_code=update_response.status_code,
                detail=f"Failed to update metadata: {update_response.text}"
            )

        asyncio.create_task(log_interaction(request_id, "update_metadata_success", f"Successfully updated metadata for analysis: {analysis_id}",
                                          response={"analysis_id": analysis_id, "original_metadata": current_metadata, "new_metadata": metadata, "final_metadata": updated_metadata, "preserved_regulation_ids": preserved_regulation_ids}))

        return {"message": "Metadata updated successfully"}

    except HTTPException as http_error:
        print(http_error,'error is here')
        asyncio.create_task(log_interaction(request_id, "update_metadata_http_error", f"HTTPException in update_gap_analysis_metadata: {analysis_id}",
                                          status="error", response={"detail": str(http_error.detail)}))
        raise http_error
    except Exception as e:
        print(e)
        asyncio.create_task(log_interaction(request_id, "update_metadata_exception", f"Unexpected exception in update_gap_analysis_metadata: {analysis_id}",
                                          status="error", response={"error": str(e)}))
        raise HTTPException(status_code=500, detail=f"Something went wrong: {str(e)}")

