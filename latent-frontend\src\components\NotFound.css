.not-found-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background-color: var(--bg-slate-50);
}

.not-found-content {
  text-align: center;
  max-width: 600px;
  padding: 40px;
  background: var(--bg-white);
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.error-icon {
  font-size: 120px;
  font-weight: bold;
  color: var(--primary-blue);
  line-height: 1;
  margin-bottom: 20px;
}

.not-found-content h1 {
  font-size: 24px;
  margin-bottom: 16px;
  color: var(--text-slate-900);
}

.not-found-content p {
  color: var(--text-slate-600);
  margin-bottom: 32px;
  line-height: 1.6;
}

.button-container {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.primary-button {
  padding: 12px 24px;
  background-color: var(--primary-blue);
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.primary-button:hover {
  background-color: var(--primary-blue);
  opacity: 0.9;
}

.secondary-button {
  padding: 12px 24px;
  background-color: var(--bg-white);
  color: var(--primary-blue);
  border: 1px solid var(--primary-blue);
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.secondary-button:hover {
  background-color: var(--hover-slate-50);
}

@media (max-width: 640px) {
  .not-found-content {
    padding: 20px;
  }

  .error-icon {
    font-size: 80px;
  }

  .not-found-content h1 {
    font-size: 20px;
  }

  .button-container {
    flex-direction: column;
  }
} 