#!/usr/bin/env python3
import os
import sys
import httpx
from dotenv import load_dotenv

load_dotenv()

SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_SERVICE_KEY = os.getenv("SUPABASE_SERVICE_KEY")

if not SUPABASE_URL or not SUPABASE_SERVICE_KEY:
    print("Error: SUPABASE_URL and SUPABASE_SERVICE_KEY must be set in .env file")
    sys.exit(1)

def check_organization_exists(name: str) -> bool:
    """Check if an organization with the given name already exists."""
    headers = {
        "apikey": SUPABASE_SERVICE_KEY,
        "Authorization": f"Bearer {SUPABASE_SERVICE_KEY}",
        "Content-Type": "application/json"
    }
    
    try:
        with httpx.Client() as client:
            response = client.get(
                f"{SUPABASE_URL}/rest/v1/organizations",
                headers=headers,
                params={
                    "name": f"eq.{name}",
                    "select": "name"
                }
            )
            response.raise_for_status()
            existing_orgs = response.json()
            return len(existing_orgs) > 0
    except Exception as e:
        print(f"Error checking for existing organization: {str(e)}")
        sys.exit(1)

def create_organization(name: str, industry: str = None, location: str = None, size: int = None, max_sop_uploads: int = None):
    # First check if organization already exists
    if check_organization_exists(name):
        print(f"Error: An organization with the name '{name}' already exists.")
        sys.exit(1)

    headers = {
        "apikey": SUPABASE_SERVICE_KEY,
        "Authorization": f"Bearer {SUPABASE_SERVICE_KEY}",
        "Content-Type": "application/json",
        "Prefer": "return=representation"  # Ask Supabase to return the created record
    }
    
    data = {
        "name": name,
        "industry": industry,
        "location": location,
        "size": size,
        "max_sop_uploads": max_sop_uploads,
        "metadata": {}
    }
    
    try:
        with httpx.Client() as client:
            response = client.post(
                f"{SUPABASE_URL}/rest/v1/organizations",
                headers=headers,
                json=data
            )
            
            # Handle the case where the name is already taken (shouldn't happen due to our check, but just in case)
            if response.status_code == 409:  # Conflict status code
                print(f"Error: An organization with the name '{name}' already exists.")
                sys.exit(1)
                
            response.raise_for_status()
            
            # If we got a 201 Created status, the operation was successful
            if response.status_code == 201:
                print(f"Successfully created organization:")
                print(f"Name: {name}")
                if industry:
                    print(f"Industry: {industry}")
                if location:
                    print(f"Location: {location}")
                if size:
                    print(f"Size: {size}")
                if max_sop_uploads is not None:
                    print(f"Max SOP Uploads: {max_sop_uploads}")
                else:
                    print(f"Max SOP Uploads: Unlimited")
                
                # Try to get the created organization's ID
                try:
                    org = response.json()
                    if org and 'id' in org:
                        print(f"ID: {org['id']}")
                except:
                    print("Note: Organization created but ID not returned")
                return True
            else:
                print(f"Unexpected status code: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
    except httpx.HTTPError as e:
        print(f"HTTP Error: {str(e)}")
        if hasattr(e, 'response'):
            print(f"Response status code: {e.response.status_code}")
            print(f"Response text: {e.response.text}")
        sys.exit(1)
    except Exception as e:
        print(f"Error creating organization: {str(e)}")
        sys.exit(1)

def list_organizations() -> None:
    """List all organizations in the database."""
    headers = {
        "apikey": SUPABASE_SERVICE_KEY,
        "Authorization": f"Bearer {SUPABASE_SERVICE_KEY}",
        "Content-Type": "application/json"
    }
    
    try:
        with httpx.Client() as client:
            response = client.get(
                f"{SUPABASE_URL}/rest/v1/organizations",
                headers=headers,
                params={
                    "select": "id,name,industry,location,size,max_sop_uploads"
                }
            )
            response.raise_for_status()
            organizations = response.json()
            
            if organizations:
                print("\nOrganizations:")
                print("=" * 110)
                print(f"{'ID':<36} | {'Name':<25} | {'Industry':<15} | {'Location':<15} | {'Size':<8} | {'Max SOPs'}")
                print("-" * 110)

                for org in organizations:
                    id_str = str(org.get('id', 'N/A'))
                    name_str = str(org.get('name', 'N/A'))
                    industry_str = str(org.get('industry', 'N/A'))
                    location_str = str(org.get('location', 'N/A'))
                    size_str = str(org.get('size', 'N/A'))
                    max_sop_str = str(org.get('max_sop_uploads', 'Unlimited'))
                    if max_sop_str == 'None':
                        max_sop_str = 'Unlimited'

                    print(f"{id_str:<36} | {name_str:<25} | {industry_str:<15} | {location_str:<15} | {size_str:<8} | {max_sop_str}")
                
                print(f"\nTotal: {len(organizations)} organizations")
            else:
                print("No organizations found in the database")
    except Exception as e:
        print(f"Error listing organizations: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python create_organization.py 'Organization Name' [industry] [location] [size] [max_sop_uploads]")
        print("       python create_organization.py list")
        print("Example: python create_organization.py 'Acme Corp' 'Technology' 'San Francisco' 100 50")
        print("         python create_organization.py 'Test Org' 'Healthcare' 'New York' 25")
        print("Note: max_sop_uploads is optional. If not provided, organization will have unlimited SOP uploads.")
        sys.exit(1)
    
    if sys.argv[1].lower() == "list":
        list_organizations()
        sys.exit(0)

    name = sys.argv[1]
    industry = sys.argv[2] if len(sys.argv) > 2 else None
    location = sys.argv[3] if len(sys.argv) > 3 else None
    size = int(sys.argv[4]) if len(sys.argv) > 4 else None
    max_sop_uploads = int(sys.argv[5]) if len(sys.argv) > 5 else None

    create_organization(name, industry, location, size, max_sop_uploads)