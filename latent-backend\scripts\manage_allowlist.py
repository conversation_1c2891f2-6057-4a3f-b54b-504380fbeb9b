#!/usr/bin/env python3

import os
import sys
import argparse
import httpx
import json
from dotenv import load_dotenv
from supabase import create_client, Client

# Load environment variables
load_dotenv()

def setup_supabase() -> Client:
    """Initialize and return a Supabase client."""
    supabase_url = os.environ.get("SUPABASE_URL")
    supabase_key = os.environ.get("SUPABASE_SERVICE_KEY")
    
    if not supabase_url or not supabase_key:
        print("❌ Error: Missing SUPABASE_URL or SUPABASE_SERVICE_KEY environment variables")
        sys.exit(1)
    
    return create_client(supabase_url, supabase_key)

def add_email(supabase: Client, email: str, organization_id: str, org_role: str = "admin") -> None:
    """Add an email to the allowlist."""
    try:
        if not organization_id:
            print("❌ Error: Organization ID is required")
            sys.exit(1)

        # Validate org_role
        valid_roles = ["admin", "user", "viewer"]  # Add more roles as needed
        if org_role not in valid_roles:
            print(f"❌ Error: Invalid org_role '{org_role}'. Valid roles are: {', '.join(valid_roles)}")
            sys.exit(1)

        # Check if organization exists
        org_result = supabase.table("organizations").select("id").eq("id", organization_id).execute()

        if not org_result.data:
            print(f"❌ Error: Organization with ID {organization_id} does not exist")
            sys.exit(1)

        data = {
            "email": email,
            "organization_id": organization_id,
            "org_role": org_role,
            "added_by": "python_script"
        }

        result = supabase.table("allowed_emails").insert(data).execute()

        if result.data:
            print(f"✅ Email {email} added to allowlist successfully with role '{org_role}'")
        else:
            print(f"❌ Error adding email: {result.error.message if result.error else 'Unknown error'}")
            sys.exit(1)
    except Exception as e:
        print(f"❌ Error adding email: {str(e)}")
        sys.exit(1)

def remove_email(supabase: Client, email: str) -> None:
    """Remove an email from the allowlist."""
    try:
        # First check if the email exists
        check_result = supabase.table("allowed_emails").select("id").eq("email", email).execute()
        
        if not check_result.data:
            print(f"❌ Error: Email {email} not found in the allowlist")
            sys.exit(1)
            
        result = supabase.table("allowed_emails").delete().eq("email", email).execute()
        
        if result.data:
            print(f"✅ Email {email} removed from allowlist successfully")
        else:
            print(f"❌ Error: Failed to remove email {email} from allowlist")
            sys.exit(1)
    except Exception as e:
        error_msg = str(e)
        if "not found" in error_msg.lower():
            print(f"❌ Error: Email {email} not found in the allowlist")
        else:
            print(f"❌ Error removing email: {error_msg}")
        sys.exit(1)

def delete_user(supabase: Client, email: str) -> None:
    """Completely delete a user from Supabase auth system and the users table."""
    try:
        # We need to use the admin API directly instead of trying to query auth.users
        # First, let's find the user by email
        supabase_url = os.environ.get("SUPABASE_URL")
        service_key = os.environ.get("SUPABASE_SERVICE_KEY")
        
        # Use the list users API endpoint to find the user by email
        list_users_url = f"{supabase_url}/auth/v1/admin/users"
        
        response = httpx.get(
            list_users_url,
            headers={
                "apikey": service_key,
                "Authorization": f"Bearer {service_key}"
            },
            params={"email": email}
        )
        
        if response.status_code != 200:
            print(f"❌ Error fetching users: {response.text}")
            sys.exit(1)
            
        users_data = response.json()
        
        if not users_data or len(users_data.get("users", [])) == 0:
            print(f"❌ User with email {email} not found")
            sys.exit(1)
            
        # Find the user with the exact matching email
        matching_users = [user for user in users_data.get("users", []) if user.get("email") == email]
        
        if not matching_users:
            print(f"❌ User with email {email} not found")
            sys.exit(1)
            
        user_id = matching_users[0].get("id")
        
        print(f"Found user with ID: {user_id}")
        
        # First, delete the user from the custom users table 
        # This is important to do before deleting from auth
        try:
            users_delete_response = supabase.table("users").delete().eq("id", user_id).execute()
            print(f"✅ User deleted from users table")
        except Exception as e:
            print(f"❌ Error deleting from users table: {str(e)}")
            sys.exit(1)
        
        # Delete the user from auth.users using the admin API
        delete_url = f"{supabase_url}/auth/v1/admin/users/{user_id}"
        
        delete_response = httpx.delete(
            delete_url,
            headers={
                "apikey": service_key,
                "Authorization": f"Bearer {service_key}"
            }
        )
        
        if delete_response.status_code in [200, 204]:
            print(f"✅ User {email} deleted from auth system successfully")
            
            # Also explicitly remove them from the allowed_emails table if they exist there
            try:
                supabase.table("allowed_emails").delete().eq("email", email).execute()
                print("✅ User removed from allowed_emails table")
            except Exception as email_err:
                print(f"⚠️ Warning: Could not remove from allowed_emails: {str(email_err)}")
            
            # For revoking all sessions, use the official endpoint
            sessions_url = f"{supabase_url}/auth/v1/admin/users/{user_id}/logout"
            
            sessions_response = httpx.post(
                sessions_url,
                headers={
                    "apikey": service_key,
                    "Authorization": f"Bearer {service_key}"
                }
            )
            
            # Method 2: Directly delete all sessions with proper content headers
            try:
                direct_sessions_url = f"{supabase_url}/auth/v1/admin/session/bulk"
                
                # For DELETE requests with a body, we need to explicitly set the Content-Type
                bulk_delete = httpx.request(
                    "DELETE",
                    direct_sessions_url,
                    headers={
                        "apikey": service_key,
                        "Authorization": f"Bearer {service_key}",
                        "Content-Type": "application/json"
                    },
                    content=json.dumps({"user_ids": [user_id]})
                )
                
                if bulk_delete.status_code in [200, 204]:
                    print(f"✅ Additional session cleanup completed successfully")
            except Exception as session_err:
                print(f"⚠️ Warning: Additional session cleanup failed: {str(session_err)}")
            
            if sessions_response.status_code == 200:
                print(f"✅ All sessions for user {email} have been terminated")
            else:
                print(f"⚠️ Warning: Session termination returned status: {sessions_response.status_code}")
                
            print(f"✅ User completely deleted and all traces removed")
        else:
            print(f"❌ Error deleting user from auth system: {delete_response.text}")
            sys.exit(1)
    except Exception as e:
        print(f"❌ Error deleting user: {str(e)}")
        sys.exit(1)

def list_emails(supabase: Client) -> None:
    """List all emails in the allowlist."""
    try:
        result = supabase.table("allowed_emails").select("*").execute()

        if result.data:
            print("\nAllowed Emails:")
            print("=" * 120)
            print(f"{'ID':<36} | {'Email':<30} | {'Organization ID':<36} | {'Role':<10} | {'Is Active'}")
            print("-" * 120)

            for item in result.data:
                id_str = str(item.get('id', 'N/A'))
                email_str = str(item.get('email', 'N/A'))
                org_id_str = str(item.get('organization_id', 'N/A'))
                org_role_str = str(item.get('org_role', 'admin'))
                is_active_str = str(item.get('is_active', True))

                print(f"{id_str:<36} | {email_str:<30} | {org_id_str:<36} | {org_role_str:<10} | {is_active_str}")

            print(f"\nTotal: {len(result.data)} emails")
        else:
            print("No emails found in the allowlist")
    except Exception as e:
        print(f"❌ Error listing emails: {str(e)}")
        sys.exit(1)

def main():
    """Main function to parse arguments and execute commands."""
    parser = argparse.ArgumentParser(description="Manage the email allowlist for Supabase authentication")
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")
    
    # Add command
    add_parser = subparsers.add_parser("add", help="Add an email to the allowlist")
    add_parser.add_argument("email", help="Email address to add")
    add_parser.add_argument("--org", "-o", dest="organization_id", required=True, help="Organization ID (required)")
    add_parser.add_argument("--role", "-r", dest="org_role", default="admin",
                           help="Organization role for the user (default: admin). Valid roles: admin, user, viewer")

    # Remove command
    remove_parser = subparsers.add_parser("remove", help="Remove an email from the allowlist")
    remove_parser.add_argument("email", help="Email address to remove")
    
    # Delete user command
    delete_parser = subparsers.add_parser("delete-user", help="Completely delete a user from Supabase auth")
    delete_parser.add_argument("email", help="Email address of the user to delete")
    
    # List command
    subparsers.add_parser("list", help="List all emails in the allowlist")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    supabase = setup_supabase()
    
    if args.command == "add":
        add_email(supabase, args.email, args.organization_id, args.org_role)
    elif args.command == "remove":
        remove_email(supabase, args.email)
    elif args.command == "list":
        list_emails(supabase)
    elif args.command == "delete-user":
        delete_user(supabase, args.email)

if __name__ == "__main__":
    main() 