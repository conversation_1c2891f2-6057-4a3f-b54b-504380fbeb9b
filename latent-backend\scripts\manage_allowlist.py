#!/usr/bin/env python3

import os
import sys
import argparse
import httpx
import json
from datetime import datetime, timezone
from dotenv import load_dotenv
from supabase import create_client, Client

# Load environment variables
load_dotenv()

def setup_supabase() -> Client:
    """Initialize and return a Supabase client."""
    supabase_url = os.environ.get("SUPABASE_URL")
    supabase_key = os.environ.get("SUPABASE_SERVICE_KEY")

    if not supabase_url or not supabase_key:
        print("❌ Error: Missing SUPABASE_URL or SUPABASE_SERVICE_KEY environment variables")
        sys.exit(1)

    return create_client(supabase_url, supabase_key)

def parse_expiry_date(date_str: str) -> str:
    """
    Parse a date string and convert to 12:00 AM UTC.

    Supports formats:
    - yyyy-mm-dd (e.g., 2025-07-08)
    - yyyy-mm-dd HH:MM:SS+TZ (e.g., 2025-07-08 15:34:00+00)

    Args:
        date_str: Date string in supported format

    Returns:
        ISO8601 formatted datetime string at 12:00 AM UTC

    Raises:
        ValueError: If date format is invalid
    """
    try:
        # Try to parse full datetime format first: 2025-07-08 15:34:00+00
        if ' ' in date_str and ('+' in date_str or 'Z' in date_str):
            # Extract just the date part (yyyy-mm-dd)
            date_part = date_str.split(' ')[0]
            date_obj = datetime.strptime(date_part, "%Y-%m-%d")
        else:
            # Parse simple date format: yyyy-mm-dd
            date_obj = datetime.strptime(date_str, "%Y-%m-%d")

        # Set time to 12:00 AM (midnight) UTC
        datetime_utc = date_obj.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=timezone.utc)

        # Return ISO8601 formatted string
        return datetime_utc.isoformat()

    except ValueError as e:
        raise ValueError(f"Invalid date format. Expected yyyy-mm-dd or yyyy-mm-dd HH:MM:SS+TZ, got: {date_str}. Error: {str(e)}")

def add_email(supabase: Client, email: str, organization_id: str, org_role: str = "admin", expiry_date: str = None) -> None:
    """Add an email to the allowlist."""
    try:
        if not organization_id:
            print("❌ Error: Organization ID is required")
            sys.exit(1)

        # Validate org_role
        valid_roles = ["admin", "user"]  # Add more roles as needed
        if org_role not in valid_roles:
            print(f"❌ Error: Invalid org_role '{org_role}'. Valid roles are: {', '.join(valid_roles)}")
            sys.exit(1)

        # Check if organization exists
        org_result = supabase.table("organizations").select("id").eq("id", organization_id).execute()
        
        if not org_result.data:
            print(f"❌ Error: Organization with ID {organization_id} does not exist")
            sys.exit(1)
            
        data = {
            "email": email,
            "organization_id": organization_id,
            "org_role": org_role,
            "added_by": "python_script"
        }

        # Handle expiry date conversion
        if expiry_date:
            try:
                # Convert yyyy-mm-dd to 12:00 AM UTC ISO8601 format
                parsed_expiry = parse_expiry_date(expiry_date)
                data["expiry_date"] = parsed_expiry
                print(f"📅 Expiry date set to: {parsed_expiry}")
            except ValueError as e:
                print(f"❌ Error: {str(e)}")
                sys.exit(1)

        result = supabase.table("allowed_emails").insert(data).execute()

        if result.data:
            print(f"✅ Email {email} added to allowlist successfully with role '{org_role}'")
        else:
            print(f"❌ Error adding email: {result.error.message if result.error else 'Unknown error'}")
            sys.exit(1)
    except Exception as e:
        print(f"❌ Error adding email: {str(e)}")
        sys.exit(1)

def remove_email(supabase: Client, email: str) -> None:
    """Remove an email from the allowlist."""
    try:
        # First check if the email exists
        check_result = supabase.table("allowed_emails").select("id").eq("email", email).execute()
        
        if not check_result.data:
            print(f"❌ Error: Email {email} not found in the allowlist")
            sys.exit(1)
            
        result = supabase.table("allowed_emails").delete().eq("email", email).execute()
        
        if result.data:
            print(f"✅ Email {email} removed from allowlist successfully")
        else:
            print(f"❌ Error: Failed to remove email {email} from allowlist")
            sys.exit(1)
    except Exception as e:
        error_msg = str(e)
        if "not found" in error_msg.lower():
            print(f"❌ Error: Email {email} not found in the allowlist")
        else:
            print(f"❌ Error removing email: {error_msg}")
        sys.exit(1)

def delete_user(supabase: Client, email: str) -> None:
    """Completely delete a user from Supabase auth system and the users table."""
    try:
        # We need to use the admin API directly instead of trying to query auth.users
        # First, let's find the user by email
        supabase_url = os.environ.get("SUPABASE_URL")
        service_key = os.environ.get("SUPABASE_SERVICE_KEY")
        
        # Use the list users API endpoint to find the user by email
        list_users_url = f"{supabase_url}/auth/v1/admin/users"
        
        response = httpx.get(
            list_users_url,
            headers={
                "apikey": service_key,
                "Authorization": f"Bearer {service_key}"
            },
            params={"email": email}
        )
        
        if response.status_code != 200:
            print(f"❌ Error fetching users: {response.text}")
            sys.exit(1)
            
        users_data = response.json()
        
        if not users_data or len(users_data.get("users", [])) == 0:
            print(f"❌ User with email {email} not found")
            sys.exit(1)
            
        # Find the user with the exact matching email
        matching_users = [user for user in users_data.get("users", []) if user.get("email") == email]
        
        if not matching_users:
            print(f"❌ User with email {email} not found")
            sys.exit(1)
            
        user_id = matching_users[0].get("id")
        
        print(f"Found user with ID: {user_id}")
        
        # First, delete the user from the custom users table 
        # This is important to do before deleting from auth
        try:
            users_delete_response = supabase.table("users").delete().eq("id", user_id).execute()
            print(f"✅ User deleted from users table")
        except Exception as e:
            print(f"❌ Error deleting from users table: {str(e)}")
            sys.exit(1)
        
        # Delete the user from auth.users using the admin API
        delete_url = f"{supabase_url}/auth/v1/admin/users/{user_id}"
        
        delete_response = httpx.delete(
            delete_url,
            headers={
                "apikey": service_key,
                "Authorization": f"Bearer {service_key}"
            }
        )
        
        if delete_response.status_code in [200, 204]:
            print(f"✅ User {email} deleted from auth system successfully")
            
            # Also explicitly remove them from the allowed_emails table if they exist there
            try:
                supabase.table("allowed_emails").delete().eq("email", email).execute()
                print("✅ User removed from allowed_emails table")
            except Exception as email_err:
                print(f"⚠️ Warning: Could not remove from allowed_emails: {str(email_err)}")
            
            # For revoking all sessions, use the official endpoint
            sessions_url = f"{supabase_url}/auth/v1/admin/users/{user_id}/logout"
            
            sessions_response = httpx.post(
                sessions_url,
                headers={
                    "apikey": service_key,
                    "Authorization": f"Bearer {service_key}"
                }
            )
            
            # Method 2: Directly delete all sessions with proper content headers
            try:
                direct_sessions_url = f"{supabase_url}/auth/v1/admin/session/bulk"
                
                # For DELETE requests with a body, we need to explicitly set the Content-Type
                bulk_delete = httpx.request(
                    "DELETE",
                    direct_sessions_url,
                    headers={
                        "apikey": service_key,
                        "Authorization": f"Bearer {service_key}",
                        "Content-Type": "application/json"
                    },
                    content=json.dumps({"user_ids": [user_id]})
                )
                
                if bulk_delete.status_code in [200, 204]:
                    print(f"✅ Additional session cleanup completed successfully")
            except Exception as session_err:
                print(f"⚠️ Warning: Additional session cleanup failed: {str(session_err)}")
            
            if sessions_response.status_code == 200:
                print(f"✅ All sessions for user {email} have been terminated")
            else:
                print(f"⚠️ Warning: Session termination returned status: {sessions_response.status_code}")
                
            print(f"✅ User completely deleted and all traces removed")
        else:
            print(f"❌ Error deleting user from auth system: {delete_response.text}")
            sys.exit(1)
    except Exception as e:
        print(f"❌ Error deleting user: {str(e)}")
        sys.exit(1)

def list_emails(supabase: Client) -> None:
    """List all emails in the allowlist."""
    try:
        result = supabase.table("allowed_emails").select("*").execute()

        if result.data:
            print("\nAllowed Emails:")
            print("=" * 150)
            print(f"{'ID':<36} | {'Email':<25} | {'Organization ID':<36} | {'Role':<8} | {'Active':<8} | {'Expiry Date'}")
            print("-" * 150)

            for item in result.data:
                id_str = str(item.get('id', 'N/A'))
                email_str = str(item.get('email', 'N/A'))
                org_id_str = str(item.get('organization_id', 'N/A'))
                org_role_str = str(item.get('org_role', 'admin'))
                is_active_str = str(item.get('is_active', True))

                # Format expiry date for display
                expiry_date = item.get('expiry_date')
                if expiry_date:
                    try:
                        # Parse the ISO8601 date and format as yyyy-mm-dd
                        expiry_dt = datetime.fromisoformat(expiry_date.replace('Z', '+00:00'))
                        expiry_str = expiry_dt.strftime('%Y-%m-%d')
                    except:
                        expiry_str = str(expiry_date)
                else:
                    expiry_str = 'No expiry'

                print(f"{id_str:<36} | {email_str:<25} | {org_id_str:<36} | {org_role_str:<8} | {is_active_str:<8} | {expiry_str}")

            print(f"\nTotal: {len(result.data)} emails")
        else:
            print("No emails found in the allowlist")
    except Exception as e:
        print(f"❌ Error listing emails: {str(e)}")
        sys.exit(1)

def main():
    """Main function to parse arguments and execute commands."""
    parser = argparse.ArgumentParser(description="Manage the email allowlist for Supabase authentication")
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")
    
    # Add command
    add_parser = subparsers.add_parser("add", help="Add an email to the allowlist")
    add_parser.add_argument("email", help="Email address to add")
    add_parser.add_argument("--org", "-o", dest="organization_id", required=True, help="Organization ID (required)")
    add_parser.add_argument("--role", "-r", dest="org_role", default="admin",
                           help="Organization role for the user (default: admin). Valid roles: admin, user")
    add_parser.add_argument("--expiry", dest="expiry_date", default=None,
                           help="Expiry date in yyyy-mm-dd format (will be set to 12:00 AM UTC). Example: 2025-07-08")

    # Remove command
    remove_parser = subparsers.add_parser("remove", help="Remove an email from the allowlist")
    remove_parser.add_argument("email", help="Email address to remove")
    
    # Delete user command
    delete_parser = subparsers.add_parser("delete-user", help="Completely delete a user from Supabase auth")
    delete_parser.add_argument("email", help="Email address of the user to delete")
    
    # List command
    subparsers.add_parser("list", help="List all emails in the allowlist")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    supabase = setup_supabase()
    
    if args.command == "add":
        add_email(supabase, args.email, args.organization_id, args.org_role, args.expiry_date)
    elif args.command == "remove":
        remove_email(supabase, args.email)
    elif args.command == "list":
        list_emails(supabase)
    elif args.command == "delete-user":
        delete_user(supabase, args.email)

if __name__ == "__main__":
    main() 