# ──────────────────────────────────────────────────────────────
#  Latent backend – single publish-profile workflow
#  (deploys existing Azure Web Apps for dev, demo, main)
# ──────────────────────────────────────────────────────────────
name: Build & Deploy Python app to Azure Web App – Latent backend

on:
  push:
    branches: [dev, demo, main]     # promotion branches
  workflow_dispatch:

jobs:
# ───────────────────────────── 1. BUILD ─────────────────────────────
  build:
    runs-on: ubuntu-latest
    # Give the job access to the Environment-level secrets/vars
    environment: ${{ github.ref_name }}     # dev | demo | main

    env:
      SUPABASE_URL:                     ${{ secrets.SUPABASE_URL }}
      SUPABASE_PUBLIC_KEY:              ${{ secrets.SUPABASE_PUBLIC_KEY }}
      SUPABASE_SERVICE_ROLE_KEY:        ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
      SUPABASE_JWT_SECRET:              ${{ secrets.SUPABASE_JWT_SECRET }}
      AZURE_STORAGE_CONNECTION_STRING:  ${{ secrets.AZURE_STORAGE_CONNECTION_STRING }}
      AZURE_STORAGE_CONTAINER_NAME:     ${{ secrets.AZURE_STORAGE_CONTAINER_NAME }}
      MAX_FILE_SIZE_BYTES:              ${{ secrets.MAX_FILE_SIZE_BYTES }}
      FRONTEND_URL:                     ${{ secrets.FRONTEND_URL }}

    permissions:
      contents: read   # for actions/checkout

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Create & activate venv
        run: |
          python -m venv venv
          source venv/bin/activate

      - name: Install dependencies
        run: pip install -r requirements.txt

      # ── optional: add test step here ──

      - name: Zip artefact
        run: zip -r release.zip .

      - name: Upload artefact
        uses: actions/upload-artifact@v4
        with:
          name: python-app
          path: |
            release.zip
            !venv/

# ───────────────────────────── 2. DEPLOY ─────────────────────────────
  deploy:
    runs-on: ubuntu-latest
    needs: build
    environment:
      name: ${{ github.ref_name }}          # dev | demo | main
      url:  ${{ steps.deploy.outputs.webapp-url }}

    steps:
      - name: Download artefact
        uses: actions/download-artifact@v4
        with:
          name: python-app

      - name: Unzip artefact
        run: unzip release.zip

      - name: Azure login
        uses: azure/login@v2
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      - name: Update app settings
        uses: Azure/appservice-settings@v1
        with:
          app-name:  ${{ vars.AZURE_APP_NAME }}
          slot-name: Production
          mask-inputs: true
          app-settings-json: |
            [
              { "name": "SUPABASE_URL",                   "value": "${{ secrets.SUPABASE_URL }}" },
              { "name": "SUPABASE_PUBLIC_KEY",            "value": "${{ secrets.SUPABASE_PUBLIC_KEY }}" },
              { "name": "SUPABASE_SERVICE_ROLE_KEY",      "value": "${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}" },
              { "name": "SUPABASE_JWT_SECRET",            "value": "${{ secrets.SUPABASE_JWT_SECRET }}" },
              { "name": "AZURE_STORAGE_CONNECTION_STRING","value": "${{ secrets.AZURE_STORAGE_CONNECTION_STRING }}" },
              { "name": "AZURE_STORAGE_CONTAINER_NAME",   "value": "${{ secrets.AZURE_STORAGE_CONTAINER_NAME }}" },
              { "name": "MAX_FILE_SIZE_BYTES",            "value": "${{ secrets.MAX_FILE_SIZE_BYTES }}" },
              { "name": "FRONTEND_URL",                   "value": "${{ secrets.FRONTEND_URL }}" }
            ]

      - name: Deploy to Azure Web App
        id: deploy
        uses: azure/webapps-deploy@v3
        with:
          app-name:         ${{ vars.AZURE_APP_NAME }}        # latent-backend-dev/demo/prod
          slot-name:        Production
          publish-profile:  ${{ secrets.AZURE_PUBLISH_PROFILE }}
          package:          .
