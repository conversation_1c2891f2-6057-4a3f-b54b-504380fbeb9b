from fastapi import APIRouter, Depends, HTTPException, Request
from typing import Dict, Any, List, Optional
import os
import httpx
import uuid
import asyncio
from app.services.auth_utils import verify_token
from app.services.error_logger import log_user_request, log_interaction

router = APIRouter()

SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_PUBLIC_KEY = os.getenv("SUPABASE_PUBLIC_KEY")

def get_headers(payload: Dict[Any, Any]):
    return {
        "apikey": SUPABASE_PUBLIC_KEY,
        "Authorization": f"Bearer {payload.get('access_token', '')}",
        "Content-Type": "application/json",
        "Prefer": "return=representation"
    }

@router.get('/')
async def list_regulations(request: Request, payload: Dict[Any, Any] = Depends(verify_token)):
    """
    List all regulations from the regulations table.
    RLS will ensure all users can see all regulations.
    """
    # Create request_id for logging
    request_id = str(uuid.uuid4())
    user_id = payload.get("sub")
    user_email = payload.get("email", "")
    ip_address = request.client.host
    endpoint = str(request.url)
    method = request.method
    request_params = {"user_id": user_id}

    # Log the user request
    await log_user_request(
        user_id, user_email, ip_address, endpoint, method, request_params, request_id=request_id
    )

    # Log start of function
    asyncio.create_task(log_interaction(request_id, "list_regulations_start", "Starting list_regulations", params=request_params))

    try:
        asyncio.create_task(log_interaction(request_id, "fetch_all_regulations_attempt", "Fetching all regulations from database"))

        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{SUPABASE_URL}/rest/v1/regulations",
                headers=get_headers(payload),
                params={"select": "*"}
            )

        if response.status_code != 200:
            asyncio.create_task(log_interaction(request_id, "fetch_all_regulations_failure", "Failed to fetch regulations",
                                              status="error", response={"status_code": response.status_code, "error": response.text}))
            raise HTTPException(status_code=response.status_code, detail="We couldn't retrieve the regulations. Please try again later.")

        regulations_data = response.json()
        asyncio.create_task(log_interaction(request_id, "list_regulations_success", "Successfully retrieved all regulations",
                                          response={"regulations_count": len(regulations_data)}))

        return regulations_data
    except HTTPException as http_error:
        asyncio.create_task(log_interaction(request_id, "list_regulations_http_error", "HTTPException in list_regulations",
                                          status="error", response={"detail": str(http_error.detail)}))
        raise http_error
    except Exception as e:
        asyncio.create_task(log_interaction(request_id, "list_regulations_exception", "Unexpected exception in list_regulations",
                                          status="error", response={"error": str(e)}))
        raise HTTPException(status_code=500, detail="An unexpected error occurred while retrieving regulations. Please try again later.")

@router.get('/organization')
async def list_organization_regulations(request: Request, payload: Dict[Any, Any] = Depends(verify_token)):
    """
    List all regulations currently enabled for the user's organization.
    RLS will ensure users only see regulations for their organization.
    """
    # Create request_id for logging
    request_id = str(uuid.uuid4())
    user_id = payload.get("sub")
    user_email = payload.get("email", "")
    ip_address = request.client.host
    endpoint = str(request.url)
    method = request.method
    request_params = {"user_id": user_id}

    # Log the user request
    await log_user_request(
        user_id, user_email, ip_address, endpoint, method, request_params, request_id=request_id
    )

    # Log start of function
    asyncio.create_task(log_interaction(request_id, "list_org_regulations_start", "Starting list_organization_regulations", params=request_params))

    try:
        asyncio.create_task(log_interaction(request_id, "fetch_org_regulations_attempt", "Fetching organization regulations"))

        async with httpx.AsyncClient() as client:
            # First get the organization regulations
            response = await client.get(
                f"{SUPABASE_URL}/rest/v1/organization_regulations",
                headers=get_headers(payload),
                params={
                    "select": "*",
                    "is_applicable": "eq.true"
                }
            )
        if response.status_code != 200:
            asyncio.create_task(log_interaction(request_id, "fetch_org_regulations_failure", "Failed to fetch organization regulations",
                                              status="error", response={"status_code": response.status_code, "error": response.text}))
            raise HTTPException(status_code=response.status_code, detail="We couldn't retrieve the enabled regulations for your organization. Please try again later.")

        org_regulations = response.json()
        asyncio.create_task(log_interaction(request_id, "fetch_org_regulations_success", "Successfully fetched organization regulations",
                                          response={"org_regulations_count": len(org_regulations)}))

        # Get detailed info for each regulation
        regulation_ids = [reg["regulation_id"] for reg in org_regulations]
        if not regulation_ids:
            asyncio.create_task(log_interaction(request_id, "no_org_regulations_found", "No enabled regulations found for organization"))
            return []
        
        # Create a comma-separated list of UUIDs in string format
        regulation_ids_str = ",".join([f'"{reg_id}"' for reg_id in regulation_ids])

        asyncio.create_task(log_interaction(request_id, "fetch_regulation_details_attempt", f"Fetching regulation details for {len(regulation_ids)} regulations",
                                          params={"regulation_ids_count": len(regulation_ids)}))

        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{SUPABASE_URL}/rest/v1/regulations",
                headers=get_headers(payload),
                params={
                    "select": "*",
                    "id": f"in.({regulation_ids_str})"
                }
            )

        if response.status_code != 200:
            asyncio.create_task(log_interaction(request_id, "fetch_regulation_details_failure", "Failed to fetch regulation details",
                                              status="error", response={"status_code": response.status_code, "error": response.text}))
            raise HTTPException(status_code=response.status_code, detail="We couldn't retrieve the regulation details. Please try again later.")

        regulations = response.json()
        asyncio.create_task(log_interaction(request_id, "fetch_regulation_details_success", "Successfully fetched regulation details",
                                          response={"regulations_count": len(regulations)}))

        # Combine data
        asyncio.create_task(log_interaction(request_id, "combine_regulation_data_start", "Combining organization regulations with details"))
        result = []
        for org_reg in org_regulations:
            reg_details = next((r for r in regulations if r["id"] == org_reg["regulation_id"]), {})
            result.append({
                **org_reg,
                "regulation_details": reg_details
            })

        asyncio.create_task(log_interaction(request_id, "list_org_regulations_success", "Successfully completed list_organization_regulations",
                                          response={"combined_results_count": len(result)}))

        return result
    except HTTPException as http_error:
        asyncio.create_task(log_interaction(request_id, "list_org_regulations_http_error", "HTTPException in list_organization_regulations",
                                          status="error", response={"detail": str(http_error.detail)}))
        raise http_error
    except Exception as e:
        asyncio.create_task(log_interaction(request_id, "list_org_regulations_exception", "Unexpected exception in list_organization_regulations",
                                          status="error", response={"error": str(e)}))
        raise HTTPException(status_code=500, detail="An unexpected error occurred while retrieving organization regulations. Please try again later.")

@router.get('/by_id/{regulation_id}')
async def get_regulation(
    request: Request,
    regulation_id: str,
    payload: Dict[Any, Any] = Depends(verify_token)
):
    """
    Get a single regulation by ID.
    RLS will ensure users can only access regulations they have permission for.
    """
    # Create request_id for logging
    request_id = str(uuid.uuid4())
    user_id = payload.get("sub")
    user_email = payload.get("email", "")
    ip_address = request.client.host
    endpoint = str(request.url)
    method = request.method
    request_params = {"regulation_id": regulation_id, "user_id": user_id}

    # Log the user request
    await log_user_request(
        user_id, user_email, ip_address, endpoint, method, request_params, request_id=request_id
    )

    # Log start of function
    asyncio.create_task(log_interaction(request_id, "get_regulation_start", f"Starting get_regulation for regulation: {regulation_id}", params=request_params))

    try:
        asyncio.create_task(log_interaction(request_id, "fetch_regulation_attempt", f"Fetching regulation by ID: {regulation_id}", params={"regulation_id": regulation_id}))

        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{SUPABASE_URL}/rest/v1/regulations",
                headers=get_headers(payload),
                params={
                    "select": "*",
                    "id": f"eq.{regulation_id}"
                }
            )

        if response.status_code != 200:
            asyncio.create_task(log_interaction(request_id, "fetch_regulation_failure", f"Failed to fetch regulation: {regulation_id}",
                                              status="error", response={"status_code": response.status_code, "error": response.text}))
            raise HTTPException(status_code=response.status_code,
                              detail=f"Failed to fetch regulation: {response.text}")

        regulations = response.json()

        if not regulations:
            asyncio.create_task(log_interaction(request_id, "regulation_not_found", f"Regulation not found: {regulation_id}",
                                              status="error", params={"regulation_id": regulation_id}))
            raise HTTPException(status_code=404, detail="The requested regulation could not be found. It may have been deleted or does not exist.")

        asyncio.create_task(log_interaction(request_id, "get_regulation_success", f"Successfully retrieved regulation: {regulation_id}",
                                          response={"regulation_id": regulation_id, "regulation_title": regulations[0].get("title", "N/A")}))

        return regulations[0]
    except HTTPException as e:
        asyncio.create_task(log_interaction(request_id, "get_regulation_http_error", f"HTTPException in get_regulation: {regulation_id}",
                                          status="error", response={"detail": str(e.detail)}))
        raise e
    except Exception as e:
        asyncio.create_task(log_interaction(request_id, "get_regulation_exception", f"Unexpected exception in get_regulation: {regulation_id}",
                                          status="error", response={"error": str(e)}))
        raise HTTPException(status_code=500,
                          detail=f"Error retrieving regulation: {str(e)}")

@router.post('/organization/{regulation_id}')
async def enable_regulation(
    request: Request,
    regulation_id: str,
    payload: Dict[Any, Any] = Depends(verify_token)
):
    """
    Enable a regulation for the user's organization.
    RLS will ensure users can only update for their organization.
    """
    # Create request_id for logging
    request_id = str(uuid.uuid4())
    user_id = payload.get("sub")
    user_email = payload.get("email", "")
    ip_address = request.client.host
    endpoint = str(request.url)
    method = request.method
    request_params = {"regulation_id": regulation_id, "user_id": user_id}

    # Log the user request
    await log_user_request(
        user_id, user_email, ip_address, endpoint, method, request_params, request_id=request_id
    )

    # Log start of function
    asyncio.create_task(log_interaction(request_id, "enable_regulation_start", f"Starting enable_regulation for regulation: {regulation_id}", params=request_params))

    try:
        # First get the user's organization_id
        asyncio.create_task(log_interaction(request_id, "fetch_user_organization_attempt", f"Fetching user organization: {user_id}", params={"user_id": user_id}))

        async with httpx.AsyncClient() as client:
            user_response = await client.get(
                f"{SUPABASE_URL}/rest/v1/users",
                headers=get_headers(payload),
                params={
                    "id": f"eq.{payload.get('sub')}",
                    "select": "organization_id"
                }
            )

        if user_response.status_code != 200 or not user_response.json():
            asyncio.create_task(log_interaction(request_id, "fetch_user_organization_failure", f"Failed to fetch user organization: {user_id}",
                                              status="error", response={"status_code": user_response.status_code, "error": user_response.text}))
            raise HTTPException(status_code=404, detail="We couldn't find your user or organization. Please check your account or contact support.")

        organization_id = user_response.json()[0]["organization_id"]
        asyncio.create_task(log_interaction(request_id, "fetch_user_organization_success", f"Successfully fetched user organization: {user_id}",
                                          response={"organization_id": organization_id}))
        
        # Check if the regulation exists
        asyncio.create_task(log_interaction(request_id, "verify_regulation_exists_attempt", f"Verifying regulation exists: {regulation_id}", params={"regulation_id": regulation_id}))

        async with httpx.AsyncClient() as client:
            reg_response = await client.get(
                f"{SUPABASE_URL}/rest/v1/regulations",
                headers=get_headers(payload),
                params={
                    "id": f"eq.{regulation_id}",
                    "select": "id"
                }
            )

        if reg_response.status_code != 200 or not reg_response.json():
            asyncio.create_task(log_interaction(request_id, "regulation_not_found_for_enable", f"Regulation not found for enabling: {regulation_id}",
                                              status="error", params={"regulation_id": regulation_id}))
            raise HTTPException(status_code=404, detail="Regulation not found")

        asyncio.create_task(log_interaction(request_id, "verify_regulation_exists_success", f"Regulation verified for enabling: {regulation_id}"))

        # Check if the organization_regulation already exists
        asyncio.create_task(log_interaction(request_id, "check_org_regulation_exists_attempt", f"Checking if organization regulation exists",
                                          params={"organization_id": organization_id, "regulation_id": regulation_id}))

        async with httpx.AsyncClient() as client:
            check_response = await client.get(
                f"{SUPABASE_URL}/rest/v1/organization_regulations",
                headers=get_headers(payload),
                params={
                    "organization_id": f"eq.{organization_id}",
                    "regulation_id": f"eq.{regulation_id}",
                    "select": "id"
                }
            )
        
        # If it exists, update it
        if check_response.status_code == 200 and check_response.json():
            org_reg_id = check_response.json()[0]["id"]
            asyncio.create_task(log_interaction(request_id, "update_org_regulation_attempt", f"Updating existing organization regulation: {regulation_id}",
                                              params={"org_reg_id": org_reg_id, "regulation_id": regulation_id}))

            async with httpx.AsyncClient() as client:
                response = await client.patch(
                    f"{SUPABASE_URL}/rest/v1/organization_regulations",
                    headers=get_headers(payload),
                    params={"id": f"eq.{org_reg_id}"},
                    json={
                        "is_applicable": True,
                        "updated_at": "now()"
                    }
                )
        # Otherwise insert a new record
        else:
            asyncio.create_task(log_interaction(request_id, "create_org_regulation_attempt", f"Creating new organization regulation: {regulation_id}",
                                              params={"organization_id": organization_id, "regulation_id": regulation_id}))

            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{SUPABASE_URL}/rest/v1/organization_regulations",
                    headers=get_headers(payload),
                    json={
                        "organization_id": organization_id,
                        "regulation_id": regulation_id,
                        "is_applicable": True
                    }
                )

        if response.status_code not in (200, 201):
            asyncio.create_task(log_interaction(request_id, "enable_regulation_failure", f"Failed to enable regulation: {regulation_id}",
                                              status="error", response={"status_code": response.status_code, "error": response.text}))
            raise HTTPException(status_code=response.status_code,
                               detail=f"Failed to enable regulation: {response.text}")

        asyncio.create_task(log_interaction(request_id, "enable_regulation_success", f"Successfully enabled regulation: {regulation_id}",
                                          response={"regulation_id": regulation_id, "organization_id": organization_id}))

        return response.json()
    except HTTPException as e:
        asyncio.create_task(log_interaction(request_id, "enable_regulation_http_error", f"HTTPException in enable_regulation: {regulation_id}",
                                          status="error", response={"detail": str(e.detail)}))
        raise e
    except Exception as e:
        asyncio.create_task(log_interaction(request_id, "enable_regulation_exception", f"Unexpected exception in enable_regulation: {regulation_id}",
                                          status="error", response={"error": str(e)}))
        raise HTTPException(status_code=500,
                           detail=f"Error enabling regulation: {str(e)}")

@router.delete('/organization/{regulation_id}')
async def disable_regulation(
    request: Request,
    regulation_id: str,
    payload: Dict[Any, Any] = Depends(verify_token)
):
    """
    Disable a regulation for the user's organization.
    RLS will ensure users can only update for their organization.
    """
    # Create request_id for logging
    request_id = str(uuid.uuid4())
    user_id = payload.get("sub")
    user_email = payload.get("email", "")
    ip_address = request.client.host
    endpoint = str(request.url)
    method = request.method
    request_params = {"regulation_id": regulation_id, "user_id": user_id}

    # Log the user request
    await log_user_request(
        user_id, user_email, ip_address, endpoint, method, request_params, request_id=request_id
    )

    # Log start of function
    asyncio.create_task(log_interaction(request_id, "disable_regulation_start", f"Starting disable_regulation for regulation: {regulation_id}", params=request_params))

    try:
        # First get the user's organization_id
        asyncio.create_task(log_interaction(request_id, "fetch_user_org_for_disable_attempt", f"Fetching user organization for disable: {user_id}", params={"user_id": user_id}))

        async with httpx.AsyncClient() as client:
            user_response = await client.get(
                f"{SUPABASE_URL}/rest/v1/users",
                headers=get_headers(payload),
                params={
                    "id": f"eq.{payload.get('sub')}",
                    "select": "organization_id"
                }
            )

        if user_response.status_code != 200 or not user_response.json():
            asyncio.create_task(log_interaction(request_id, "fetch_user_org_for_disable_failure", f"Failed to fetch user organization for disable: {user_id}",
                                              status="error", response={"status_code": user_response.status_code, "error": user_response.text}))
            raise HTTPException(status_code=404, detail="We couldn't find your user or organization. Please check your account or contact support.")

        organization_id = user_response.json()[0]["organization_id"]
        asyncio.create_task(log_interaction(request_id, "fetch_user_org_for_disable_success", f"Successfully fetched user organization for disable: {user_id}",
                                          response={"organization_id": organization_id}))
        
        # Check if the organization_regulation exists
        asyncio.create_task(log_interaction(request_id, "check_org_regulation_for_disable_attempt", f"Checking if organization regulation exists for disable",
                                          params={"organization_id": organization_id, "regulation_id": regulation_id}))

        async with httpx.AsyncClient() as client:
            check_response = await client.get(
                f"{SUPABASE_URL}/rest/v1/organization_regulations",
                headers=get_headers(payload),
                params={
                    "organization_id": f"eq.{organization_id}",
                    "regulation_id": f"eq.{regulation_id}",
                    "select": "id"
                }
            )

        # If it doesn't exist, return success
        if check_response.status_code != 200 or not check_response.json():
            asyncio.create_task(log_interaction(request_id, "regulation_already_disabled", f"Regulation already disabled for organization: {regulation_id}",
                                              params={"regulation_id": regulation_id, "organization_id": organization_id}))
            return {"message": "Regulation already disabled for organization"}

        # Update the record to set is_applicable = false
        org_reg_id = check_response.json()[0]["id"]
        asyncio.create_task(log_interaction(request_id, "disable_org_regulation_attempt", f"Disabling organization regulation: {regulation_id}",
                                          params={"org_reg_id": org_reg_id, "regulation_id": regulation_id}))

        async with httpx.AsyncClient() as client:
            response = await client.patch(
                f"{SUPABASE_URL}/rest/v1/organization_regulations",
                headers=get_headers(payload),
                params={"id": f"eq.{org_reg_id}"},
                json={
                    "is_applicable": False,
                    "updated_at": "now()"
                }
            )

        if response.status_code != 200:
            asyncio.create_task(log_interaction(request_id, "disable_regulation_failure", f"Failed to disable regulation: {regulation_id}",
                                              status="error", response={"status_code": response.status_code, "error": response.text}))
            raise HTTPException(status_code=response.status_code,
                              detail=f"Failed to disable regulation: {response.text}")

        asyncio.create_task(log_interaction(request_id, "disable_regulation_success", f"Successfully disabled regulation: {regulation_id}",
                                          response={"regulation_id": regulation_id, "organization_id": organization_id}))

        return response.json()
    except HTTPException as e:
        asyncio.create_task(log_interaction(request_id, "disable_regulation_http_error", f"HTTPException in disable_regulation: {regulation_id}",
                                          status="error", response={"detail": str(e.detail)}))
        raise e
    except Exception as e:
        asyncio.create_task(log_interaction(request_id, "disable_regulation_exception", f"Unexpected exception in disable_regulation: {regulation_id}",
                                          status="error", response={"error": str(e)}))
        raise HTTPException(status_code=500,
                          detail=f"Error disabling regulation: {str(e)}")
