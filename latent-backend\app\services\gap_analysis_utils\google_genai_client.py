from google import genai
from google.genai import types


class GoogleGenAIClient:
    def __init__(self, config):
        """Initialize Google GenAI client with config

        Args:
            config (Config): Configuration object containing API key
        """
        self.api_key = config.get("google_ai_studio.api_key")
        self.client = genai.Client(api_key=self.api_key)

    def generate_content(self, prompt, model="gemini-2.0-flash-thinking-exp", temperature=0):
        """Generate content using specified model

        Args:
            prompt (str): Input prompt for generation
            model (str): Model name to use, defaults to gemini-2.0-flash

        Returns:
            Response from the model
        """
        try:
            response = self.client.models.generate_content(
                model=model,
                contents=prompt,
                config=types.GenerateContentConfig(temperature=temperature),
            )
            return response
        except Exception:
            raise Exception("A temporary issue occurred while analyzing the SOP.")


if __name__ == "__main__":
    from app.services.gap_analysis_utils.config import Config
    config = Config("app/services/gap_analysis_utils/user_config.yaml")
    client = GoogleGenAIClient(config)
    response = client.generate_content(
        "Explain how AI works", model="gemini-2.0-flash-thinking-exp"
    )
    print(response.text)
