import React, { useState } from 'react';

import supabase from '../../../supabase';
import './Login.css';

const Login = () => {
  const [activeTab, setActiveTab] = useState('login');
  const [formData, setFormData] = useState({
    loginEmail: '',
    loginPassword: '',
    signupEmail: '',
    signupPassword: '',
    signupPasswordConfirm: '',
    firstName: '',
    lastName: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [message, setMessage] = useState('');
  // const navigate = useNavigate();

  const handleInputChange = (e) => {
    const { id, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [id]: value
    }));
  };

  const handleLogin = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setMessage('');

    // Basic validation
    if (!formData.loginEmail || !formData.loginPassword) {
      setError('Please fill in all fields');
      setLoading(false);
      return;
    }

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: formData.loginEmail,
        password: formData.loginPassword,
      });

      if (error) {
        if (error.message.includes('Invalid login credentials')) {
          setError('Invalid email or password');
        } else {
          setError(error.message);
        }
        return;
      }

      // If login successful, navigate to dashboard
      if (data?.user) {
        window.location.href = '/dashboard'; // This will force a full page reload
        // OR use this if you want to keep React state:
        // navigate('/dashboard', { replace: true });

      }
    } catch (error) {
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSignup = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setMessage('');

    // Basic validation
    if (!formData.signupEmail || !formData.signupPassword || !formData.signupPasswordConfirm || 
        !formData.firstName || !formData.lastName) {
      setError('Please fill in all fields');
      setLoading(false);
      return;
    }

    // Password confirmation check
    if (formData.signupPassword !== formData.signupPasswordConfirm) {
      setError('Passwords do not match');
      setLoading(false);
      return;
    }

    try {
      // Normalize the email
      const normalizedEmail = formData.signupEmail.trim().toLowerCase();

      console.log("Attempting signup for:", normalizedEmail);

      // Attempt signup
      const { data, error: signupError } = await supabase.auth.signUp({
        email: normalizedEmail,
        password: formData.signupPassword,
        options: {
          data: {
            first_name: formData.firstName.trim(),
            last_name: formData.lastName.trim()
          }
        }
      });

      console.log("Signup response data:", data);
      console.log("Signup error:", signupError);

      // Check for errors
      if (signupError) {
        console.error("Signup error details:", signupError);
        
        if (signupError.message.includes('Database error saving new user')) {
          setError(`you are not allowed to login contact your administrator`);
        } else if (signupError.message.includes('User already registered')) {
          setError('User already signed up, please login');
        } else {
          setError(signupError.message || 'Error during signup');
        }
        return;
      }

      // Check if user was created
      if (data?.user) {
        // console.log("User created successfully:", data.user);
        
        // Check if email confirmation is required
        if (data.user.identities && data.user.identities.length === 0) {
          setError('This email is already registered. Please login instead.');
          return;
        }
        
        setMessage('Check your email for the confirmation link.');
        // Clear the form
        setFormData(prev => ({
          ...prev,
          signupEmail: '',
          signupPassword: '',
          signupPasswordConfirm: '',
          firstName: '',
          lastName: ''
        }));
      } else {
        // No user data but also no error - unusual case
        console.warn("No user data returned but no error either");
        setError('Something went wrong with registration. Please try again.');
      }
    } catch (error) {
      console.error("Unexpected error during signup:", error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container">
      <div className="logo-container">
        <img 
          src="/zipplogo.png" 
          alt="Zipp Logo" 
          className="login-logo"
        />
      </div>
      
      <div id="auth-container">
        <div className="tab-container">
          <button 
            className={`tab-button ${activeTab === 'login' ? 'active' : ''}`}
            onClick={() => setActiveTab('login')}
          >
            Login
          </button>
          <button 
            className={`tab-button ${activeTab === 'signup' ? 'active' : ''}`}
            onClick={() => setActiveTab('signup')}
          >
            Sign Up
          </button>
        </div>
        
        <div id="login" className={`tab-content ${activeTab === 'login' ? 'active' : ''}`}>
          <h2>Login</h2>
          <form onSubmit={handleLogin}>
            <div className="form-group">
              <label htmlFor="loginEmail">Email</label>
              <input
                type="email"
                id="loginEmail"
                value={formData.loginEmail}
                onChange={handleInputChange}
                required
              />
            </div>
            <div className="form-group">
              <label htmlFor="loginPassword">Password</label>
              <input
                type="password"
                id="loginPassword"
                value={formData.loginPassword}
                onChange={handleInputChange}
                required
              />
            </div>
            <button type="submit" className="btn" disabled={loading}>
              {loading ? 'Logging in...' : 'Login'}
            </button>
          </form>
        </div>

        <div id="signup" className={`tab-content ${activeTab === 'signup' ? 'active' : ''}`}>
          <h2>Sign Up</h2>
          <form onSubmit={handleSignup}>
            <div className="form-group">
              <label htmlFor="firstName">First Name</label>
              <input
                type="text"
                id="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
                required
              />
            </div>
            <div className="form-group">
              <label htmlFor="lastName">Last Name</label>
              <input
                type="text"
                id="lastName"
                value={formData.lastName}
                onChange={handleInputChange}
                required
              />
          </div>
          <div className="form-group">
              <label htmlFor="signupEmail">Email</label>
            <input
              type="email"
                id="signupEmail"
                value={formData.signupEmail}
                onChange={handleInputChange}
                required
              />
            </div>
            <div className="form-group">
              <label htmlFor="signupPassword">Password</label>
              <input
                type="password"
                id="signupPassword"
                value={formData.signupPassword}
                onChange={handleInputChange}
                required
            />
          </div>
          <div className="form-group">
              <label htmlFor="signupPasswordConfirm">Confirm Password</label>
            <input
              type="password"
                id="signupPasswordConfirm"
                value={formData.signupPasswordConfirm}
                onChange={handleInputChange}
                required
            />
          </div>
            <button type="submit" className="btn" disabled={loading}>
              {loading ? 'Signing up...' : 'Sign Up'}
          </button>
          </form>
          <p className="info-text">
            Only allowed emails can sign up. Please check if your email is on the allowlist.
          </p>
          </div>
      </div>
      
      {error && <div className="message error">{error}</div>}
      {message && <div className="message success">{message}</div>}
    </div>
  );
};

export default Login; 