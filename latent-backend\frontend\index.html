<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase Auth Demo</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <h1>Supabase Authentication</h1>
        
        <div id="auth-container">
            <div class="tab-container">
                <button class="tab-button active" data-tab="login">Login</button>
                <button class="tab-button" data-tab="signup">Sign Up</button>
            </div>
            
            <div id="login" class="tab-content active">
                <h2>Login</h2>
                <form id="login-form">
                    <div class="form-group">
                        <label for="login-email">Email</label>
                        <input type="email" id="login-email" required>
                    </div>
                    <div class="form-group">
                        <label for="login-password">Password</label>
                        <input type="password" id="login-password" required>
                    </div>
                    <button type="submit" class="btn">Login</button>
                </form>
            </div>
            
            <div id="signup" class="tab-content">
                <h2>Sign Up</h2>
                <form id="signup-form">
                    <div class="form-group">
                        <label for="signup-email">Email</label>
                        <input type="email" id="signup-email" required>
                    </div>
                    <div class="form-group">
                        <label for="signup-password">Password</label>
                        <input type="password" id="signup-password" required minlength="6">
                    </div>
                    <button type="submit" class="btn">Sign Up</button>
                </form>
                <p class="info-text">Only allowed emails can sign up. Please check if your email is on the allowlist.</p>
            </div>
        </div>
        
        <div id="user-container" style="display: none;">
            <h2>Welcome <span id="user-email"></span>!</h2>
            <p>You've successfully authenticated with Supabase.</p>
            <button id="logout-button" class="btn">Logout</button>
            <button id="delete-account-button" class="btn btn-danger">Delete Account</button>
        </div>
        
        <div id="message" class="message"></div>
    </div>

    <!-- Delete Account Confirmation Modal -->
    <div id="delete-account-modal" class="modal">
        <div class="modal-content">
            <h3>Delete Account</h3>
            <p>Are you sure you want to delete your account? This action cannot be undone.</p>
            <div class="modal-buttons">
                <button id="confirm-delete" class="btn btn-danger">Yes, Delete My Account</button>
                <button id="cancel-delete" class="btn">Cancel</button>
            </div>
        </div>
    </div>

    <!-- Load Supabase JS library -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/dist/umd/supabase.min.js"></script>
    <script src="app.js"></script>
</body>
</html> 