import re
import os
from json_repair import repair_json
import subprocess
import json
import logging

# Initialize the logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
handler = logging.StreamHandler()
formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
handler.setFormatter(formatter)
logger.addHandler(handler)


def repair_json_js(json_string):
    logger.info("Trying the JS json repair")
    current_dir = os.path.dirname(os.path.realpath(__file__))
    script_path = os.path.join(current_dir, "jsonrepair.js")

    result = subprocess.run(
        ["node", script_path, json_string], capture_output=True, text=True
    )

    if result.stderr:
        logger.error("Errors: %s", result.stderr)

    logger.info("End the JS json repair")
    return result.stdout


def format_json_blocks_correctly(json_string):
    # Official JSON guidelines require us to use double quotes instead of single quotes
    corrected_string = json_string.replace("'", '"')

    return corrected_string


def extract_json_v2(text):
    def find_outermost_boundaries(text):
        stack = []
        boundaries = []
        for i, char in enumerate(text):
            if char in "{[":
                if not stack:
                    boundaries.append((i, None))  # Start of new outermost JSON
                stack.append(char)
            elif char in "}]":
                if stack:
                    open_char = "{" if char == "}" else "["
                    if stack[-1] == open_char:
                        stack.pop()
                        if not stack:  # Closing of an outermost JSON
                            start, _ = boundaries[-1]
                            boundaries[-1] = (start, i + 1)
        return boundaries

    # First, replace problematic escape sequences
    text = text.replace("\\x", "\\u00")

    boundaries = find_outermost_boundaries(text)
    for start, end in boundaries:
        segment = text[start:end].strip()
        try:
            # First try to load JSON normally
            if segment.startswith("{") and segment.endswith("}"):
                json_object = json.loads(format_json_blocks_correctly(segment))
                if isinstance(json_object, dict):
                    return json_object
            elif segment.startswith("[") and segment.endswith("]"):
                json_array = json.loads(format_json_blocks_correctly(segment))
                if all(isinstance(item, dict) for item in json_array):
                    return json_array
        except json.JSONDecodeError:
            # If normal loading fails, try to repair and load again
            try:
                repaired_segment = repair_json_js(segment)
                json_object = json.loads(repaired_segment)
                if isinstance(json_object, (dict, list)):
                    return json_object
            except json.JSONDecodeError:
                continue

    raise ValueError("Not able to parse JSON")


def extract_json(text):
    def find_json_boundaries(text, open_char, close_char):
        stack = []
        start = -1
        for i, char in enumerate(text):
            if char == open_char:
                stack.append(i)
                if start == -1:
                    start = i
            elif char == close_char and stack:
                stack.pop()
                if not stack:
                    return start, i + 1
        return None

    array_boundaries = find_json_boundaries(text, "[", "]")
    if array_boundaries:
        try:
            return json.loads(
                repair_json_js(text[array_boundaries[0] : array_boundaries[1]])
            )
        except json.JSONDecodeError:
            raise ValueError("Not able to parse Array boundaries")

    object_boundaries = find_json_boundaries(text, "{", "}")
    if object_boundaries:
        try:
            return json.loads(
                repair_json_js(text[object_boundaries[0] : object_boundaries[1]])
            )
        except json.JSONDecodeError:
            raise ValueError("Not able to parse Object boundaries")

    raise ValueError("Not able to parse JSON")


def validate_json_schema(extracted_json, sample_output):
    if isinstance(sample_output, list):
        if not isinstance(extracted_json, list):
            raise ValueError("Expected an array but did not get one")
        for item in extracted_json:
            if not isinstance(item, dict):
                raise ValueError("Array elements must be dict objects")
    elif isinstance(sample_output, dict):
        if not isinstance(extracted_json, dict):
            raise ValueError("Expected a dict object but did not get one")
    else:
        raise ValueError("Sample output must be either a list or a dict")


def sanitize_and_parse_json_string(json_string, sample_output=None):
    try:
        return json.loads(
            json_string.split("<json_output>")[1].split("</json_output>")[0]
        )
    except:
        try:
            if isinstance(json_string, dict):
                json_string = json.dumps(json_string)
            sanitized_string = json_string.replace("\n", " ").replace("\t", " ")
            if "```json" in sanitized_string:
                start_tag = "```json"
                end_tag = "```"

                start_index = sanitized_string.index(start_tag) + len(start_tag)
                end_index = sanitized_string.index(end_tag, start_index)

                # Extract and parse the JSON content
                json_text = sanitized_string[start_index:end_index].strip()
                return json.loads(json_text)
            match = re.search(
                r"<json_output>(.*?)</json_output>", sanitized_string, re.DOTALL
            )
            if match:
                content_within_tags = match.group(1)
            else:
                content_within_tags = sanitized_string

            parsed_json = extract_json_v2(content_within_tags)
            if isinstance(parsed_json, dict):
                for k, v in parsed_json.items():
                    try:
                        parsed_json[k] = json.loads(parsed_json[v])
                    except Exception as e:
                        pass
            if sample_output is not None:
                validate_json_schema(parsed_json, sample_output)

            return parsed_json

        except json.JSONDecodeError as e:
            print(f"Failed to parse: {json_string}")
            raise e


import unittest


class TestPostProcessingUtils(unittest.TestCase):

    # def test_sanitize_and_parse_json_string_with_valid_json(self):
    #     json_string = '{"key": "value"}'
    #     sample_output = {"key": "value"}
    #     result = sanitize_and_parse_json_string(json_string, sample_output)
    #     self.assertEqual(result, {"key": "value"})

    # def test_sanitize_and_parse_json_string_with_invalid_json(self):
    #     json_string = '{"key": "value"'
    #     sample_output = {"key": "value"}
    #     with self.assertRaises(json.JSONDecodeError):
    #         sanitize_and_parse_json_string(json_string, sample_output)

    def test_sanitize_and_parse_json_string_with_embedded_json(self):
        json_string = '<json_output>{"key": "value"}</json_output>'
        sample_output = {"key": "value"}
        result = sanitize_and_parse_json_string(json_string, sample_output)
        self.assertEqual(result, {"key": "value"})


if __name__ == "__main__":
    unittest.main()
