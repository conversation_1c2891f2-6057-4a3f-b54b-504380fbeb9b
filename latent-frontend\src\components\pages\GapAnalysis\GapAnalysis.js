import React, { useState, useEffect } from 'react';
import Navigation from '../../common/Navigation/Navigation';
import DepartmentFilters from '../../common/DepartmentFilters/DepartmentFilters';
import Modal from '../../common/Modal/Modal';
import AssessmentForm from './AssessmentForm';
import GapDetailsSidebar from './GapDetailsSidebar';
import sopService from '../../../services/sopService';
import apiService from '../../../services/api';
import API_URLS from '../../../config/apiUrls';
import LoadingSpinner from '../../common/LoadingSpinner/LoadingSpinner';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import './GapAnalysis.css';
import { toast } from 'react-toastify';
import { sanitizeText } from '../../../utils/sanitize';

// Helper function to format date for display
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return 'Invalid date';
  
  // Format: "May 24 at 9:29 PM"
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }) + ' at ' + date.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });
};

const GapAnalysis = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [selectedRowIndex, setSelectedRowIndex] = useState(null);
  const [selectedDepartment, setSelectedDepartment] = useState('All');
  const [gapResults, setGapResults] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [analysisResults, setAnalysisResults] = useState(null);
  const [analysisLoading, setAnalysisLoading] = useState(false);
  const [analysisError, setAnalysisError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [processingSopId, setProcessingSopId] = useState(null);
  
  // Add sorting and filtering states
  const [sortField, setSortField] = useState('date');
  const [sortDirection, setSortDirection] = useState('desc');
  const [dateFilter, setDateFilter] = useState('all');

  // const stats = [
  //   {
  //     title: 'Compliant SOPs',
  //     value: '76%',
  //     description: '38 of 50 SOPs meet requirements',
  //     action: 'View Details',
  //     disabled: true
  //   },
  //   {
  //     title: 'Non-Compliant',
  //     value: '24%',
  //     description: '12 SOPs require attention',
  //     action: 'View Issues',
  //     disabled: true
  //   },
  //   {
  //     title: 'Critical Gaps',
  //     value: '5',
  //     description: 'High-priority items needing immediate action',
  //     action: 'Resolve Now',
  //     disabled: true
  //   },
  //   {
  //     title: 'Last Assessment',
  //     value: 'June 12, 2023',
  //     description: 'Next scheduled review in 14 days',
  //     action: 'Schedule Review',
  //     disabled: true
  //   }
  // ];

  // Modify the useEffect that fetches SOPs to also fetch analysis results
  useEffect(() => {
    const fetchSOPs = async () => {
      try {
        setLoading(true);
        const sops = await sopService.getAllSOPs();
        
        // Transform SOPs into gap analysis results
        const transformedResults = sops.sops_data.map(sop => {
          const department = sop.metadata?.department || 'Unassigned';
          
          return {
            id: sop.id,
            title: sop.title || 'Unnamed SOP',
            department: department,
            date: sop.metadata?.date || sop.created_at || new Date().toISOString(),
            // Extract gap metrics directly from SOP data
            totalGaps: sop.total_gaps,
            highPriorityGaps: sop.high_priority_gaps,
            mediumPriorityGaps: sop.medium_priority_gaps,
            lowPriorityGaps: sop.low_priority_gaps,
          };
        });
        
        setGapResults(transformedResults);
        
        // After fetching SOPs, fetch analysis results for all SOPs
        if (transformedResults.length > 0) {
          fetchAnalysisResults(transformedResults.map(sop => sop.id));
        }
      } catch (err) {
        console.error('Error fetching SOPs:', err);
        setError('Failed to load gap analysis results. Please try again later.');
      } finally {
        setTimeout(() => {
          setLoading(false);
          
        }, 1000);
      }
    };

    fetchSOPs();
  }, []);

  // Update the fetchAnalysisResults function to properly check for changed_gap_details
  const fetchAnalysisResults = async (sopIds) => {
    try {
      setAnalysisLoading(true);
      setAnalysisError(null);
      
      // Call the /results endpoint with the SOP IDs as payload
      const response = await apiService.post(API_URLS.ANALYSIS.RESULTS, sopIds);
      
      // Store the analysis results in state
      if (response && response.results) {
        // Map the results to the corresponding SOPs
        const resultsMap = {};
        response.results.forEach(result => {
          if (result.sop_id) {
            // Determine which gap details to use - prefer changed_gap_detail if available
            let gapDetailsToUse = [];
            
            if (result.metadata && result.metadata.changed_gap_detail && 
                Array.isArray(result.metadata.changed_gap_detail) && 
                result.metadata.changed_gap_detail.length > 0) {
              // Use the validated/changed gap details if available
              gapDetailsToUse = result.metadata.changed_gap_detail;
              console.log('Using changed_gap_detail for SOP:', result.sop_id);
            } else if (result.gap_details) {
              // Fall back to original gap details
              gapDetailsToUse = result.gap_details;
              console.log('Using original gap_details for SOP:', result.sop_id);
            }
            
            // Ensure gap_details is always an array
            if (!Array.isArray(gapDetailsToUse)) {
              gapDetailsToUse = [];
            }
            
            // Use direct values from API response instead of client-side calculations
            console.log(`SOP ${result.sop_id}: Analysis data available`);
            
            // Store the result with gap details only (metrics come from SOP data)
            resultsMap[result.sop_id] = {
              ...result,
              // Store the gap details we're using for consistency
              gap_details: gapDetailsToUse
            };
          }
        });
        
        // Update the gap results with analysis data
        setGapResults(prevResults => 
          prevResults.map(sop => ({
            ...sop,
            analysis: resultsMap[sop.id] || { gap_details: [] }
          }))
        );
        
        console.log('Updated gap results with analysis data');
      }
    } catch (err) {
      console.error('Error fetching analysis results:', err);
      setAnalysisError('Failed to load analysis results. Please try again later.');
    } finally {
      setAnalysisLoading(false);
    }
  };

  // Add a silent update function that doesn't show loading states
  const silentUpdateAnalysisData = async (sopId) => {
    try {
      console.log('Silently updating analysis data for SOP:', sopId);
      
      // Fetch updated analysis results for the specific SOP
      const response = await apiService.post(API_URLS.ANALYSIS.RESULTS, [sopId]);
      
      if (response && response.results && response.results.length > 0) {
        const result = response.results[0];
        
        if (result.sop_id === sopId) {
          // Determine which gap details to use
          let gapDetailsToUse = [];
          
          if (result.metadata && result.metadata.changed_gap_detail && 
              Array.isArray(result.metadata.changed_gap_detail) && 
              result.metadata.changed_gap_detail.length > 0) {
            gapDetailsToUse = result.metadata.changed_gap_detail;
          } else if (result.gap_details) {
            gapDetailsToUse = result.gap_details;
          }
          
          if (!Array.isArray(gapDetailsToUse)) {
            gapDetailsToUse = [];
          }
          
          // Add a small delay to ensure backend has processed the changes
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          // Also fetch updated SOP data to get latest gap metrics
          console.log('Fetching updated SOP data for ID:', sopId);
          const sops = await sopService.getAllSOPs();
          const updatedSop = sops.find(sop => sop.id === sopId);
          
          console.log('Updated SOP data:', updatedSop);
          console.log('Gap metrics - Total:', updatedSop?.total_gaps, 'High:', updatedSop?.high_priority_gaps, 'Medium:', updatedSop?.medium_priority_gaps, 'Low:', updatedSop?.low_priority_gaps);
          
          // Update the specific SOP's analysis data and gap metrics
          setGapResults(prevResults => {
            const updatedResults = prevResults.map(sop => 
              sop.id === sopId 
                ? { 
                    ...sop, 
                    // Update gap metrics from SOP data
                    totalGaps: updatedSop?.total_gaps || sop.totalGaps,
                    highPriorityGaps: updatedSop?.high_priority_gaps || sop.highPriorityGaps,
                    mediumPriorityGaps: updatedSop?.medium_priority_gaps || sop.mediumPriorityGaps,
                    lowPriorityGaps: updatedSop?.low_priority_gaps || sop.lowPriorityGaps,
                    // Update analysis data
                    analysis: {
                      ...result,
                      gap_details: gapDetailsToUse
                    }
                  } 
                : sop
            );
            
            console.log('Updated gap results:', updatedResults);
            return updatedResults;
          });
          
          // Update analysisResults if it's for the same SOP
          setAnalysisResults(prevResults => {
            if (prevResults && prevResults.sop_id === sopId) {
              return {
                ...result,
                gap_details: gapDetailsToUse
              };
            }
            return prevResults;
          });
          
          console.log('Successfully updated analysis data and gap metrics silently for SOP:', sopId);
        }
      }
    } catch (err) {
      console.error('Error silently updating analysis data:', err);
      // Don't show error toast for silent updates to avoid disrupting UX
    }
  };

  // Improve the refreshAllData function to ensure it properly updates the UI
  const refreshAllData = async (silent = false) => {
    try {
      console.log('Refreshing gap analysis data...');
      if (!silent) {
        setLoading(true);
      }
      
      // Fetch SOPs
      const sops = await sopService.getAllSOPs();
      console.log('Fetched SOPs:', sops);
      
      // Transform SOPs into gap analysis results
      const transformedResults = sops.map(sop => {
        const department = sop.metadata?.department || 'Unassigned';
        
        return {
          id: sop.id,
          title: sop.title || 'Unnamed SOP',
          department: department,
          date: sop.metadata?.date || sop.created_at || new Date().toISOString(),
          // Extract gap metrics directly from SOP data
          totalGaps: sop.total_gaps,
          highPriorityGaps: sop.high_priority_gaps,
          mediumPriorityGaps: sop.medium_priority_gaps,
          lowPriorityGaps: sop.low_priority_gaps,
        };
      });
      
      setGapResults(transformedResults);
      console.log('Updated gap results:', transformedResults);
      
      // Fetch analysis results for all SOPs
      if (transformedResults.length > 0) {
        const sopIds = transformedResults.map(sop => sop.id);
        console.log('Fetching analysis results for SOPs:', sopIds);
        await fetchAnalysisResults(sopIds);
      }
      
      setError(null);
      console.log('Gap analysis data refresh complete');
    } catch (err) {
      console.error('Error refreshing gap analysis data:', err);
      setError('Failed to load gap analysis results. Please try again later.');
    } finally {
      if (!silent) {
        setLoading(false);
      }
    }
  };

  // Filter results based on selected department
  const filteredResultsMemo = React.useMemo(() => {
    // Helper function to get the highest date from available dates (shared between filtering and sorting)
    const getHighestDate = (item) => {
      const dates = [];
      
      // Add analysis dates if they exist
      if (item.analysis && item.analysis.analyzed_at) {
        dates.push(new Date(item.analysis.analyzed_at));
      }
      if (item.analysis && item.analysis.updated_at) {
        dates.push(new Date(item.analysis.updated_at));
      }
      
      // Add SOP creation date
      if (item.date) {
        dates.push(new Date(item.date));
      }
      
      // Return the highest date, or epoch if no valid dates
      return dates.length > 0 ? new Date(Math.max(...dates)) : new Date(0);
    };

    // Start with department filtering
    let filtered = gapResults;
    
    if (selectedDepartment !== 'All') {
      filtered = filtered.filter(item => item.department === selectedDepartment);
    }
    
    // Apply search term filter
    if (searchTerm.trim() !== '') {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(item => 
        (item.title || '').toLowerCase().includes(term) ||
        (item.department || '').toLowerCase().includes(term)
      );
    }
    
    // Apply date filter
    if (dateFilter !== 'all') {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      const lastWeek = new Date(today);
      lastWeek.setDate(lastWeek.getDate() - 7);
      const lastMonth = new Date(today);
      lastMonth.setMonth(lastMonth.getMonth() - 1);
      
      filtered = filtered.filter(item => {
        // Use the highest date among all available dates
        const dateToUse = getHighestDate(item);
        
        switch (dateFilter) {
          case 'today':
            return dateToUse >= today;
          case 'yesterday':
            return dateToUse >= yesterday && dateToUse < today;
          case 'week':
            return dateToUse >= lastWeek;
          case 'month':
            return dateToUse >= lastMonth;
          default:
            return true;
        }
      });
    }
    
    // Sort the filtered results
    return filtered.sort((a, b) => {
      if (sortField === 'date') {
        const dateA = getHighestDate(a);
        const dateB = getHighestDate(b);
        
        return sortDirection === 'asc' ? dateA - dateB : dateB - dateA;
      } else if (sortField === 'title') {
        const titleA = (a.title || '').toLowerCase();
        const titleB = (b.title || '').toLowerCase();
        
        return sortDirection === 'asc' 
          ? titleA.localeCompare(titleB)
          : titleB.localeCompare(titleA);
      } else if (sortField === 'priority') {
        // First, check if items have analysis data (analyzed vs not analyzed)
        const aHasAnalysis = a.analysis && a.analysis.analyzed_at;
        const bHasAnalysis = b.analysis && b.analysis.analyzed_at;
        
        // If one has analysis and the other doesn't, prioritize the analyzed one
        if (aHasAnalysis && !bHasAnalysis) {
          return -1; // a comes first (analyzed items on top)
        } else if (!aHasAnalysis && bHasAnalysis) {
          return 1; // b comes first (analyzed items on top)
        }
        
        // Helper function to determine priority level of an item
        const getPriorityLevel = (item) => {
          if ((item.highPriorityGaps || 0) > 0) return 'high';
          if ((item.mediumPriorityGaps || 0) > 0) return 'medium';
          if ((item.lowPriorityGaps || 0) > 0) return 'low';
          return 'none'; // No gaps
        };
        
        const priorityA = getPriorityLevel(a);
        const priorityB = getPriorityLevel(b);
        
        // Define priority order: high > medium > low > none
        const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1, 'none': 0 };
        const valueA = priorityOrder[priorityA];
        const valueB = priorityOrder[priorityB];
        
        // Sort: desc = high to low (3,2,1,0), asc = low to high (0,1,2,3)
        return sortDirection === 'asc' ? valueA - valueB : valueB - valueA;
      }
      return 0;
    });
  }, [gapResults, selectedDepartment, searchTerm, sortField, sortDirection, dateFilter]);

  const handleDepartmentChange = (department) => {
    setSelectedDepartment(department);
    setSelectedRowIndex(null); // Clear selected row when changing departments
    setIsSidebarOpen(false); // Close sidebar when changing departments
  };

  const handleViewDetails = (index) => {
    setSelectedRowIndex(index);
    
    const selectedSop = filteredResultsMemo[index];
    
    // Check if we already have analysis data for this SOP
    if (selectedSop && selectedSop.analysis) {
      // Use the existing analysis data
      setAnalysisResults(selectedSop.analysis);
      setIsSidebarOpen(true);
    } else {
      // Only make API call if we don't have the data already
      setAnalysisLoading(true);
      setAnalysisError(null);
      
      apiService.post(API_URLS.ANALYSIS.ANALYZE, { sop_id: selectedSop.id })
        .then(response => {
          setAnalysisResults(response);
          
          // Also update the cached results for this SOP
          setGapResults(prevResults => 
            prevResults.map(sop => 
              sop.id === selectedSop.id 
                ? { ...sop, analysis: response } 
                : sop
            )
          );
        })
        .catch(err => {
          console.error('Error analyzing SOP:', err);
          setAnalysisError('Failed to analyze SOP. Please try again later.');
        })
        .finally(() => {
          setAnalysisLoading(false);
          setIsSidebarOpen(true);
        });
    }
  };

  const renderCreateAssessmentButton = () => {
    return (
      <button 
        className="create-assessment-btn"
        onClick={() => setIsModalOpen(true)}
      >
        Create Assessment
      </button>
    )
  }

  // Function to handle sort changes
  const handleSortChange = (field) => {
    if (sortField === field) {
      // Toggle direction if clicking the same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new field and default to descending for date, ascending for title
      setSortField(field);
      setSortDirection(field === 'date' ? 'desc' : 'asc');
    }
  };

  // Add a new function to reset all filters
  const resetFilters = () => {
    setSearchTerm('');
    setDateFilter('all');
    setSortField('date');
    setSortDirection('desc');
    // Keep the department filter as is, since it's a primary filter
  };


  // Update the callback functions to track processing state with logging
  const handleBeforeUpload = () => {
    console.log('Setting isUploading to true');
    setIsUploading(true);
  };

  const handleAfterUpload = () => {
    console.log('Setting isUploading to false');
    setIsUploading(false);
  };

  // Add functions to handle assessment processing
  const handleAssessmentStart = (sopId) => {
    console.log('Starting assessment processing for SOP:', sopId);
    setProcessingSopId(sopId);
    setIsModalOpen(false); // Close the modal when processing starts
  };

  const handleAssessmentComplete = () => {
    console.log('Assessment processing complete');
    setProcessingSopId(null);
    refreshAllData(true); // Refresh data silently to show new assessment
    handleAfterUpload();
  };

  return (
    <div className="gap-analysis">
      <Navigation />
      <div className="gap-content">
        <div className="page-header">
          <h2>Gap Analysis</h2>
        </div>

        <DepartmentFilters 
          onDepartmentChange={handleDepartmentChange}
          defaultSelected="All"
        />

       
          <>
            <div className="search-section">
              <input 
                type="text" 
                placeholder="Search gap analysis..." 
                className="search-input"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              {renderCreateAssessmentButton()}
            </div>
            
            <div className="filter-sort-controls">
              <div className="filter-group">
                <label>Sort by:</label>
                <div className="sort-buttons">
                  <button 
                    className={`sort-btn ${sortField === 'date' ? 'active' : ''}`}
                    onClick={() => handleSortChange('date')}
                  >
                    Date
                    {sortField === 'date' && (
                      <span className="sort-direction">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </button>
                  <button 
                    className={`sort-btn ${sortField === 'title' ? 'active' : ''}`}
                    onClick={() => handleSortChange('title')}
                  >
                    Title
                    {sortField === 'title' && (
                      <span className="sort-direction">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </button>
                  <button 
                    className={`sort-btn ${sortField === 'priority' ? 'active' : ''}`}
                    onClick={() => handleSortChange('priority')}
                  >
                    Priority
                    {sortField === 'priority' && (
                      <span className="sort-direction">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </button>
                </div>
              </div>
              
              <div className="filter-group">
                <label>Date:</label>
                <select 
                  value={dateFilter} 
                  onChange={(e) => setDateFilter(e.target.value)}
                  className="filter-select"
                >
                  <option value="all">All Time</option>
                  <option value="today">Today</option>
                  <option value="yesterday">Yesterday</option>
                  <option value="week">Last 7 Days</option>
                  <option value="month">Last 30 Days</option>
                </select>
              </div>
              
              <button 
                className="reset-filters-btn"
                onClick={resetFilters}
                disabled={dateFilter === 'all' && sortField === 'date' && sortDirection === 'desc' && searchTerm === ''}
              >
                Reset Filters
              </button>
            </div>
          </>
       

        {loading ? (
          <div className="loading-container">
            <LoadingSpinner size="large" />
            <span className="loading-text">Loading Gap Analysis Results...</span>
          </div>
        ) : (
          <>
            <div className="gap-results">
              <h2>Gap Analysis Results</h2>
              {error ? (
                <div className="error-message">{error}</div>
              ) : filteredResultsMemo.length === 0 ? (
                <div className="empty-state">
                  {searchTerm || dateFilter !== 'all' ? 
                    `No results found matching your filters. Try different filter settings.` : 
                    <>
                      No gap analysis results found. Create your first assessment by clicking {renderCreateAssessmentButton()}
                    </>
                  }
                </div>
              ) : (
                <div className="gap-items">
                  {filteredResultsMemo.map((item, index) => (
                    <div 
                      key={item.id || index} 
                      className={`gap-item ${selectedRowIndex === index ? 'selected' : ''}`}
                    >
                      <div className="gap-info">
                        <span className={`analysis-date ${!item.analysis || !item.analysis.analyzed_at ? 'no-analysis' : ''}`}>
                         
                          {item.analysis && item.analysis.analyzed_at 
                            ? (item.analysis.analyzed_at === item.analysis.updated_at 
                                ? `Analyzed on ${formatDate(item.analysis.analyzed_at)}` 
                                : `Updated on ${formatDate(item.analysis.updated_at)}`)
                            : `Created on ${formatDate(item.date)}`
                            
                          }
                        </span>
                        <h3>{sanitizeText(item.title)}</h3>
                        <div className="gap-details">
                          <span className="department">{sanitizeText(item.department)}</span>
                        </div>
                      </div>
                      {processingSopId !== item.id && (
                        <div className="gap-metrics-container">
                          <>
                            {item.totalGaps !== undefined && item.totalGaps !== null && (
                              <span className="gap-metric total-gaps">
                                <span className="metric-label">Total Gaps:</span> 
                                <span className="metric-value">{item.totalGaps}</span>
                              </span>
                            )}
                            
                            {item.highPriorityGaps !== undefined && item.highPriorityGaps !== null && item.highPriorityGaps > 0 ? (
                              <span className="gap-metric high-priority">
                                <span className="metric-label">High Priority:</span> 
                                <span className="metric-value">{item.highPriorityGaps}</span>
                              </span>
                            ) : item.mediumPriorityGaps !== undefined && item.mediumPriorityGaps !== null && item.mediumPriorityGaps > 0 ? (
                              <span className="gap-metric medium-priority">
                                <span className="metric-label">Medium Priority:</span> 
                                <span className="metric-value">{item.mediumPriorityGaps}</span>
                              </span>
                            ) : item.lowPriorityGaps !== undefined && item.lowPriorityGaps !== null && item.lowPriorityGaps > 0 ? (
                              <span className="gap-metric low-priority">
                                <span className="metric-label">Low Priority:</span> 
                                <span className="metric-value">{item.lowPriorityGaps}</span>
                              </span>
                            ) : null}
                            
                          </>
                        </div>
                      )}
                      <div className="gap-meta">
                        <div className="gap-actions">
                          {/* Only show Details button if analysis exists */}
                          {processingSopId === item.id ? (
                            <div className="processing-state">
                              <LoadingSpinner size="small" />
                              <span className="processing-text">Processing</span>
                            </div>
                          ) : item.totalGaps !== undefined && item.totalGaps !== null ? (
                            <button 
                              className="details-btn"
                              onClick={() => handleViewDetails(index)}
                            >
                              Details
                            </button>
                          ) : (
                            <span className="no-details">No Assessment</span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </>
        )}

        <Modal 
          isOpen={isModalOpen} 
          onClose={() => setIsModalOpen(false)}  
          closeOnOutsideClick={true}
          isProcessing={isUploading}
        >
          <AssessmentForm 
            onClose={() => setIsModalOpen(false)}
            onSuccess={handleAssessmentComplete}
            onBeforeUpload={handleBeforeUpload}
            onProcessingStart={handleAssessmentStart}
          />
        </Modal>
         
            <GapDetailsSidebar 
            closeOnOutsideClick={true}  
          isOpen={isSidebarOpen}
          onClose={() => {
            setIsSidebarOpen(false);
            setSelectedRowIndex(null);
            setAnalysisResults(null);
          }}
          gapDetails={selectedRowIndex !== null ? filteredResultsMemo[selectedRowIndex] : null}
          analysisResults={analysisResults}
          loading={analysisLoading}
          error={analysisError}
          onRefreshData={refreshAllData}
          onSilentUpdate={silentUpdateAnalysisData}
        />
            
        
        <ToastContainer 
          position="top-right"
          autoClose={4000}
          hideProgressBar={false}
          newestOnTop
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="light"
        />
      </div>
    </div>
  );
};

export default GapAnalysis; 
 