-- Add max_sop_uploads column to organizations table
-- This migration adds SOP upload limit functionality to organizations

-- Add max_sop_uploads column to organizations table
-- NULL means no limit, any positive integer sets the limit
ALTER TABLE organizations ADD COLUMN max_sop_uploads INTEGER DEFAULT NULL;

-- Create a function to check SOP upload limit for an organization
CREATE OR REPLACE FUNCTION check_sop_upload_limit(org_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    max_limit INTEGER;
    current_count INTEGER;
BEGIN
    -- Get the max_sop_uploads limit for the organization
    SELECT max_sop_uploads INTO max_limit
    FROM organizations
    WHERE id = org_id;
    
    -- If max_limit is NULL, there's no limit
    IF max_limit IS NULL THEN
        RETURN TRUE;
    END IF;
    
    -- Count current non-deleted SOPs for the organization
    SELECT COUNT(*) INTO current_count
    FROM sops
    WHERE organization_id = org_id AND is_deleted = FALSE;
    
    -- Return TRUE if under limit, FALSE if at or over limit
    RETURN current_count < max_limit;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get SOP count and limit info for an organization
CREATE OR REPLACE FUNCTION get_sop_upload_info(org_id UUID)
RETURNS TABLE(
    current_count INTEGER,
    max_limit INTEGER,
    can_upload BOOLEAN,
    remaining_uploads INTEGER
) AS $$
DECLARE
    max_limit_val INTEGER;
    current_count_val INTEGER;
BEGIN
    -- Get the max_sop_uploads limit for the organization
    SELECT max_sop_uploads INTO max_limit_val
    FROM organizations
    WHERE id = org_id;
    
    -- Count current non-deleted SOPs for the organization
    SELECT COUNT(*) INTO current_count_val
    FROM sops
    WHERE organization_id = org_id AND is_deleted = FALSE;
    
    -- Return the information
    RETURN QUERY SELECT 
        current_count_val,
        max_limit_val,
        CASE 
            WHEN max_limit_val IS NULL THEN TRUE
            ELSE current_count_val < max_limit_val
        END,
        CASE 
            WHEN max_limit_val IS NULL THEN NULL
            ELSE GREATEST(0, max_limit_val - current_count_val)
        END;
END;
$$ LANGUAGE plpgsql;
