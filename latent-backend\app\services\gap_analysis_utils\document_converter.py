import fitz  # PyMuPDF
import os
from typing import Union
import docx
from app.services.error_logger import log_interaction_sync

def convert_pdf_to_text(pdf_bytes: bytes) -> str:
    """
    Convert PDF bytes to text, cropping top and bottom 5% of each page.
    
    Args:
        pdf_bytes: Raw PDF file bytes
        
    Returns:
        Extracted text from the PDF
    """
    doc = fitz.open("pdf", pdf_bytes)
    text_data = []

    for page in doc:
        rect = page.rect
        new_rect = fitz.Rect(
            rect.x0,
            rect.y0 + rect.height * 0.05,
            rect.x1,
            rect.y1 - rect.height * 0.05,
        )
        page.set_cropbox(new_rect)
        page_text = page.get_text()
        text_data.append(page_text)

    doc.close()
    return "".join(text_data)

def convert_docx_to_text(docx_bytes: bytes) -> str:
    """
    Convert DOCX bytes to text.
    
    Args:
        docx_bytes: Raw DOCX file bytes
        
    Returns:
        Extracted text from the DOCX
    """
    # Save bytes to a temporary file
    temp_file = "temp_document.docx"
    with open(temp_file, "wb") as f:
        f.write(docx_bytes)
    
    # Extract text from the document
    doc = docx.Document(temp_file)
    text = []
    for para in doc.paragraphs:
        text.append(para.text)
    
    # Clean up temporary file
    os.remove(temp_file)
    
    return "\n".join(text)

def convert_txt_to_text(txt_bytes: bytes) -> str:
    """
    Convert TXT bytes to text.
    
    Args:
        txt_bytes: Raw TXT file bytes
        
    Returns:
        Decoded text content
    """
    return txt_bytes.decode('utf-8', errors='replace')

def get_document_text(file_path: str, request_id=None) -> str:
    """
    Extract text from a document file based on its extension.
    
    Args:
        file_path: Path to the document file
        
    Returns:
        Extracted text content
    """
    try:
        if request_id:
            log_interaction_sync(request_id, "get_document_text_start", "Starting get_document_text", params={"file_path": file_path})
        with open(file_path, "rb") as f:
            file_bytes = f.read()
        file_extension = os.path.splitext(file_path)[1].lower()
        if file_extension == ".pdf":
            result = convert_pdf_to_text(file_bytes)
        elif file_extension == ".docx":
            result = convert_docx_to_text(file_bytes)
        elif file_extension == ".txt":
            result = convert_txt_to_text(file_bytes)
        else:
            raise ValueError(f"Unsupported file format: {file_extension}")
        if request_id:
            log_interaction_sync(request_id, "get_document_text_end", "Finished get_document_text", response={"file_extension": file_extension})
        return result
    except Exception as e:
        if request_id:
            log_interaction_sync(request_id, "get_document_text_exception", "Exception in get_document_text", status="error", response={"error": str(e)})
        raise Exception("A temporary issue occurred while analyzing the SOP.")

if __name__ == "__main__":
    documents_directory = "../documents"
    processed_directory = "../processed_documents"
    os.makedirs(processed_directory, exist_ok=True)

    for filename in os.listdir(documents_directory):
        file_path = os.path.join(documents_directory, filename)
        try:
            extracted_text = get_document_text(file_path)
            
            # Save the extracted text
            txt_filename = os.path.splitext(filename)[0] + ".txt"
            txt_path = os.path.join(processed_directory, txt_filename)
            
            with open(txt_path, "w", encoding="utf-8") as txt_file:
                txt_file.write(extracted_text)
                
            print(f"Processed: {filename}")
        except Exception as e:
            print(f"Error processing {filename}: {e}") 