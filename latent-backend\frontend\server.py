from http.server import HTTPServer, SimpleHTTPRequestHandler
import os
from dotenv import load_dotenv
import json

# Load environment variables
load_dotenv()

class CustomHandler(SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/config':
            # Send environment variables
            config = {
                'supabaseUrl': os.getenv('SUPABASE_URL'),
                'supabaseKey': os.getenv('SUPABASE_PUBLIC_KEY')
            }
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(config).encode())
        else:
            # Serve static files
            return SimpleHTTPRequestHandler.do_GET(self)

def run(server_class=HTTPServer, handler_class=CustomHandler, port=3000):
    server_address = ('', port)
    httpd = server_class(server_address, handler_class)
    print(f"Starting server on port {port}...")
    httpd.serve_forever()

if __name__ == '__main__':
    run() 