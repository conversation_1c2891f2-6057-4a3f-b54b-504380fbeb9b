// Debug mode
const DEBUG = true;
const BACKEND_URL = 'http://localhost:8000'; // Backend API URL

// Debug logger
function debug(message, data) {
    if (!DEBUG) return;
    console.log(`[DEBUG] ${message}`, data || '');
    
    // Add to debug panel if it exists
    const debugPanel = document.getElementById('debug-panel');
    if (debugPanel) {
        const time = new Date().toLocaleTimeString();
        const entry = document.createElement('div');
        entry.className = 'debug-entry';
        
        let displayData = '';
        if (data) {
            try {
                if (typeof data === 'object') {
                    displayData = JSON.stringify(data, null, 2);
                } else {
                    displayData = String(data);
                }
            } catch (e) {
                displayData = '[Error displaying data]';
            }
        }
        
        entry.innerHTML = `<span class="debug-time">${time}</span> <span class="debug-msg">${message}</span>`;
        if (displayData) {
            entry.innerHTML += `<pre class="debug-data">${displayData}</pre>`;
        }
        debugPanel.appendChild(entry);
        debugPanel.scrollTop = debugPanel.scrollHeight;
    }
}

// Create debug panel
document.addEventListener('DOMContentLoaded', async function() {
    if (DEBUG) {
        const panel = document.createElement('div');
        panel.id = 'debug-panel';
        panel.innerHTML = '<h3>Debug Panel <button id="clear-debug">Clear</button></h3><div id="debug-content"></div>';
        document.body.appendChild(panel);
        
        // Add styles
        const style = document.createElement('style');
        style.textContent = `
            #debug-panel {
                position: fixed;
                bottom: 0;
                right: 0;
                width: 400px;
                height: 300px;
                background: rgba(0,0,0,0.8);
                color: #eee;
                z-index: 9999;
                overflow: auto;
                font-family: monospace;
                padding: 10px;
                border-top-left-radius: 5px;
                font-size: 12px;
            }
            #debug-panel h3 {
                margin: 0 0 10px 0;
                color: #fff;
                font-size: 14px;
                display: flex;
                justify-content: space-between;
            }
            #clear-debug {
                background: #444;
                border: none;
                color: #fff;
                padding: 2px 5px;
                cursor: pointer;
            }
            .debug-entry {
                margin-bottom: 5px;
                border-bottom: 1px solid #333;
                padding-bottom: 5px;
            }
            .debug-time {
                color: #aaa;
            }
            .debug-msg {
                color: #4f4;
            }
            .debug-data {
                margin: 5px 0 0 10px;
                color: #ff9;
                white-space: pre-wrap;
                font-size: 11px;
            }
        `;
        document.head.appendChild(style);
        
        // Clear button
        document.getElementById('clear-debug').addEventListener('click', function() {
            document.getElementById('debug-content').innerHTML = '';
        });
    }
    
    try {
        // Load configuration from server - we still need Supabase client for now
        // but will use backend for actual data operations
        debug('Loading configuration from server');
        const response = await fetch('/config');
        const config = await response.json();
        
        if (!config.supabaseUrl || !config.supabaseKey) {
            throw new Error('Missing Supabase configuration');
        }
        
        // Initialize Supabase
        debug('Initializing Supabase client', { url: config.supabaseUrl, keyLength: config.supabaseKey.length });
        window.supabaseClient = supabase.createClient(config.supabaseUrl, config.supabaseKey, {
            auth: {
                persistSession: true,
                autoRefreshToken: true,
                storageKey: 'supabase.auth.token'
            }
        });
        debug('Supabase client initialized');
        
        // Check if backend is available
        try {
            debug('Checking backend availability');
            
            // First try using our helper
            try {
                const healthData = await makeBackendRequest('/');
                debug('Backend health check successful', healthData);
            } catch (err) {
                // If that fails, try a simpler approach as fallback
                debug('First health check attempt failed, trying fallback', err);
                
                const healthResponse = await fetch(`${BACKEND_URL}/`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                    },
                    mode: 'cors',
                    credentials: 'omit' // Don't send credentials for the health check
                });
                
                if (healthResponse.ok) {
                    const healthData = await healthResponse.json();
                    debug('Backend health check successful with fallback', healthData);
                } else {
                    const errorText = await healthResponse.text();
                    debug('Backend health check failed with status', { 
                        status: healthResponse.status, 
                        statusText: healthResponse.statusText,
                        response: errorText
                    });
                    throw new Error(`Backend service returned error: ${healthResponse.status} ${healthResponse.statusText}`);
                }
            }
        } catch (backendError) {
            debug('Backend connection error', {
                error: backendError.toString(),
                message: backendError.message
            });
            
            // Check if it's likely a CORS issue
            if (backendError.message.includes('NetworkError') || 
                backendError.message.includes('Failed to fetch') ||
                backendError.message.includes('CORS')) {
                throw new Error(`CORS issue connecting to backend at ${BACKEND_URL}. Make sure the backend has CORS enabled and is running.`);
            } else {
                throw new Error(`Cannot connect to backend at ${BACKEND_URL}: ${backendError.message}`);
            }
        }
        
        initApp();
    } catch (error) {
        debug('Failed to initialize application', error);
        document.getElementById('message').textContent = 'Error initializing application: ' + error.message;
        document.getElementById('message').className = 'message error';
    }
});

// Helper to get JWT token from auth
async function getAuthToken() {
    const { data } = await window.supabaseClient.auth.getSession();
    return data?.session?.access_token;
}

// Helper to make backend requests with proper headers
async function makeBackendRequest(endpoint, options = {}) {
    const token = await getAuthToken();
    
    const defaultOptions = {
        method: options.method || 'GET',
        headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
            ...options.headers
        },
        mode: 'cors',
        credentials: 'same-origin'
    };
    
    // Add body if provided
    if (options.body) {
        defaultOptions.body = JSON.stringify(options.body);
    }
    
    debug(`Making ${defaultOptions.method} request to ${endpoint}`, { 
        headers: defaultOptions.headers,
        hasBody: !!options.body 
    });
    
    const url = `${BACKEND_URL}${endpoint}`;
    const response = await fetch(url, defaultOptions);
    
    if (!response.ok) {
        const errorText = await response.text();
        debug('Backend request failed', { 
            url,
            status: response.status, 
            statusText: response.statusText,
            response: errorText
        });
        throw new Error(`Backend error (${response.status}): ${response.statusText}`);
    }
    
    return response.json();
}

function initApp() {
    // DOM elements
    const loginForm = document.getElementById('login-form');
    const signupForm = document.getElementById('signup-form');
    const logoutButton = document.getElementById('logout-button');
    const messageElement = document.getElementById('message');
    const userContainer = document.getElementById('user-container');
    const authContainer = document.getElementById('auth-container');
    const userEmailSpan = document.getElementById('user-email');
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    debug('DOM elements initialized', {
        loginForm: !!loginForm,
        signupForm: !!signupForm,
        tabButtons: tabButtons.length,
        tabContents: tabContents.length
    });

    // Tab switching logic
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            debug('Tab clicked', button.getAttribute('data-tab'));
            
            // Remove active class from all tabs
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Add active class to current tab
            button.classList.add('active');
            const tabId = button.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
            
            // Clear message
            messageElement.textContent = '';
            messageElement.className = 'message';
        });
    });

    // Display message function
    function showMessage(text, type) {
        debug('Showing message', { text, type });
        messageElement.textContent = text;
        messageElement.className = `message ${type}`;
    }

    // Check if user is logged in on page load
    async function checkUser() {
        try {
            debug('Checking current user');
            const { data: sessionData, error: sessionError } = await window.supabaseClient.auth.getSession();
            
            if (sessionError) {
                debug('Session error', sessionError);
                throw sessionError;
            }
            
            const { data, error } = await window.supabaseClient.auth.getUser();
            
            debug('User check result', { data, error });
            
            if (data?.user) {
                userEmailSpan.textContent = data.user.email;
                authContainer.style.display = 'none';
                userContainer.style.display = 'block';
                
                debug('Fetching user with ID:', data.user.id);
                
                // Create user info from auth data first
                const userData = {
                    id: data.user.id,
                    email: data.user.email,
                    created_at: data.user.created_at,
                    // Default values will be used if DB query fails
                    first_name: '',
                    last_name: '',
                    org_role: 'member',
                    is_admin: false,
                    last_login: null,
                    organization_id: null
                };
                
                // Display basic user info immediately
                displayUserInfo(userData, data.user.email);
                
                // Try to get additional user data from backend
                try {
                    debug('Fetching user profile from backend');
                    const userDataFromBackend = await makeBackendRequest('/user/profile');
                    
                    debug('User data from backend', userDataFromBackend);
                    
                    if (userDataFromBackend && userDataFromBackend.length > 0) {
                        // Update our user data with complete information
                        Object.assign(userData, userDataFromBackend[0]);
                        
                        // Update user display with complete info
                        displayUserInfo(userData, data.user.email);
                        
                        // Attempt to get organization data if we have an organization ID
                        if (userData.organization_id) {
                            fetchAndDisplayOrganization(userData);
                        } else {
                            displayNoOrganizationMessage();
                        }
                    } else {
                        debug('Backend returned empty user data');
                        showMessage('Unable to fetch complete profile data from backend.', 'warning');
                        displayNoOrganizationMessage();
                    }
                } catch (error) {
                    debug('Error fetching user profile from backend', error);
                    showMessage(`Backend error: ${error.message}`, 'error');
                    displayNoOrganizationMessage();
                }
            } else {
                authContainer.style.display = 'block';
                userContainer.style.display = 'none';
                
                // Clear any user data containers
                const userInfoDiv = document.getElementById('user-info');
                if (userInfoDiv) userInfoDiv.remove();
                
                const orgInfoDiv = document.getElementById('org-info');
                if (orgInfoDiv) orgInfoDiv.remove();
            }
        } catch (error) {
            debug('Error checking user', error);
            authContainer.style.display = 'block';
            userContainer.style.display = 'none';
            showMessage(`Authentication error: ${error.message}`, 'error');
        }
    }
    
    // Helper function to display user information
    function displayUserInfo(userData, email) {
        let userInfoDiv = document.getElementById('user-info');
        if (!userInfoDiv) {
            userInfoDiv = document.createElement('div');
            userInfoDiv.id = 'user-info';
            userContainer.appendChild(userInfoDiv);
        }
        
        userInfoDiv.innerHTML = `
            <h3>Your User Details</h3>
            <div class="info-card">
                <p><strong>ID:</strong> ${userData.id}</p>
                <p><strong>Email:</strong> ${email}</p>
                <p><strong>Name:</strong> ${userData.first_name || ''} ${userData.last_name || ''}</p>
                <p><strong>Role:</strong> ${userData.org_role || 'Not specified'}</p>
                <p><strong>Is Admin:</strong> ${userData.is_admin ? 'Yes' : 'No'}</p>
                <p><strong>Created:</strong> ${userData.created_at ? new Date(userData.created_at).toLocaleString() : 'Not available'}</p>
                <p><strong>Last Login:</strong> ${userData.last_login ? new Date(userData.last_login).toLocaleString() : 'Never'}</p>
            </div>
        `;
        
        // Clear any error messages since we can at least show basic user info
        messageElement.textContent = '';
        messageElement.className = 'message';
    }
    
    // Helper function to fetch and display organization information
    async function fetchAndDisplayOrganization(userData) {
        try {
            // Fetch organization users from backend
            debug('Fetching organization users from backend');
            const orgUsersData = await makeBackendRequest('/user/organization/users');
            
            debug('Organization users from backend', orgUsersData);
            
            // Create or update the organization details display
            let orgInfoDiv = document.getElementById('org-info');
            if (!orgInfoDiv) {
                orgInfoDiv = document.createElement('div');
                orgInfoDiv.id = 'org-info';
                userContainer.appendChild(orgInfoDiv);
            }
            
            // Build HTML for organization users
            let usersHTML = '';
            if (orgUsersData && orgUsersData.length > 0) {
                usersHTML = '<h4>Members</h4><ul>';
                orgUsersData.forEach(user => {
                    usersHTML += `<li>${user.first_name || ''} ${user.last_name || ''} (${user.email}) - ${user.org_role || 'Member'}</li>`;
                });
                usersHTML += '</ul>';
            }
            
            orgInfoDiv.innerHTML = `
                <h3>Your Organization (via Backend)</h3>
                <div class="info-card">
                    <p><strong>Organization ID:</strong> ${userData.organization_id}</p>
                    <p><strong>Your Role:</strong> ${userData.org_role}</p>
                    <p><strong>Total Members:</strong> ${orgUsersData ? orgUsersData.length : 0}</p>
                    ${usersHTML}
                </div>
            `;
        } catch (error) {
            debug('Error fetching organization from backend', error);
            displayNoOrganizationMessage();
            showMessage(`Backend error: ${error.message}`, 'error');
        }
    }
    
    // Helper function to display a message when the user has no organization
    function displayNoOrganizationMessage() {
        debug('User has no organization assigned');
        // Remove org info if it exists
        const orgInfoDiv = document.getElementById('org-info');
        if (orgInfoDiv) {
            orgInfoDiv.remove();
        }
        
        // Show a message that user has no organization, but don't hide user data
        let newOrgInfoDiv = document.createElement('div');
        newOrgInfoDiv.id = 'org-info';
        newOrgInfoDiv.innerHTML = `
            <h3>Organization Status</h3>
            <div class="info-card">
                <p>You are not assigned to any organization.</p>
            </div>
        `;
        userContainer.appendChild(newOrgInfoDiv);
    }

    // Sign up form submission
    signupForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        debug('Signup form submitted');
        
        const email = document.getElementById('signup-email').value;
        const password = document.getElementById('signup-password').value;
        
        debug('Signup credentials', { email, passwordLength: password.length });
        
        try {
            // We still use Supabase client for auth operations
            const { data, error } = await window.supabaseClient.auth.signUp({
                email,
                password,
                options: {
                    emailRedirectTo: `${window.location.origin}/confirm-email.html`
                }
            });
            
            debug('Signup result', { data, error });
            
            if (error) throw error;
            
            showMessage('Registration successful! Please check your email for confirmation.', 'success');
            signupForm.reset();
        } catch (error) {
            debug('Signup error', error);
            // If the error is related to the email not being on allowlist, display specific message
            if (error.message && error.message.includes('Email not authorized')) {
                showMessage('This email is not authorized to sign up. Please contact an administrator.', 'error');
            } else {
                showMessage(`Error: ${error.message || 'Unknown error'}`, 'error');
            }
        }
    });

    // Login form submission
    loginForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        debug('Login form submitted');
        
        const email = document.getElementById('login-email').value;
        const password = document.getElementById('login-password').value;
        
        debug('Login credentials', { email, passwordLength: password.length });
        
        try {
            // We still use Supabase client for auth operations
            const { data, error } = await window.supabaseClient.auth.signInWithPassword({
                email,
                password
            });
            
            debug('Login result', { data, error });
            
            if (error) throw error;
            
            // Update UI for logged in user
            checkUser();
        } catch (error) {
            debug('Login error', error);
            showMessage(`Error: ${error.message || 'Unknown error'}`, 'error');
        }
    });

    // Logout button
    logoutButton.addEventListener('click', async () => {
        debug('Logout button clicked');
        try {
            const { error } = await window.supabaseClient.auth.signOut();
            
            debug('Logout result', { error });
            
            if (error) throw error;
            
            // Update UI for logged out user
            checkUser();
        } catch (error) {
            debug('Logout error', error);
            showMessage(`Error: ${error.message || 'Unknown error'}`, 'error');
        }
    });

    // Delete account button and modal
    const deleteAccountButton = document.getElementById('delete-account-button');
    const deleteAccountModal = document.getElementById('delete-account-modal');
    const confirmDeleteButton = document.getElementById('confirm-delete');
    const cancelDeleteButton = document.getElementById('cancel-delete');
    
    // Open delete confirmation modal
    deleteAccountButton.addEventListener('click', () => {
        debug('Delete account button clicked');
        deleteAccountModal.style.display = 'flex';
    });
    
    // Cancel delete
    cancelDeleteButton.addEventListener('click', () => {
        debug('Delete account cancelled');
        deleteAccountModal.style.display = 'none';
    });
    
    // Confirm delete
    confirmDeleteButton.addEventListener('click', async () => {
        debug('Delete account confirmed');
        try {
            // Get current user data
            const { data } = await window.supabaseClient.auth.getUser();
            if (!data || !data.user) {
                throw new Error('No user found');
            }
            
            const userId = data.user.id;
            debug('Deleting user with ID', userId);
            
            // Call our backend endpoint to delete the user
            await makeBackendRequest(`/user/profile/${userId}`, {
                method: 'DELETE'
            });
            
            debug('User deleted successfully');
            
            // Sign out the user after deletion
            await window.supabaseClient.auth.signOut();
            
            // Close modal
            deleteAccountModal.style.display = 'none';
            
            // Update UI and show message
            showMessage('Your account has been successfully deleted.', 'success');
            checkUser();
        } catch (error) {
            debug('Delete account error', error);
            deleteAccountModal.style.display = 'none';
            showMessage(`Error deleting account: ${error.message}`, 'error');
        }
    });

    // Auth state changes listener
    window.supabaseClient.auth.onAuthStateChange((event, session) => {
        debug('Auth state changed', { event, session });
        if (event === 'SIGNED_IN') {
            checkUser();
        } else if (event === 'SIGNED_OUT') {
            authContainer.style.display = 'block';
            userContainer.style.display = 'none';
        }
    });

    // Check user on page load
    checkUser();
} 