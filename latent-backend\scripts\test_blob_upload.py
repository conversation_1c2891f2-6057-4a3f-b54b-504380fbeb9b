import os
from azure.storage.blob import BlobServiceClient
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Azure Storage connection string
connection_string = os.getenv("AZURE_STORAGE_CONNECTION_STRING")
container_name = os.getenv("AZURE_STORAGE_CONTAINER_NAME")

def upload_file(file_path: str, blob_name: str):
    try:
        # Create the BlobServiceClient
        blob_service_client = BlobServiceClient.from_connection_string(connection_string)
        
        # Get a reference to the container
        container_client = blob_service_client.get_container_client(container_name)
        
        # Get a reference to the blob
        blob_client = container_client.get_blob_client(blob_name)
        
        # Upload the file
        with open(file_path, "rb") as data:
            blob_client.upload_blob(data, overwrite=True)
            
        # Get the URL of the uploaded blob
        blob_url = blob_client.url
        print(f"File uploaded successfully!")
        print(f"Blob URL: {blob_url}")
        return blob_url
        
    except Exception as e:
        print(f"Error uploading file: {str(e)}")
        raise

if __name__ == "__main__":
    # Test file path - replace with your test file path
    test_file_path = "test.txt"
    
    # Create a test file if it doesn't exist
    if not os.path.exists(test_file_path):
        with open(test_file_path, "w") as f:
            f.write("This is a test file for Azure Blob Storage upload")
    
    # Test blob name
    test_blob_name = "test/test.txt"
    
    # Try uploading the file
    upload_file(test_file_path, test_blob_name) 