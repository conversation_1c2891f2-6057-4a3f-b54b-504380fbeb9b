-- ===========================================
-- TABLE DEFINITIONS
-- ===========================================

-- ORGANIZATIONS
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL UNIQUE,
    industry TEXT,
    location TEXT,
    size INTEGER,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    metadata JSONB DEFAULT '{}'
);

-- USERS
CREATE TABLE users (
    id UUID PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    first_name TEXT,
    last_name TEXT,
    created_at TIMESTAMPTZ DEFAULT now(),
    last_login TIMESTAMPTZ,
    is_admin BOOLEAN DEFAULT false,
    new_user BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}',
    organization_id UUID,
    org_role TEXT NOT NULL DEFAULT 'admin',
    FOREIGN KEY (organization_id) REFERENCES organizations(id) DEFERRABLE INITIALLY DEFERRED
);

-- REGULATIONS
CREATE TABLE regulations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    industry TEXT,
    region TEXT,
    compliance_type TEXT,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    uploaded_by UUID REFERENCES users(id),
    source TEXT
);

-- ORGANIZATION_REGULATIONS
CREATE TABLE organization_regulations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id),
    regulation_id UUID REFERENCES regulations(id),
    is_applicable BOOLEAN DEFAULT true,
    customization_notes TEXT,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    last_updated TIMESTAMPTZ DEFAULT now(),
    metadata JSONB DEFAULT '{}'
);

-- SOPS
CREATE TABLE sops (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    file_name TEXT NOT NULL,
    blob_storage_url TEXT NOT NULL,
    content_type TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    organization_id UUID REFERENCES organizations(id),
    uploaded_by UUID REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    is_deleted BOOLEAN DEFAULT false
);

ALTER TABLE sops ADD COLUMN blob_file_name TEXT DEFAULT NULL;
ALTER TABLE sops ADD COLUMN metadata JSONB DEFAULT '{}';



-- GAP_ANALYSIS_RESULTS
CREATE TABLE gap_analysis_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sop_id UUID REFERENCES sops(id),
    regulation_id UUID REFERENCES regulations(id),
    gap_details JSONB NOT NULL,
    compliance_score INTEGER CHECK (compliance_score BETWEEN 0 AND 100),
    analyzed_at TIMESTAMPTZ DEFAULT now(),
    metadata JSONB DEFAULT '{}'
);

-- ALLOWED_EMAILS
CREATE TABLE allowed_emails (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT UNIQUE NOT NULL,
    organization_id UUID REFERENCES organizations(id),
    is_active BOOLEAN DEFAULT true,
    expiry_date TIMESTAMPTZ,
    added_at TIMESTAMPTZ DEFAULT now(),
    added_by TEXT,
    org_role TEXT NOT NULL DEFAULT 'admin'
);

-- ===========================================
-- INDEXES
-- ===========================================

CREATE INDEX idx_users_org ON users (organization_id);
CREATE INDEX idx_sops_org ON sops (organization_id);
CREATE INDEX idx_sops_user ON sops (uploaded_by);
CREATE INDEX idx_gap_sop ON gap_analysis_results (sop_id);
CREATE INDEX idx_gap_reg ON gap_analysis_results (regulation_id);
CREATE INDEX idx_org_reg_org ON organization_regulations (organization_id);
CREATE INDEX idx_org_reg_reg ON organization_regulations (regulation_id);
CREATE INDEX idx_allowed_emails_email ON allowed_emails (email);
CREATE INDEX idx_allowed_emails_org ON allowed_emails (organization_id); 