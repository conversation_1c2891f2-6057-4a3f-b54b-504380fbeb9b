import apiService from './api';
import API_URLS from '../config/apiUrls';

const activityService = {
  // Get recent activity for the user's organization
  getRecentActivity: async () => {
    try {
      const response = await apiService.get(API_URLS.USER.RECENT_ACTIVITY);
      return response.data;
    } catch (error) {
      console.error('Error fetching recent activity:', error);
      throw error;
    }
  },
};

export default activityService;
