Collecting fastapi==0.115.12
  Using cached fastapi-0.115.12-py3-none-any.whl.metadata (27 kB)
Requirement already satisfied: uvicorn==0.34.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (0.34.0)
Requirement already satisfied: python-dotenv==1.1.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (1.1.0)
Requirement already satisfied: httpx==0.28.1 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (0.28.1)
Requirement already satisfied: azure-storage-blob==12.25.1 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (12.25.1)
Requirement already satisfied: PyJWT==2.10.1 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (2.10.1)
Requirement already satisfied: supabase in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (2.15.0)
Collecting anthropic==0.45.2
  Using cached anthropic-0.45.2-py3-none-any.whl.metadata (23 kB)
Collecting chromadb==0.6.3
  Using cached chromadb-0.6.3-py3-none-any.whl.metadata (6.8 kB)
Collecting json_repair==0.35.0
  Using cached json_repair-0.35.0-py3-none-any.whl.metadata (11 kB)
Collecting openai==1.61.0
  Using cached openai-1.61.0-py3-none-any.whl.metadata (27 kB)
Collecting pandas==2.2.3
  Using cached pandas-2.2.3-cp313-cp313-win_amd64.whl.metadata (19 kB)
Collecting protobuf==5.29.3
  Using cached protobuf-5.29.3-cp310-abi3-win_amd64.whl.metadata (592 bytes)
Collecting PyYAML==6.0.2
  Using cached PyYAML-6.0.2-cp313-cp313-win_amd64.whl.metadata (2.1 kB)
Requirement already satisfied: Requests==2.32.3 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (2.32.3)
Collecting streamlit==1.41.1
  Using cached streamlit-1.41.1-py2.py3-none-any.whl.metadata (8.5 kB)
Collecting google-generativeai==0.8.4
  Using cached google_generativeai-0.8.4-py3-none-any.whl.metadata (4.2 kB)
Collecting python-multipart==0.0.20
  Using cached python_multipart-0.0.20-py3-none-any.whl.metadata (1.8 kB)
Collecting sqlalchemy==2.0.27
  Downloading SQLAlchemy-2.0.27-py3-none-any.whl.metadata (9.6 kB)
Collecting python-docx==1.1.2
  Using cached python_docx-1.1.2-py3-none-any.whl.metadata (2.0 kB)
Requirement already satisfied: starlette<0.47.0,>=0.40.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from fastapi==0.115.12) (0.46.1)
Requirement already satisfied: pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from fastapi==0.115.12) (2.10.6)
Requirement already satisfied: typing-extensions>=4.8.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from fastapi==0.115.12) (4.12.2)
Requirement already satisfied: click>=7.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from uvicorn==0.34.0) (8.1.8)
Requirement already satisfied: h11>=0.8 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from uvicorn==0.34.0) (0.14.0)
Requirement already satisfied: anyio in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from httpx==0.28.1) (4.9.0)
Requirement already satisfied: certifi in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from httpx==0.28.1) (2025.1.31)
Requirement already satisfied: httpcore==1.* in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from httpx==0.28.1) (1.0.7)
Requirement already satisfied: idna in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from httpx==0.28.1) (3.10)
Requirement already satisfied: azure-core>=1.30.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from azure-storage-blob==12.25.1) (1.32.0)
Requirement already satisfied: cryptography>=2.1.4 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from azure-storage-blob==12.25.1) (44.0.2)
Requirement already satisfied: isodate>=0.6.1 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from azure-storage-blob==12.25.1) (0.7.2)
Collecting distro<2,>=1.7.0 (from anthropic==0.45.2)
  Downloading distro-1.9.0-py3-none-any.whl.metadata (6.8 kB)
Collecting jiter<1,>=0.4.0 (from anthropic==0.45.2)
  Downloading jiter-0.9.0-cp313-cp313-win_amd64.whl.metadata (5.3 kB)
Requirement already satisfied: sniffio in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from anthropic==0.45.2) (1.3.1)
Collecting build>=1.0.3 (from chromadb==0.6.3)
  Downloading build-1.2.2.post1-py3-none-any.whl.metadata (6.5 kB)
Collecting chroma-hnswlib==0.7.6 (from chromadb==0.6.3)
  Downloading chroma_hnswlib-0.7.6.tar.gz (32 kB)
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
Collecting numpy>=1.22.5 (from chromadb==0.6.3)
  Using cached numpy-2.2.4-cp313-cp313-win_amd64.whl.metadata (60 kB)
Collecting posthog>=2.4.0 (from chromadb==0.6.3)
  Downloading posthog-3.25.0-py2.py3-none-any.whl.metadata (3.0 kB)
Collecting onnxruntime>=1.14.1 (from chromadb==0.6.3)
  Downloading onnxruntime-1.21.1-cp313-cp313-win_amd64.whl.metadata (4.9 kB)
Requirement already satisfied: opentelemetry-api>=1.2.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from chromadb==0.6.3) (1.32.0)
Collecting opentelemetry-exporter-otlp-proto-grpc>=1.2.0 (from chromadb==0.6.3)
  Downloading opentelemetry_exporter_otlp_proto_grpc-1.32.1-py3-none-any.whl.metadata (2.5 kB)
Collecting opentelemetry-instrumentation-fastapi>=0.41b0 (from chromadb==0.6.3)
  Downloading opentelemetry_instrumentation_fastapi-0.53b1-py3-none-any.whl.metadata (2.2 kB)
Requirement already satisfied: opentelemetry-sdk>=1.2.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from chromadb==0.6.3) (1.32.0)
Collecting tokenizers>=0.13.2 (from chromadb==0.6.3)
  Downloading tokenizers-0.21.1-cp39-abi3-win_amd64.whl.metadata (6.9 kB)
Collecting pypika>=0.48.9 (from chromadb==0.6.3)
  Downloading PyPika-0.48.9.tar.gz (67 kB)
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
Collecting tqdm>=4.65.0 (from chromadb==0.6.3)
  Downloading tqdm-4.67.1-py3-none-any.whl.metadata (57 kB)
Collecting overrides>=7.3.1 (from chromadb==0.6.3)
  Downloading overrides-7.7.0-py3-none-any.whl.metadata (5.8 kB)
Collecting importlib-resources (from chromadb==0.6.3)
  Downloading importlib_resources-6.5.2-py3-none-any.whl.metadata (3.9 kB)
Collecting grpcio>=1.58.0 (from chromadb==0.6.3)
  Downloading grpcio-1.71.0-cp313-cp313-win_amd64.whl.metadata (4.0 kB)
Collecting bcrypt>=4.0.1 (from chromadb==0.6.3)
  Downloading bcrypt-4.3.0-cp39-abi3-win_amd64.whl.metadata (10 kB)
Collecting typer>=0.9.0 (from chromadb==0.6.3)
  Downloading typer-0.15.2-py3-none-any.whl.metadata (15 kB)
Collecting kubernetes>=28.1.0 (from chromadb==0.6.3)
  Downloading kubernetes-32.0.1-py2.py3-none-any.whl.metadata (1.7 kB)
Collecting tenacity>=8.2.3 (from chromadb==0.6.3)
  Downloading tenacity-9.1.2-py3-none-any.whl.metadata (1.2 kB)
Collecting mmh3>=4.0.1 (from chromadb==0.6.3)
  Downloading mmh3-5.1.0-cp313-cp313-win_amd64.whl.metadata (16 kB)
Collecting orjson>=3.9.12 (from chromadb==0.6.3)
  Downloading orjson-3.10.16-cp313-cp313-win_amd64.whl.metadata (42 kB)
Requirement already satisfied: rich>=10.11.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from chromadb==0.6.3) (14.0.0)
Requirement already satisfied: python-dateutil>=2.8.2 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from pandas==2.2.3) (2.9.0.post0)
Collecting pytz>=2020.1 (from pandas==2.2.3)
  Downloading pytz-2025.2-py2.py3-none-any.whl.metadata (22 kB)
Collecting tzdata>=2022.7 (from pandas==2.2.3)
  Downloading tzdata-2025.2-py2.py3-none-any.whl.metadata (1.4 kB)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from Requests==2.32.3) (3.4.1)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from Requests==2.32.3) (2.3.0)
Collecting altair<6,>=4.0 (from streamlit==1.41.1)
  Downloading altair-5.5.0-py3-none-any.whl.metadata (11 kB)
Collecting blinker<2,>=1.0.0 (from streamlit==1.41.1)
  Downloading blinker-1.9.0-py3-none-any.whl.metadata (1.6 kB)
Requirement already satisfied: cachetools<6,>=4.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from streamlit==1.41.1) (5.5.2)
Requirement already satisfied: packaging<25,>=20 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from streamlit==1.41.1) (24.2)
Collecting pillow<12,>=7.1.0 (from streamlit==1.41.1)
  Downloading pillow-11.2.1-cp313-cp313-win_amd64.whl.metadata (9.1 kB)
Collecting pyarrow>=7.0 (from streamlit==1.41.1)
  Downloading pyarrow-19.0.1-cp313-cp313-win_amd64.whl.metadata (3.4 kB)
Collecting rich>=10.11.0 (from chromadb==0.6.3)
  Downloading rich-13.9.4-py3-none-any.whl.metadata (18 kB)
Collecting toml<2,>=0.10.1 (from streamlit==1.41.1)
  Downloading toml-0.10.2-py2.py3-none-any.whl.metadata (7.1 kB)
Collecting watchdog<7,>=2.1.5 (from streamlit==1.41.1)
  Downloading watchdog-6.0.0-py3-none-win_amd64.whl.metadata (44 kB)
Collecting gitpython!=3.1.19,<4,>=3.0.7 (from streamlit==1.41.1)
  Downloading GitPython-3.1.44-py3-none-any.whl.metadata (13 kB)
Collecting pydeck<1,>=0.8.0b4 (from streamlit==1.41.1)
  Downloading pydeck-0.9.1-py2.py3-none-any.whl.metadata (4.1 kB)
Collecting tornado<7,>=6.0.3 (from streamlit==1.41.1)
  Downloading tornado-6.4.2-cp38-abi3-win_amd64.whl.metadata (2.6 kB)
Collecting google-ai-generativelanguage==0.6.15 (from google-generativeai==0.8.4)
  Downloading google_ai_generativelanguage-0.6.15-py3-none-any.whl.metadata (5.7 kB)
Collecting google-api-core (from google-generativeai==0.8.4)
  Downloading google_api_core-2.24.2-py3-none-any.whl.metadata (3.0 kB)
Collecting google-api-python-client (from google-generativeai==0.8.4)
  Downloading google_api_python_client-2.167.0-py2.py3-none-any.whl.metadata (6.7 kB)
Requirement already satisfied: google-auth>=2.15.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from google-generativeai==0.8.4) (2.39.0)
Requirement already satisfied: greenlet!=0.4.17 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from sqlalchemy==2.0.27) (3.2.0)
Requirement already satisfied: lxml>=3.1.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from python-docx==1.1.2) (5.3.2)
Collecting proto-plus<2.0.0dev,>=1.22.3 (from google-ai-generativelanguage==0.6.15->google-generativeai==0.8.4)
  Downloading proto_plus-1.26.1-py3-none-any.whl.metadata (2.2 kB)
Requirement already satisfied: gotrue<3.0.0,>=2.11.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from supabase) (2.12.0)
Requirement already satisfied: postgrest<1.1,>0.19 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from supabase) (1.0.1)
Requirement already satisfied: realtime<2.5.0,>=2.4.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from supabase) (2.4.2)
Requirement already satisfied: storage3<0.12,>=0.10 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from supabase) (0.11.3)
Requirement already satisfied: supafunc<0.10,>=0.9 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from supabase) (0.9.4)
Collecting jinja2 (from altair<6,>=4.0->streamlit==1.41.1)
  Downloading jinja2-3.1.6-py3-none-any.whl.metadata (2.9 kB)
Collecting jsonschema>=3.0 (from altair<6,>=4.0->streamlit==1.41.1)
  Downloading jsonschema-4.23.0-py3-none-any.whl.metadata (7.9 kB)
Collecting narwhals>=1.14.2 (from altair<6,>=4.0->streamlit==1.41.1)
  Downloading narwhals-1.35.0-py3-none-any.whl.metadata (9.2 kB)
Requirement already satisfied: six>=1.11.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from azure-core>=1.30.0->azure-storage-blob==12.25.1) (1.17.0)
Collecting pyproject_hooks (from build>=1.0.3->chromadb==0.6.3)
  Downloading pyproject_hooks-1.2.0-py3-none-any.whl.metadata (1.3 kB)
Requirement already satisfied: colorama in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from build>=1.0.3->chromadb==0.6.3) (0.4.6)
Requirement already satisfied: cffi>=1.12 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from cryptography>=2.1.4->azure-storage-blob==12.25.1) (1.17.1)
Collecting gitdb<5,>=4.0.1 (from gitpython!=3.1.19,<4,>=3.0.7->streamlit==1.41.1)
  Downloading gitdb-4.0.12-py3-none-any.whl.metadata (1.2 kB)
Requirement already satisfied: googleapis-common-protos<2.0.0,>=1.56.2 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from google-api-core->google-generativeai==0.8.4) (1.69.2)
Requirement already satisfied: pyasn1-modules>=0.2.1 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from google-auth>=2.15.0->google-generativeai==0.8.4) (0.4.2)
Requirement already satisfied: rsa<5,>=3.1.4 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from google-auth>=2.15.0->google-generativeai==0.8.4) (4.9.1)
Requirement already satisfied: pytest-mock<4.0.0,>=3.14.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from gotrue<3.0.0,>=2.11.0->supabase) (3.14.0)
Collecting websocket-client!=0.40.0,!=0.41.*,!=0.42.*,>=0.32.0 (from kubernetes>=28.1.0->chromadb==0.6.3)
  Downloading websocket_client-1.8.0-py3-none-any.whl.metadata (8.0 kB)
Collecting requests-oauthlib (from kubernetes>=28.1.0->chromadb==0.6.3)
  Downloading requests_oauthlib-2.0.0-py2.py3-none-any.whl.metadata (11 kB)
Collecting oauthlib>=3.2.2 (from kubernetes>=28.1.0->chromadb==0.6.3)
  Downloading oauthlib-3.2.2-py3-none-any.whl.metadata (7.5 kB)
Collecting durationpy>=0.7 (from kubernetes>=28.1.0->chromadb==0.6.3)
  Downloading durationpy-0.9-py3-none-any.whl.metadata (338 bytes)
Collecting coloredlogs (from onnxruntime>=1.14.1->chromadb==0.6.3)
  Downloading coloredlogs-15.0.1-py2.py3-none-any.whl.metadata (12 kB)
Collecting flatbuffers (from onnxruntime>=1.14.1->chromadb==0.6.3)
  Downloading flatbuffers-25.2.10-py2.py3-none-any.whl.metadata (875 bytes)
Collecting sympy (from onnxruntime>=1.14.1->chromadb==0.6.3)
  Downloading sympy-1.13.3-py3-none-any.whl.metadata (12 kB)
Requirement already satisfied: deprecated>=1.2.6 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from opentelemetry-api>=1.2.0->chromadb==0.6.3) (1.2.18)
Requirement already satisfied: importlib-metadata<8.7.0,>=6.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from opentelemetry-api>=1.2.0->chromadb==0.6.3) (8.6.1)
Collecting opentelemetry-exporter-otlp-proto-common==1.32.1 (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb==0.6.3)
  Downloading opentelemetry_exporter_otlp_proto_common-1.32.1-py3-none-any.whl.metadata (1.9 kB)
Collecting opentelemetry-proto==1.32.1 (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb==0.6.3)
  Downloading opentelemetry_proto-1.32.1-py3-none-any.whl.metadata (2.4 kB)
Collecting opentelemetry-sdk>=1.2.0 (from chromadb==0.6.3)
  Downloading opentelemetry_sdk-1.32.1-py3-none-any.whl.metadata (1.6 kB)
Collecting opentelemetry-instrumentation-asgi==0.53b1 (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb==0.6.3)
  Downloading opentelemetry_instrumentation_asgi-0.53b1-py3-none-any.whl.metadata (2.1 kB)
Collecting opentelemetry-instrumentation==0.53b1 (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb==0.6.3)
  Downloading opentelemetry_instrumentation-0.53b1-py3-none-any.whl.metadata (6.8 kB)
Collecting opentelemetry-semantic-conventions==0.53b1 (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb==0.6.3)
  Downloading opentelemetry_semantic_conventions-0.53b1-py3-none-any.whl.metadata (2.5 kB)
Collecting opentelemetry-util-http==0.53b1 (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb==0.6.3)
  Downloading opentelemetry_util_http-0.53b1-py3-none-any.whl.metadata (2.6 kB)
Requirement already satisfied: wrapt<2.0.0,>=1.0.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from opentelemetry-instrumentation==0.53b1->opentelemetry-instrumentation-fastapi>=0.41b0->chromadb==0.6.3) (1.17.2)
Collecting asgiref~=3.0 (from opentelemetry-instrumentation-asgi==0.53b1->opentelemetry-instrumentation-fastapi>=0.41b0->chromadb==0.6.3)
  Downloading asgiref-3.8.1-py3-none-any.whl.metadata (9.3 kB)
Collecting opentelemetry-api>=1.2.0 (from chromadb==0.6.3)
  Downloading opentelemetry_api-1.32.1-py3-none-any.whl.metadata (1.6 kB)
Requirement already satisfied: deprecation<3.0.0,>=2.1.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from postgrest<1.1,>0.19->supabase) (2.1.0)
Collecting monotonic>=1.5 (from posthog>=2.4.0->chromadb==0.6.3)
  Downloading monotonic-1.6-py2.py3-none-any.whl.metadata (1.5 kB)
Collecting backoff>=1.10.0 (from posthog>=2.4.0->chromadb==0.6.3)
  Downloading backoff-2.2.1-py3-none-any.whl.metadata (14 kB)
Requirement already satisfied: annotated-types>=0.6.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4->fastapi==0.115.12) (0.7.0)
Requirement already satisfied: pydantic-core==2.27.2 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4->fastapi==0.115.12) (2.27.2)
Requirement already satisfied: aiohttp<4.0.0,>=3.11.14 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from realtime<2.5.0,>=2.4.0->supabase) (3.11.15)
Requirement already satisfied: websockets<15,>=11 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from realtime<2.5.0,>=2.4.0->supabase) (14.2)
Requirement already satisfied: markdown-it-py>=2.2.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from rich>=10.11.0->chromadb==0.6.3) (3.0.0)
Requirement already satisfied: pygments<3.0.0,>=2.13.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from rich>=10.11.0->chromadb==0.6.3) (2.19.1)
Requirement already satisfied: strenum<0.5.0,>=0.4.15 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from supafunc<0.10,>=0.9->supabase) (0.4.15)
Collecting huggingface-hub<1.0,>=0.16.4 (from tokenizers>=0.13.2->chromadb==0.6.3)
  Downloading huggingface_hub-0.30.2-py3-none-any.whl.metadata (13 kB)
Collecting shellingham>=1.3.0 (from typer>=0.9.0->chromadb==0.6.3)
  Downloading shellingham-1.5.4-py2.py3-none-any.whl.metadata (3.5 kB)
Collecting httptools>=0.6.3 (from uvicorn[standard]>=0.18.3->chromadb==0.6.3)
  Downloading httptools-0.6.4-cp313-cp313-win_amd64.whl.metadata (3.7 kB)
Collecting watchfiles>=0.13 (from uvicorn[standard]>=0.18.3->chromadb==0.6.3)
  Downloading watchfiles-1.0.5-cp313-cp313-win_amd64.whl.metadata (5.0 kB)
Collecting httplib2<1.0.0,>=0.19.0 (from google-api-python-client->google-generativeai==0.8.4)
  Downloading httplib2-0.22.0-py3-none-any.whl.metadata (2.6 kB)
Collecting google-auth-httplib2<1.0.0,>=0.2.0 (from google-api-python-client->google-generativeai==0.8.4)
  Downloading google_auth_httplib2-0.2.0-py2.py3-none-any.whl.metadata (2.2 kB)
Collecting uritemplate<5,>=3.0.1 (from google-api-python-client->google-generativeai==0.8.4)
  Downloading uritemplate-4.1.1-py2.py3-none-any.whl.metadata (2.9 kB)
Requirement already satisfied: aiohappyeyeballs>=2.3.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from aiohttp<4.0.0,>=3.11.14->realtime<2.5.0,>=2.4.0->supabase) (2.6.1)
Requirement already satisfied: aiosignal>=1.1.2 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from aiohttp<4.0.0,>=3.11.14->realtime<2.5.0,>=2.4.0->supabase) (1.3.2)
Requirement already satisfied: attrs>=17.3.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from aiohttp<4.0.0,>=3.11.14->realtime<2.5.0,>=2.4.0->supabase) (25.3.0)
Requirement already satisfied: frozenlist>=1.1.1 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from aiohttp<4.0.0,>=3.11.14->realtime<2.5.0,>=2.4.0->supabase) (1.5.0)
Requirement already satisfied: multidict<7.0,>=4.5 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from aiohttp<4.0.0,>=3.11.14->realtime<2.5.0,>=2.4.0->supabase) (6.3.0)
Requirement already satisfied: propcache>=0.2.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from aiohttp<4.0.0,>=3.11.14->realtime<2.5.0,>=2.4.0->supabase) (0.3.1)
Requirement already satisfied: yarl<2.0,>=1.17.0 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from aiohttp<4.0.0,>=3.11.14->realtime<2.5.0,>=2.4.0->supabase) (1.18.3)
Requirement already satisfied: pycparser in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from cffi>=1.12->cryptography>=2.1.4->azure-storage-blob==12.25.1) (2.22)
Collecting smmap<6,>=3.0.1 (from gitdb<5,>=4.0.1->gitpython!=3.1.19,<4,>=3.0.7->streamlit==1.41.1)
  Downloading smmap-5.0.2-py3-none-any.whl.metadata (4.3 kB)
Collecting grpcio>=1.58.0 (from chromadb==0.6.3)
  Downloading grpcio-1.72.0rc1-cp313-cp313-win_amd64.whl.metadata (4.0 kB)
Collecting grpcio-status<2.0.dev0,>=1.33.2 (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0dev,>=1.34.1->google-ai-generativelanguage==0.6.15->google-generativeai==0.8.4)
  Downloading grpcio_status-1.72.0rc1-py3-none-any.whl.metadata (1.1 kB)
Collecting pyparsing!=3.0.0,!=3.0.1,!=3.0.2,!=3.0.3,<4,>=2.4.2 (from httplib2<1.0.0,>=0.19.0->google-api-python-client->google-generativeai==0.8.4)
  Downloading pyparsing-3.2.3-py3-none-any.whl.metadata (5.0 kB)
Requirement already satisfied: h2<5,>=3 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from httpx[http2]<0.29,>=0.26->gotrue<3.0.0,>=2.11.0->supabase) (4.2.0)
Collecting filelock (from huggingface-hub<1.0,>=0.16.4->tokenizers>=0.13.2->chromadb==0.6.3)
  Downloading filelock-3.18.0-py3-none-any.whl.metadata (2.9 kB)
Collecting fsspec>=2023.5.0 (from huggingface-hub<1.0,>=0.16.4->tokenizers>=0.13.2->chromadb==0.6.3)
  Downloading fsspec-2025.3.2-py3-none-any.whl.metadata (11 kB)
Requirement already satisfied: zipp>=3.20 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from importlib-metadata<8.7.0,>=6.0->opentelemetry-api>=1.2.0->chromadb==0.6.3) (3.21.0)
Collecting MarkupSafe>=2.0 (from jinja2->altair<6,>=4.0->streamlit==1.41.1)
  Downloading MarkupSafe-3.0.2-cp313-cp313-win_amd64.whl.metadata (4.1 kB)
Collecting jsonschema-specifications>=2023.03.6 (from jsonschema>=3.0->altair<6,>=4.0->streamlit==1.41.1)
  Downloading jsonschema_specifications-2024.10.1-py3-none-any.whl.metadata (3.0 kB)
Collecting referencing>=0.28.4 (from jsonschema>=3.0->altair<6,>=4.0->streamlit==1.41.1)
  Downloading referencing-0.36.2-py3-none-any.whl.metadata (2.8 kB)
Collecting rpds-py>=0.7.1 (from jsonschema>=3.0->altair<6,>=4.0->streamlit==1.41.1)
  Downloading rpds_py-0.24.0-cp313-cp313-win_amd64.whl.metadata (4.2 kB)
Requirement already satisfied: mdurl~=0.1 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->chromadb==0.6.3) (0.1.2)
Requirement already satisfied: pyasn1<0.7.0,>=0.6.1 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from pyasn1-modules>=0.2.1->google-auth>=2.15.0->google-generativeai==0.8.4) (0.6.1)
Requirement already satisfied: pytest>=6.2.5 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from pytest-mock<4.0.0,>=3.14.0->gotrue<3.0.0,>=2.11.0->supabase) (8.3.5)
Collecting humanfriendly>=9.1 (from coloredlogs->onnxruntime>=1.14.1->chromadb==0.6.3)
  Downloading humanfriendly-10.0-py2.py3-none-any.whl.metadata (9.2 kB)
Collecting mpmath<1.4,>=1.1.0 (from sympy->onnxruntime>=1.14.1->chromadb==0.6.3)
  Downloading mpmath-1.3.0-py3-none-any.whl.metadata (8.6 kB)
INFO: pip is looking at multiple versions of grpcio-status to determine which version is compatible with other requirements. This could take a while.
Collecting grpcio-status<2.0.dev0,>=1.33.2 (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0dev,>=1.34.1->google-ai-generativelanguage==0.6.15->google-generativeai==0.8.4)
  Downloading grpcio_status-1.71.0-py3-none-any.whl.metadata (1.1 kB)
Requirement already satisfied: hyperframe<7,>=6.1 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from h2<5,>=3->httpx[http2]<0.29,>=0.26->gotrue<3.0.0,>=2.11.0->supabase) (6.1.0)
Requirement already satisfied: hpack<5,>=4.1 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from h2<5,>=3->httpx[http2]<0.29,>=0.26->gotrue<3.0.0,>=2.11.0->supabase) (4.1.0)
Collecting pyreadline3 (from humanfriendly>=9.1->coloredlogs->onnxruntime>=1.14.1->chromadb==0.6.3)
  Downloading pyreadline3-3.5.4-py3-none-any.whl.metadata (4.7 kB)
Requirement already satisfied: iniconfig in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from pytest>=6.2.5->pytest-mock<4.0.0,>=3.14.0->gotrue<3.0.0,>=2.11.0->supabase) (2.1.0)
Requirement already satisfied: pluggy<2,>=1.5 in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from pytest>=6.2.5->pytest-mock<4.0.0,>=3.14.0->gotrue<3.0.0,>=2.11.0->supabase) (1.5.0)
Downloading fastapi-0.115.12-py3-none-any.whl (95 kB)
Downloading anthropic-0.45.2-py3-none-any.whl (222 kB)
Downloading chromadb-0.6.3-py3-none-any.whl (611 kB)
   ---------------------------------------- 611.1/611.1 kB 10.5 MB/s eta 0:00:00
Downloading json_repair-0.35.0-py3-none-any.whl (19 kB)
Downloading openai-1.61.0-py3-none-any.whl (460 kB)
Downloading pandas-2.2.3-cp313-cp313-win_amd64.whl (11.5 MB)
   ---------------------------------------- 11.5/11.5 MB 4.0 MB/s eta 0:00:00
Downloading protobuf-5.29.3-cp310-abi3-win_amd64.whl (434 kB)
Downloading PyYAML-6.0.2-cp313-cp313-win_amd64.whl (156 kB)
Downloading streamlit-1.41.1-py2.py3-none-any.whl (9.1 MB)
   ---------------------------------------- 9.1/9.1 MB 4.7 MB/s eta 0:00:00
Downloading google_generativeai-0.8.4-py3-none-any.whl (175 kB)
Using cached python_multipart-0.0.20-py3-none-any.whl (24 kB)
Downloading SQLAlchemy-2.0.27-py3-none-any.whl (1.9 MB)
   ---------------------------------------- 1.9/1.9 MB 4.8 MB/s eta 0:00:00
Using cached python_docx-1.1.2-py3-none-any.whl (244 kB)
Downloading google_ai_generativelanguage-0.6.15-py3-none-any.whl (1.3 MB)
   ---------------------------------------- 1.3/1.3 MB 5.9 MB/s eta 0:00:00
Downloading altair-5.5.0-py3-none-any.whl (731 kB)
   ---------------------------------------- 731.2/731.2 kB 5.7 MB/s eta 0:00:00
Downloading bcrypt-4.3.0-cp39-abi3-win_amd64.whl (152 kB)
Downloading blinker-1.9.0-py3-none-any.whl (8.5 kB)
Downloading build-1.2.2.post1-py3-none-any.whl (22 kB)
Downloading distro-1.9.0-py3-none-any.whl (20 kB)
Downloading GitPython-3.1.44-py3-none-any.whl (207 kB)
Downloading google_api_core-2.24.2-py3-none-any.whl (160 kB)
Downloading grpcio-1.71.0-cp313-cp313-win_amd64.whl (4.3 MB)
   ---------------------------------------- 4.3/4.3 MB 6.6 MB/s eta 0:00:00
Downloading jiter-0.9.0-cp313-cp313-win_amd64.whl (204 kB)
Downloading kubernetes-32.0.1-py2.py3-none-any.whl (2.0 MB)
   ---------------------------------------- 2.0/2.0 MB 6.6 MB/s eta 0:00:00
Downloading mmh3-5.1.0-cp313-cp313-win_amd64.whl (41 kB)
Using cached numpy-2.2.4-cp313-cp313-win_amd64.whl (12.6 MB)
Downloading onnxruntime-1.21.1-cp313-cp313-win_amd64.whl (12.3 MB)
   ---------------------------------------- 12.3/12.3 MB 4.7 MB/s eta 0:00:00
Downloading opentelemetry_exporter_otlp_proto_grpc-1.32.1-py3-none-any.whl (18 kB)
Downloading opentelemetry_exporter_otlp_proto_common-1.32.1-py3-none-any.whl (18 kB)
Downloading opentelemetry_proto-1.32.1-py3-none-any.whl (55 kB)
Downloading opentelemetry_instrumentation_fastapi-0.53b1-py3-none-any.whl (12 kB)
Downloading opentelemetry_instrumentation-0.53b1-py3-none-any.whl (30 kB)
Downloading opentelemetry_instrumentation_asgi-0.53b1-py3-none-any.whl (16 kB)
Downloading opentelemetry_semantic_conventions-0.53b1-py3-none-any.whl (188 kB)
Downloading opentelemetry_api-1.32.1-py3-none-any.whl (65 kB)
Downloading opentelemetry_util_http-0.53b1-py3-none-any.whl (7.3 kB)
Downloading opentelemetry_sdk-1.32.1-py3-none-any.whl (118 kB)
Downloading orjson-3.10.16-cp313-cp313-win_amd64.whl (133 kB)
Downloading overrides-7.7.0-py3-none-any.whl (17 kB)
Downloading pillow-11.2.1-cp313-cp313-win_amd64.whl (2.7 MB)
   ---------------------------------------- 2.7/2.7 MB 5.5 MB/s eta 0:00:00
Downloading posthog-3.25.0-py2.py3-none-any.whl (89 kB)
Downloading pyarrow-19.0.1-cp313-cp313-win_amd64.whl (25.2 MB)
   ---------------------------------------- 25.2/25.2 MB 4.4 MB/s eta 0:00:00
Downloading pydeck-0.9.1-py2.py3-none-any.whl (6.9 MB)
   ---------------------------------------- 6.9/6.9 MB 4.8 MB/s eta 0:00:00
Downloading pytz-2025.2-py2.py3-none-any.whl (509 kB)
Downloading rich-13.9.4-py3-none-any.whl (242 kB)
Downloading tenacity-9.1.2-py3-none-any.whl (28 kB)
Downloading tokenizers-0.21.1-cp39-abi3-win_amd64.whl (2.4 MB)
   ---------------------------------------- 2.4/2.4 MB 5.3 MB/s eta 0:00:00
Downloading toml-0.10.2-py2.py3-none-any.whl (16 kB)
Downloading tornado-6.4.2-cp38-abi3-win_amd64.whl (438 kB)
Downloading tqdm-4.67.1-py3-none-any.whl (78 kB)
Downloading typer-0.15.2-py3-none-any.whl (45 kB)
Downloading tzdata-2025.2-py2.py3-none-any.whl (347 kB)
Downloading watchdog-6.0.0-py3-none-win_amd64.whl (79 kB)
Downloading google_api_python_client-2.167.0-py2.py3-none-any.whl (13.2 MB)
   ---------------------------------------- 13.2/13.2 MB 3.9 MB/s eta 0:00:00
Downloading importlib_resources-6.5.2-py3-none-any.whl (37 kB)
Downloading backoff-2.2.1-py3-none-any.whl (15 kB)
Downloading durationpy-0.9-py3-none-any.whl (3.5 kB)
Downloading gitdb-4.0.12-py3-none-any.whl (62 kB)
Downloading google_auth_httplib2-0.2.0-py2.py3-none-any.whl (9.3 kB)
Downloading httplib2-0.22.0-py3-none-any.whl (96 kB)
Downloading httptools-0.6.4-cp313-cp313-win_amd64.whl (87 kB)
Downloading huggingface_hub-0.30.2-py3-none-any.whl (481 kB)
Downloading jinja2-3.1.6-py3-none-any.whl (134 kB)
Downloading jsonschema-4.23.0-py3-none-any.whl (88 kB)
Downloading monotonic-1.6-py2.py3-none-any.whl (8.2 kB)
Downloading narwhals-1.35.0-py3-none-any.whl (325 kB)
Downloading oauthlib-3.2.2-py3-none-any.whl (151 kB)
Downloading proto_plus-1.26.1-py3-none-any.whl (50 kB)
Downloading shellingham-1.5.4-py2.py3-none-any.whl (9.8 kB)
Downloading uritemplate-4.1.1-py2.py3-none-any.whl (10 kB)
Downloading watchfiles-1.0.5-cp313-cp313-win_amd64.whl (291 kB)
Downloading websocket_client-1.8.0-py3-none-any.whl (58 kB)
Downloading coloredlogs-15.0.1-py2.py3-none-any.whl (46 kB)
Downloading flatbuffers-25.2.10-py2.py3-none-any.whl (30 kB)
Downloading pyproject_hooks-1.2.0-py3-none-any.whl (10 kB)
Downloading requests_oauthlib-2.0.0-py2.py3-none-any.whl (24 kB)
Downloading sympy-1.13.3-py3-none-any.whl (6.2 MB)
   ---------------------------------------- 6.2/6.2 MB 4.4 MB/s eta 0:00:00
Downloading asgiref-3.8.1-py3-none-any.whl (23 kB)
Downloading fsspec-2025.3.2-py3-none-any.whl (194 kB)
Downloading grpcio_status-1.71.0-py3-none-any.whl (14 kB)
Downloading humanfriendly-10.0-py2.py3-none-any.whl (86 kB)
Downloading jsonschema_specifications-2024.10.1-py3-none-any.whl (18 kB)
Downloading MarkupSafe-3.0.2-cp313-cp313-win_amd64.whl (15 kB)
Downloading mpmath-1.3.0-py3-none-any.whl (536 kB)
   ---------------------------------------- 536.2/536.2 kB 5.1 MB/s eta 0:00:00
Downloading pyparsing-3.2.3-py3-none-any.whl (111 kB)
Downloading referencing-0.36.2-py3-none-any.whl (26 kB)
Downloading rpds_py-0.24.0-cp313-cp313-win_amd64.whl (239 kB)
Downloading smmap-5.0.2-py3-none-any.whl (24 kB)
Downloading filelock-3.18.0-py3-none-any.whl (16 kB)
Downloading pyreadline3-3.5.4-py3-none-any.whl (83 kB)
Building wheels for collected packages: chroma-hnswlib, pypika
  Building wheel for chroma-hnswlib (pyproject.toml): started
  Building wheel for chroma-hnswlib (pyproject.toml): finished with status 'done'
  Created wheel for chroma-hnswlib: filename=chroma_hnswlib-0.7.6-cp313-cp313-win_amd64.whl size=156721 sha256=4126db0107a05a8c7013cbe4d564ba031790c05cc911124f13a6405c14e2a5e6
  Stored in directory: c:\users\<USER>\appdata\local\pip\cache\wheels\e4\de\05\47d2e8cd71d86b683765286c3308516ddcb7e8bf7db44fa69f
  Building wheel for pypika (pyproject.toml): started
  Building wheel for pypika (pyproject.toml): finished with status 'done'
  Created wheel for pypika: filename=pypika-0.48.9-py2.py3-none-any.whl size=53914 sha256=6947ba8ad4ebca6d12f9d2267b513705617600a520f8f8a5ea003c808684754a
  Stored in directory: c:\users\<USER>\appdata\local\pip\cache\wheels\b4\f8\a5\28e9c1524d320f4b8eefdce0e487b5c2e128dbf2ed1bb4a60b
Successfully built chroma-hnswlib pypika
Installing collected packages: pytz, pypika, mpmath, monotonic, flatbuffers, durationpy, websocket-client, watchdog, uritemplate, tzdata, tqdm, tornado, toml, tenacity, sympy, sqlalchemy, smmap, shellingham, rpds-py, PyYAML, python-multipart, python-docx, pyreadline3, pyproject_hooks, pyparsing, pyarrow, protobuf, pillow, overrides, orjson, opentelemetry-util-http, oauthlib, numpy, narwhals, mmh3, MarkupSafe, json_repair, jiter, importlib-resources, httptools, grpcio, fsspec, filelock, distro, blinker, bcrypt, backoff, asgiref, watchfiles, rich, requests-oauthlib, referencing, proto-plus, posthog, pandas, opentelemetry-proto, opentelemetry-api, jinja2, humanfriendly, huggingface-hub, httplib2, gitdb, chroma-hnswlib, build, typer, tokenizers, pydeck, opentelemetry-semantic-conventions, opentelemetry-exporter-otlp-proto-common, openai, kubernetes, jsonschema-specifications, grpcio-status, google-auth-httplib2, google-api-core, gitpython, fastapi, coloredlogs, anthropic, opentelemetry-sdk, opentelemetry-instrumentation, onnxruntime, jsonschema, google-api-python-client, opentelemetry-instrumentation-asgi, opentelemetry-exporter-otlp-proto-grpc, google-ai-generativelanguage, altair, streamlit, opentelemetry-instrumentation-fastapi, google-generativeai, chromadb
  Attempting uninstall: protobuf
    Found existing installation: protobuf 5.29.4
    Uninstalling protobuf-5.29.4:
      Successfully uninstalled protobuf-5.29.4
  Attempting uninstall: rich
    Found existing installation: rich 14.0.0
    Uninstalling rich-14.0.0:
      Successfully uninstalled rich-14.0.0
  Attempting uninstall: opentelemetry-proto
    Found existing installation: opentelemetry-proto 1.32.0
    Uninstalling opentelemetry-proto-1.32.0:
      Successfully uninstalled opentelemetry-proto-1.32.0
  Attempting uninstall: opentelemetry-api
    Found existing installation: opentelemetry-api 1.32.0
    Uninstalling opentelemetry-api-1.32.0:
      Successfully uninstalled opentelemetry-api-1.32.0
  Attempting uninstall: opentelemetry-semantic-conventions
    Found existing installation: opentelemetry-semantic-conventions 0.53b0
    Uninstalling opentelemetry-semantic-conventions-0.53b0:
      Successfully uninstalled opentelemetry-semantic-conventions-0.53b0
  Attempting uninstall: opentelemetry-exporter-otlp-proto-common
    Found existing installation: opentelemetry-exporter-otlp-proto-common 1.32.0
    Uninstalling opentelemetry-exporter-otlp-proto-common-1.32.0:
      Successfully uninstalled opentelemetry-exporter-otlp-proto-common-1.32.0
  Attempting uninstall: fastapi
    Found existing installation: fastapi 0.115.11
    Uninstalling fastapi-0.115.11:
      Successfully uninstalled fastapi-0.115.11
  Attempting uninstall: opentelemetry-sdk
    Found existing installation: opentelemetry-sdk 1.32.0
    Uninstalling opentelemetry-sdk-1.32.0:
      Successfully uninstalled opentelemetry-sdk-1.32.0
  Attempting uninstall: opentelemetry-instrumentation
    Found existing installation: opentelemetry-instrumentation 0.53b0
    Uninstalling opentelemetry-instrumentation-0.53b0:
      Successfully uninstalled opentelemetry-instrumentation-0.53b0
Successfully installed MarkupSafe-3.0.2 PyYAML-6.0.2 altair-5.5.0 anthropic-0.45.2 asgiref-3.8.1 backoff-2.2.1 bcrypt-4.3.0 blinker-1.9.0 build-1.2.2.post1 chroma-hnswlib-0.7.6 chromadb-0.6.3 coloredlogs-15.0.1 distro-1.9.0 durationpy-0.9 fastapi-0.115.12 filelock-3.18.0 flatbuffers-25.2.10 fsspec-2025.3.2 gitdb-4.0.12 gitpython-3.1.44 google-ai-generativelanguage-0.6.15 google-api-core-2.24.2 google-api-python-client-2.167.0 google-auth-httplib2-0.2.0 google-generativeai-0.8.4 grpcio-1.71.0 grpcio-status-1.71.0 httplib2-0.22.0 httptools-0.6.4 huggingface-hub-0.30.2 humanfriendly-10.0 importlib-resources-6.5.2 jinja2-3.1.6 jiter-0.9.0 json_repair-0.35.0 jsonschema-4.23.0 jsonschema-specifications-2024.10.1 kubernetes-32.0.1 mmh3-5.1.0 monotonic-1.6 mpmath-1.3.0 narwhals-1.35.0 numpy-2.2.4 oauthlib-3.2.2 onnxruntime-1.21.1 openai-1.61.0 opentelemetry-api-1.32.1 opentelemetry-exporter-otlp-proto-common-1.32.1 opentelemetry-exporter-otlp-proto-grpc-1.32.1 opentelemetry-instrumentation-0.53b1 opentelemetry-instrumentation-asgi-0.53b1 opentelemetry-instrumentation-fastapi-0.53b1 opentelemetry-proto-1.32.1 opentelemetry-sdk-1.32.1 opentelemetry-semantic-conventions-0.53b1 opentelemetry-util-http-0.53b1 orjson-3.10.16 overrides-7.7.0 pandas-2.2.3 pillow-11.2.1 posthog-3.25.0 proto-plus-1.26.1 protobuf-5.29.3 pyarrow-19.0.1 pydeck-0.9.1 pyparsing-3.2.3 pypika-0.48.9 pyproject_hooks-1.2.0 pyreadline3-3.5.4 python-docx-1.1.2 python-multipart-0.0.20 pytz-2025.2 referencing-0.36.2 requests-oauthlib-2.0.0 rich-13.9.4 rpds-py-0.24.0 shellingham-1.5.4 smmap-5.0.2 sqlalchemy-2.0.27 streamlit-1.41.1 sympy-1.13.3 tenacity-9.1.2 tokenizers-0.21.1 toml-0.10.2 tornado-6.4.2 tqdm-4.67.1 typer-0.15.2 tzdata-2025.2 uritemplate-4.1.1 watchdog-6.0.0 watchfiles-1.0.5 websocket-client-1.8.0
