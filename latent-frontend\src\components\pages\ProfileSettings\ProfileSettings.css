.profile-settings {
  margin-top: 2rem;
  padding: 0 2rem;
  max-width: 1200px;
  margin: 2rem auto;
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
}

.profile-header h1 {
  font-size: 24px;
  color: #333;
  margin: 0;
}

.profile-image {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
  position: absolute;
  right: 10px;
}

.settings-section {
  margin-bottom: 2rem;
  width: 100%;
  /* padding: 0 1rem; */
}

.settings-section h2 {
  font-size: 16px;
  color: #333;
  margin-bottom: 1rem;
}

.profile-header-line {
  width: 100%;
    background: rgb(243, 243, 243);
    border: none;
    height: 1px;
}

.form-group {
  margin-bottom: 1rem;
  width: 100%;
}

.form-group label {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 0.5rem;
}

.profile-settings .form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e1e1e1;
  border-radius: 6px;
  font-size: 14px;
  background-color: var(--bg-slate-50);
  box-sizing: border-box;
}

.form-group input::placeholder {
  color: #999;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  width: 100%;
}

.toggle-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.toggle-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: #f8f7ff;
  border-radius: 6px;
}

.toggle-item label {
  font-size: 14px;
  color: #333;
}

/* Toggle Switch Styles */
.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
}

.switch input:checked + .slider {
  background-color: #6c63ff;
}

.switch input:checked + .slider:before {
  transform: translateX(20px);
}

.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

.selected-standards {
  font-size: 14px;
  color: #666;
  margin-top: 0.5rem;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;
}

.btn-save, .btn-cancel {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 160px;
  text-align: center;
}

.profile-settings .btn-save {
  padding: 0.75rem 1.5rem;
  background-color: #6c63ff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.btn-save:hover:not(:disabled) {
  background-color: #5b52e0;
}

.btn-save:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  background-color: #a5a5a5;
  color: white;
  border: none;
}

.btn-cancel {
  background-color: transparent;
  color: #6c63ff;
  border: none;
}

.btn-cancel:hover {
  color: #5a52e0;
  background-color: rgba(108, 99, 255, 0.05);
}

@media (max-width: 768px) {
  .profile-settings {
    padding: 0 1rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
}

/* Add these styles to your existing CSS */

.product-categories {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0px;
  /* background-color: #f8f7ff; */
  border-radius: 8px;
}

.category-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.category-icon {
  width: 32px;
  height: 32px;
  background-color: #F7F6FF;
  border-radius: 8px;
}

.category-left span {
  color: #333;
  font-size: 14px;
}

/* Toggle Switch Styles */
.toggle-switch {
  position: relative;
  width: 36px;
  height: 20px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-switch label {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #e1e1e1;
  transition: .4s;
  border-radius: 20px;
}

.toggle-switch label:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

.toggle-switch input:checked + label {
  background-color: #6c63ff;
}

.toggle-switch input:checked + label:before {
  transform: translateX(16px);
}

/* Add this to your existing CSS */

.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 16px;
  color: #666;
}

.error-message {
  background-color: #ffe6e6;
  color: #d32f2f;
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1.5rem;
  font-size: 14px;
}

/* Update the regulations list styling */
.profile-settings .regulations-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
}

.profile-settings .regulation-item {
  display: flex;
  align-items: center;
}

.profile-settings .checkbox-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  user-select: none;
  color: #333;
  position: relative;
  padding-left: 28px; /* Make room for the checkbox */
}

.profile-settings .checkbox-container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.profile-settings .checkmark {
  position: absolute;
  left: 0;
  top: 0;
  height: 18px;
  width: 18px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 3px;
}

.profile-settings .checkbox-container:hover input ~ .checkmark {
  background-color: #f5f5f5;
}

.profile-settings .checkbox-container input:checked ~ .checkmark {
  background-color: #6c63ff;
  border-color: #6c63ff;
}

.profile-settings .checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.profile-settings .checkbox-container input:checked ~ .checkmark:after {
  display: block;
}

.profile-settings .checkbox-container .checkmark:after {
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 1rem;
}

.section-header h2 {
  margin: 0;
}

.section-header .loading-spinner {
  width: 18px;
  height: 18px;
  border-width: 2px;
}

.loading-message {
  color: #666;
  font-size: 14px;
  padding: 8px 0;
}

/* Add button loading styles */
.button-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.button-loading .loading-spinner {
  width: 16px;
  height: 16px;
  border-width: 2px;
  border-color: rgba(255, 255, 255, 0.3);
  border-top-color: white;
}

/* Improve styles for disabled checkboxes */
.checkbox-container input:disabled ~ .checkmark {
  background-color: #e9ecef;
  border-color: #ced4da;
  cursor: not-allowed;
}

.checkbox-container input:disabled ~ .checkmark:after {
  border-color: #adb5bd;
}

/* Make the entire label show not-allowed cursor when checkbox is disabled */
.checkbox-container input:disabled ~ .checkmark,
.checkbox-container input:disabled ~ span,
.regulation-item:has(input:disabled) {
  cursor: not-allowed !important;
}

/* Style for the regulation item text when disabled */
.checkbox-container input:disabled ~ span {
  color: #6c757d;
  opacity: 0.8;
}

/* Add a subtle visual indication that the item is disabled */
.regulation-item:has(input:disabled) {
  opacity: 0.85;
}

/* Ensure the admin-only badge is properly styled */
.admin-only-badge {
  font-size: 12px;
  background-color: #e9ecef;
  color: #6c757d;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: normal;
  margin-left: 8px;
  display: inline-block;
}

/* Make the regulations heading display properly */
.regulations-heading {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

/* Admin notice styling */
.admin-notice {
  background-color: #f8f9fa;
  border-left: 3px solid #6c757d;
  padding: 10px 15px;
  margin-bottom: 20px;
  border-radius: 0 4px 4px 0;
}

.admin-notice p {
  margin: 0;
  font-size: 14px;
  color: #495057;
}


