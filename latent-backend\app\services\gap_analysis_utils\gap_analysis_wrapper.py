import os
import sys
import json
from typing import List, Dict, Union, Optional
import pandas as pd
import asyncio
from app.services.error_logger import log_interaction

# Add the project root to the path
current_file = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_file)))
sys.path.insert(0, project_root)

# Use relative imports from the current package
from app.services.gap_analysis_utils.config import Config
from app.services.gap_analysis_utils.llm_interact import LLMInteract
from app.services.gap_analysis_utils.sop_gap_analysis import SOPGapAnalyzer
from app.services.gap_analysis_utils.sop_gap_steps_generator_standalone import SOPGapStepGenerator, GapStep

# Update path to point to user_config.yaml in the services folder
services_dir = os.path.dirname(os.path.dirname(current_file))
cfg = Config(os.path.join(services_dir, "gap_analysis_utils", "user_config.yaml"))

async def analyze_sop(
    sop_text: str,
    regulation_documents: List[str],
    model: str = "gemini-20-flash",
    output_file: Optional[str] = None,
    request_id: str = None
) -> Dict:
    """
    Analyze SOP text against regulatory documents to identify gaps and generate missing steps.
    
    Args:
        sop_text: The text content of the SOP to analyze
        regulation_documents: List of regulatory document texts to compare against
        model: The LLM model to use (default: "gemini-20-flash")
        output_file: Optional path to save results as JSON
        request_id: Optional ID for logging
        
    Returns:
        Dictionary containing gaps and missing steps
    """
    try:
        if request_id:
            await log_interaction(request_id, "analyze_sop_start", "Starting analyze_sop", params={"model": model})
        llm = LLMInteract(config=cfg)
        gap_analyzer = SOPGapAnalyzer(llm, model=model)
        step_generator = SOPGapStepGenerator(llm)
        if request_id:
            await log_interaction(request_id, "analyze_sop_gaps_attempt", "Calling analyze_sop_gaps", params={})
        gaps = await gap_analyzer.analyze_sop_gaps(sop_text, regulation_documents, request_id=request_id)
        if request_id:
            await log_interaction(request_id, "analyze_sop_gaps_success", "analyze_sop_gaps completed", response={"gaps": gaps})
        if not gaps:
            if request_id:
                await log_interaction(request_id, "no_gaps", "No gaps identified", response={})
            return {'gaps': [], 'gap_steps': []}
        if request_id:
            await log_interaction(request_id, "generate_missing_steps_attempt", "Calling generate_missing_steps", params={})
        gap_steps = await step_generator.generate_missing_steps(gaps, sop_text, request_id=request_id)
        if request_id:
            await log_interaction(request_id, "generate_missing_steps_success", "generate_missing_steps completed", response={"gap_steps": [step.dict() for step in gap_steps]})
        results = {
            'gaps': gaps,
            'gap_steps': [step.dict() for step in gap_steps]
        }
        if output_file:
            with open(output_file, 'w') as f:
                json.dump(results, f, indent=2)
            if request_id:
                await log_interaction(request_id, "output_file_saved", "Results saved to output file", params={"output_file": output_file})
        if request_id:
            await log_interaction(request_id, "analyze_sop_end", "Finished analyze_sop", response={"gaps": gaps, "gap_steps": [step.dict() for step in gap_steps]})
        return results
    except Exception as e:
        print(f"Exception in analyze_sop: {e}")
        if request_id:
            await log_interaction(request_id, "analyze_sop_exception", "Exception in analyze_sop", status="error", response={"error": str(e), "real_error": repr(e), "gaps": gaps if 'gaps' in locals() else None, "gap_steps": [step.dict() for step in gap_steps] if 'gap_steps' in locals() else None})
        raise Exception("A temporary issue occurred while analyzing the SOP.")


# Example usage
if __name__ == "__main__":
    project_root = os.path.dirname(os.path.abspath(__file__))
    guidelines = []
    guidelines_dir = os.path.join(project_root, "demo_guidelines")
    
    if os.path.exists(guidelines_dir):
        for filename in os.listdir(guidelines_dir):
            if filename.endswith('.txt'):
                with open(os.path.join(guidelines_dir, filename), 'r') as f:
                    guidelines.append(f.read())
    
    # Check if guidelines were found
    if not guidelines:
        print("No guidelines found. Please add .txt files to the demo_guidelines directory.")
        sys.exit(1)
    
    # Get SOP text from sop.txt
    sop_path = os.path.join(project_root, '../demo_sops', "sop.txt")
    if os.path.exists(sop_path):
        with open(sop_path, 'r') as f:
            sop_text = f.read()
    else:
        print("Error: sop.txt not found in the current directory.")
        print("Please create a sop.txt file in the same directory as this script.")
        sys.exit(1)
    
    # Analyze SOP
    results = analyze_sop(
        sop_text=sop_text,
        regulation_documents=guidelines,
        model="gemini-20-flash",  # Change to other models if needed
        output_file="gap_analysis_results.json"
    )
    
    # Print summary
    print("\nAnalysis complete!")
    print(f"Found {len(results['gaps'])} gaps")
    print(f"Generated {len(results['gap_steps'])} missing steps")
    print("Results saved to gap_analysis_results.json") 