I. INTRODUCTION
This guidance is intended to help manufacturers implementing modern quality systems and risk management approaches to meet the requirements of the Agency's current good manufacturing practice (CGMP) regulations (2l CFR parts 210 and 211). The guidance describes a comprehensive quality systems (QS) model, highlighting the model's consistency with the CGMP regulatory requirements for manufacturing human and veterinary drugs, including biological drug products. The guidance also explains how manufacturers implementing such quality systems can be in full compliance with parts 210 and 211. This guidance is not intended to place new expectations on manufacturers, nor to replace the CGMP requirements. Readers are advised to always refer to parts 210 and 211 to ensure full compliance with the regulations.
FDA's guidance documents, including this guidance, do not establish legally enforceable responsibilities. Instead, guidances describe the Agency's current thinking on a topic and should be viewed only as recommendations, unless specific regulatory or statutory requirements are cited. The use of the word should in Agency guidances means that something is suggested or recommended, but not required.
II. BACKGROUND AND PURPOSE
A. Background
In August 2002, the FDA announced the Pharmaceutical CGMPs for the 21st Century Initiative. In that announcement, the FDA explained the Agency’s intent to integrate quality systems and risk management approaches into its existing programs with the goal of encouraging industry to adopt modern and innovative manufacturing technologies. The CGMP initiative was spurred by the fact that since 1978, when the last major revision of the CGMP regulations was published,
1 This guidance was developed by the Office of Compliance in the Center for Drug Evaluation and Research (CDER) in cooperation with the Center for Biologics Evaluation and Research (CBER), the Center for Veterinary Medicine (CVM), and the Office of Regulatory Affairs (ORA).
1
Contains Nonbinding Recommendations
there have been many advances in manufacturing science and in our understanding of quality systems. In addition, many pharmaceutical manufacturers are already implementing comprehensive, modern quality systems and risk management approaches. This guidance is intended to help manufacturers implementing modern quality systems and risk management approaches to meet the requirements of the Agency's CGMP regulations. The Agency also saw a need to harmonize the CGMPs with other non-U.S. pharmaceutical regulatory systems and with FDA’s own medical device quality systems regulations. This guidance supports these goals. It also supports the objectives of the Critical Path Initiative, which intends to make the development of innovative medical products more efficient so that safe and effective therapies can reach patients sooner.
The CGMPs for the 21st Century Initiative steering committee created a Quality System Guidance Development working group (QS working group) to compare the current CGMP regulations, which call for some specific quality management elements, to other existing quality management systems. The QS working group mapped the relationship between CGMP regulations (parts 210 and 211 and the 1978 Preamble to the CGMP regulations2) and various quality system models, such as the Drug Manufacturing Inspections Program (i.e., systems-based inspectional program),3 the Environmental Protection Agency's Guidance for Developing Quality Systems for Environmental Programs, ISO Quality Standards, other quality publications, and experience from regulatory cases. The QS working group determined that, although the CGMP regulations do provide great flexibility, they do not incorporate explicitly all of the elements that today constitute most quality management systems.
The CGMP regulations and other quality management systems differ somewhat in organization and in certain constituent elements; however, they are very similar and share underlying principles. For example, the CGMP regulations stress quality control. More recently developed quality systems stress quality management, quality assurance, and the use of risk management tools, in addition to quality control. The QS working group decided that it would be very useful to examine exactly how the CGMP regulations and the elements of a modern, comprehensive quality system fit together in today's manufacturing world. This guidance is the result of that examination.
B. Goal of the Guidance
This guidance describes a comprehensive quality systems model, which, if implemented, will allow manufacturers to support and sustain robust, modern quality systems that are consistent with CGMP regulations. The guidance demonstrates how and where the elements of this comprehensive model can fit within the requirements of the CGMP regulations. The inherent flexibility of the CGMP regulations should enable manufacturers to implement a quality system in a form that is appropriate for their specific operations.
The overarching philosophy articulated in both the CGMP regulations and in robust modern quality systems is:
2 See Reference #1. 3 See Reference #2.
2
Contains Nonbinding Recommendations
Quality should be built into the product, and testing alone cannot be relied on to ensure product quality.
This guidance is intended to serve as a bridge between the 1978 regulations and our current understanding of quality systems. In addition to being part of the FDA's CGMP initiative, this guidance is being issued for a number of reasons:
•
•
•
•
A quality system addresses the public and private sectors’ mutual goal of providing a high-quality drug product to patients and prescribers. A well-built quality system should reduce the number of (or prevent) recalls, returned or salvaged products, and defective products entering the marketplace.
It is important that the CGMP regulations are harmonized to the extent possible with other widely used quality management systems, including ISO 9000, non-U.S. pharmaceutical quality management requirements, and FDA’s own medical device quality system regulations. This guidance serves as a first step to highlight common elements between the CGMP regulations and Quality Management Systems. With the globalization of pharmaceutical manufacturing and the increasing prevalence of drug- and biologic-device combination products, the convergence of quality management principles across different regions and among various product types is very desirable.
The FDA has concluded that modern quality systems, when coupled with manufacturing process and product knowledge and the use of effective risk management practices, can handle many types of changes to facilities, equipment, and processes without the need for prior approval regulatory submissions. Manufacturers with a robust quality system and appropriate process knowledge can implement many types of improvements. In addition, an effective quality system, by lowering the risk of manufacturing problems, may result in shorter and fewer FDA inspections.
A quality system can provide the necessary framework for implementing quality by design4 (building in quality from the development phase and throughout a product’s life cycle), continual improvement, and risk management in the drug manufacturing process. A quality system adopted by a manufacturer can be tailored to fit the specific environment, taking into account factors such as scope of operations, complexity of processes, and appropriate use of finite resources.
C. Scope of the Guidance
This guidance applies to manufacturers of drug products (finished pharmaceuticals), including products regulated by the Center for Biologics Evaluation and Research (CBER), the Center for Drug Evaluation and Research (CDER), and the Center for Veterinary Medicine (CVM). It may also be useful to manufacturers of components (including active pharmaceutical ingredients) used in the manufacture of these products. This document is not intended to create new requirements for pharmaceutical manufacturing that go beyond those established in the current regulations, nor is the guidance intended to be a guide for the conduct of FDA inspections. Rather, the document explains how implementing comprehensive quality systems can help manufacturers achieve compliance with 21 CFR parts
4 See ICH Q8 Pharmaceutical Development.
3
Contains Nonbinding Recommendations
210 and 211. Although the QS working group found that many of the quality system elements correlate with specific CGMP requirements, some do not. The Agency expects compliance with
CGMP regulations, and FDA’s inspection program will remain focused on compliance with those regulations.
D. Organization of this Guidance
To provide a reference familiar to industry, the quality systems model described in section IV ofthis guidance is organized — in its major sections — according to the structure of international quality standards. Major sections of the model include the following:
•••
•
Management Responsibilities
Resources
Manufacturing Operations
Evaluation Activities
Under each of these sections the key elements found in modern quality systems are discussed. When an element correlates with a CGMP regulatory requirement, that correlation is noted. In some cases, a specific CGMP regulation is discussed in more detail as it relates to a quality system element. At the end of each section, a table is included listing the quality system elements of that section and the specific CGMP regulations with which they correlate. A glossary is included at the end of the document.
III. CGMPS AND THE CONCEPTS OF MODERN QUALITY SYSTEMS
Several key concepts are critical for any discussion of modern quality systems. The following concepts are used throughout this guidance as they relate to the manufacture of pharmaceutical products.
A. Quality
Every pharmaceutical product has established identity, strength, purity, and other quality characteristics designed to ensure the required levels of safety and effectiveness. For the purposes of this guidance document, the phrase achieving quality means achieving these characteristics for a product.
B. Quality by Design and Product Development
Quality by design means designing and developing a product and associated manufacturing processes that will be used during product development to ensure that the product consistently attains a predefined quality at the end of the manufacturing process.5 Quality by design, in conjunction with a quality system, provides a sound framework for the transfer of product knowledge and process understanding from drug development to the commercial manufacturing processes and for post-development changes and optimization. The CGMP regulations, when
5 See ICH-Q8 Pharmaceutical Development.
4
Contains Nonbinding Recommendations
viewed in their entirety, incorporate the concept of quality by design. This guidance describes how these elements fit together.
C. Quality Risk Management
Quality risk management is a valuable component of an effective quality systems framework.6 Quality risk management can, for example, help guide the setting of specifications and process parameters for drug manufacturing, assess and mitigate the risk of changing a process or specification, and determine the extent of discrepancy investigations and corrective actions.
D. CAPA (Corrective and Preventive Action)
CAPA is a well-known CGMP regulatory concept that focuses on investigating, understanding, and correcting discrepancies while attempting to prevent their recurrence. Quality system models discuss CAPA as three separate concepts, all of which are used in this guidance.
••
•
Remedial corrections of an identified problem
Root cause analysis with corrective action to help understand the cause of the deviation and potentially prevent recurrence of a similar problem
Preventive action to avert recurrence of a similar potential problem
E. Change Control
Change control is another well-known CGMP concept that focuses on managing change to prevent unintended consequences. The CGMP regulations provide for change control primarily through the assigned responsibilities of the quality control unit. Certain major manufacturing changes (e.g., changes that alter specifications, a critical product attribute or bioavailability) require regulatory filings and prior regulatory approval (21 CFR 314.70, 514.8, and 601.12).
Effective change control activities (e.g., quality planning and control of revisions to specifications, process parameters, procedures) are key components of any quality system. In this guidance, change is discussed in terms of creating a regulatory environment that encourages change towards continual improvement. This means a manufacturer is empowered to make changes subject to the regulations based on the variability of materials used in manufacturing and process improvements resulting from knowledge gained during a product’s lifecycle.
F. The Quality Unit
Many of the modern quality system concepts described here correlate very closely with the CGMP regulations (refer to the charts later in the document). Current industry practice generally divides the responsibilities of the quality control unit (QCU), as defined in the CGMP regulations, between quality control (QC) and quality assurance (QA) functions.
•
QC usually involves (1) assessing the suitability of incoming components, containers,
closures, labeling, in-process materials, and the finished products; (2) evaluating the
6 See ICH Q9 Quality Risk Management.
5
Contains Nonbinding Recommendations
performance of the manufacturing process to ensure adherence to proper specifications and limits; and (3) determining the acceptability of each batch for release.
•
QA primarily involves (1) review and approval of all procedures related to production and maintenance, (2) review of associated records, and (3) auditing and performing/evaluating trend analyses.
This guidance uses the term quality unit7 (QU) to reflect modern practice while remaining consistent with the CGMP definition in § 210.3(b)(15). The concept of a quality unit is also consistent with modern quality systems in ensuring that the various operations associated with all systems are appropriately planned, approved, conducted, and monitored.
The CGMP regulations specifically assign the QU the authority to create, monitor, and implement a quality system. Such activities do not substitute for, or preclude, the daily responsibility of manufacturing personnel to build quality into the product. The QU should not take on the responsibilities of other units of a manufacturer’s organization, such as the responsibilities handled by manufacturing personnel, engineers, and development scientists.8 Manufacturing personnel and the QU are both critical in fulfilling the manufacturer’s responsibility to produce quality products.
Other CGMP assigned responsibilities of the QU are consistent with modern quality system approaches (§ 211.22):
•
•
•
•
Ensuring that controls are implemented and completed satisfactorily during manufacturing operations
Ensuring that developed procedures and specifications are appropriate and followed, including those used by a firm under contract to the manufacturer
Approving or rejecting incoming materials, in-process materials, and drug products
Reviewing production records and investigating any unexplained discrepancies
Under a quality system, it is normally expected that the product and process development units, the manufacturing units, and the QU will remain independent. In very limited circumstances, a single individual can perform both production and quality functions. That person is still accountable for implementing all the controls and reviewing the results of manufacture to ensure that product quality standards have been met. Under such circumstances, it is recommended that another qualified individual, not involved in the production operation, conduct an additional, periodic review of QU activities.
G. Six-system Inspection Model
The FDA's Drug Manufacturing Inspection Compliance Program, which contains instructions to FDA personnel for conducting inspections, is a systems-based approach to inspection and is very
7 Generally, the term quality unit is used in this guidance. However, quality control unit is used when directly quoting parts 210 and 211. 8 See Reference #1, comment 91.
6
Contains Nonbinding Recommendations
consistent with the robust quality system model presented in this guidance.
9 The diagram below shows the relationship among the six systems: the quality system and the five manufacturing systems. The quality system provides the foundation for the manufacturing systems that are linked and function within it. The quality system model described in this guidance does not consider the five manufacturing systems as discrete entities, but instead integrates them into appropriate sections of the model. Those familiar with the six-system inspection approach will see organizational differences in this guidance; however, the inter-relationship should be readily apparent. One of the important themes of the systems based inspection compliance program is that you have the ability to assess whether each of the systems is in a state of control. The quality system model presented in this guidance will also serve to help firms achieve this state of control.
9 See Reference #2; This inspectional approach is currently in use by CDER and by CBER for blood and blood product inspections. CBER and CVM are developing a similar approach for drug product inspections. 7
Contains Nonbinding Recommendations
IV. THE QUALITY SYSTEMS MODEL
The goal of this section is to describe a model for use in pharmaceutical manufacturing that can help manufacturers comply with the CGMP regulations. It should be noted that implementing an effective quality system in a manufacturing organization will require a significant investment of time and resources. However, we believe the long-term benefits of implementing a quality system will outweigh the costs.10
This section describes a robust quality systems model that, if properly implemented, can provide the controls to consistently produce a product of acceptable quality. Where applicable, the relationship between elements of this model and CGMP regulations is noted. At the end of each section, a table shows how the specific CGMP regulations correlate to the elements in the quality systems model. As already explained, many of the quality systems elements correlate closely with the CGMP regulations. It is important to emphasize that this guidance is not recommending new regulatory requirements. The guidance is intended to provide recommendations to manufacturers who are implementing, or plan to implement, a quality systems model to help them comply with CGMP regulations. FDA regulatory and inspectional coverage will remain focused on the specific CGMP regulations.
The model is described according to four major factors: •••
•
Management Responsibilities
Resources
Manufacturing Operations
Evaluation Activities
In each of the sections that follow, the specific elements of a robust modern quality systems model are described. When elements of the quality systems model correlate with specific CGMP regulations, this correlation is noted.
A. Management Responsibilities
Modern robust quality systems models call for management to play a key role in the design, implementation, and management of the quality system. For example, management is responsible for establishing the quality system structure appropriate for the specific organization. Management has ultimate responsibility to provide the leadership needed for the successful functioning of a quality system. This section describes management's role in developing, implementing, and managing a robust quality system. There is some overlap with the CGMP regulations in this section (see the table at the end of the section).
1. Provide Leadership
In a robust, modern quality system, senior management should demonstrate commitment to developing and maintaining their quality system. Quality system plans should be aligned with a manufacturer’s strategic plans to ensure that the system is part of the manufacturer’s mission and quality strategies. For example, quality systems departments normally have equal standing with
10 See Reference #3.
8
Contains Nonbinding Recommendations
other departments within an organization. Quality systems staff are effectively integrated into manufacturing activities and are involved in activities such as nonconformance investigations. Senior managers set implementation priorities and develop action plans. All levels of management can provide support of the quality system by:
•
•
•
Actively participating in system design, implementation, and monitoring, including system review (see IV.A.5.)
Advocating continual improvement of operations of the quality system
Committing necessary resources
In a robust quality systems environment, all managers should demonstrate strong and visible support for the quality system and ensure its implementation throughout the organization (e.g., across multiple sites).
All managers should encourage internal communication on quality issues at all levels in the organization. Communication should be ongoing among research and development, regulatory affairs, manufacturing, and QU personnel on issues that affect quality, with management included whenever appropriate.
2. Structure the Organization
When designing a robust quality system, management has the responsibility to structure the organization and ensure that assigned authorities and responsibilities support the production, quality, and management activities needed to produce quality products. Senior managers have the responsibility to ensure that the organization’s structure is documented.
All managers have the responsibility to communicate employee roles, responsibilities, and authorities within the system and ensure that interactions are defined and understood.
An organization also has the responsibility to give the individual who is appointed to manage the quality system the authority to detect problems and implement solutions. Usually, a senior manager administers the quality system and can, thus, ensure that the organization receives prompt feedback on quality issues.
3. Build Your Quality System to Meet Requirements
Implementing a robust quality system can help ensure compliance with CGMP regulations related to drug safety, identity, strength, quality, and purity. Under the quality systems model, the Agency recommends that senior managers ensure that the quality system that is designed and implemented provides clear organizational guidance and facilitates systematic evaluation of issues. For example, according to the model, when documenting the implementation of a quality system, the following should be addressed:
•
•
•
The quality standard that will be followed
The scope of the quality system, including any outsourcing (see IV.B.4.)
The manufacturer’s policies to implement the quality systems criteria and the supporting objectives (see IV.A.4.)
9
Contains Nonbinding Recommendations
•
The procedures needed to establish and maintain the quality system
It is recommended under a modern quality systems approach that a formal process be established to change procedures in a controlled manner. It is also recommended that, when operating under a quality system, manufacturers develop and document control procedures to complete, secure, protect, and archive records, including data, which provide evidence of operational and quality system activities. This approach is consistent with the CGMP regulations, which require manufacturers to establish and follow scientifically sound and appropriate written controls for specifications, plans, and procedures that direct operational and quality system activities and to ensure that these directives are accurate, appropriately reviewed and approved, and available for use (see the CGMPs at § 211.22 (c) and (d)).
4. Establish Policies, Objectives, and Plans
Policies, objectives, and plans under a modern quality system provide the means by which senior managers articulate their vision of and commitment to quality to all levels of the organization.
Under a quality system, senior management should incorporate a strong commitment to quality into the organizational mission. Senior managers should develop an organizational quality policy that aligns with this mission; commit to meeting requirements and improving the quality system; and propose objectives to fulfill the quality policy. Under a quality system, to make the policy relevant, it must be communicated to, and understood by, personnel and contractors (if applicable) and revised, as needed.
Managers operating within a quality system should define the quality objectives identified for implementing the quality policy. Senior management should ensure that the quality objectives are created at the top level of the organization (and other levels as needed) through a formal quality planning process. Objectives are typically aligned with the manufacturer’s strategic plans. A quality system seeks to ensure that managers support the objectives with necessary resources and have measurable goals that are monitored regularly.
Under a quality systems approach, managers would use quality planning to identify and allocate resources and define methods to achieve the quality objectives. Quality system plans should be documented and communicated to personnel to ensure awareness of how their operational activities are aligned with strategic and quality goals.
5. Review the System
System review is a key component in any robust quality system to ensure its continuing suitability, adequacy, and effectiveness. Under a quality system, senior managers should conduct reviews of the quality system’s performance according to a planned schedule. Such a review typically includes assessments of the process, product, and customer needs (in this section, customer is defined as the recipient of the product and the product is the goods or services provided). Under a quality systems approach, a review should consider at least the following:
•
•
The appropriateness of the quality policy and objectives
The results of audits and other assessments
10
Contains Nonbinding Recommendations
•
•
•
•
•
•
Customer feedback, including complaints
The analysis of data trending results
The status of actions to prevent a potential problem or a recurrence
Any follow-up actions from previous management reviews
Any changes in business practices or environment that may affect the quality system (such as the volume or type of operations)
Product characteristics meeting the customer’s needs
When developing and implementing new quality systems, reviews should take place more frequently than when the system has matured. Outside of scheduled reviews, the quality system should typically be included as a standing agenda item in general management meetings. In addition, a periodic review performed by a qualified source, external to the organization, may also be useful in assessing the suitability and effectiveness of the system.
Review outcomes typically include:
•
•
•
Improvements to the quality system and related quality processes
Improvements to manufacturing processes and products
Realignment of resources
Under a quality system, the results of a management review would typically be recorded. Planned actions should be implemented using effective corrective and preventive action and change control procedures.
The following table shows how the CGMP regulations correlate to specific elements in the quality systems model for this section. Manufacturers should always refer to the specific regulations to make sure they are in compliance.
11
Contains Nonbinding Recommendations
21 CFR CGMP Regulations Related to Management Responsibilities
Quality SystemElement
Regulatory Citations
1. Leadership
—
2. Structure
Establish quality function: § 211.22 (a) (see definition § 210.3(b)(15))
Notification: § 211.180(f)
3. Build QS
QU procedures: § 211.22(d)
reinforcement in: §§ 211.100(a), 211.160(a) QU procedures, specifications: § 211.22(c), with
QU control steps: § 211.22(a), with reinforcement in §§ 211.42(c), 211.84(a), 211.87, 211.101(c)(1), 211.110(c), 211.115(b), 211.142, 211.165(d), 211.192
QU quality assurance; review/investigate: §§ 211.22(a), 211.100(a-b) 211.180(f), 211.192, 211.198(a)
Record control: §§ 211.180(a-d), 211.180(c), 211.180(d), 211.180(e), 211.186, 211.192, 211.194, 211.198(b)
4. Establish Policies, Objectives and Plans
Procedures: §§ 211.22(c-d), 211.100(a)
5. System Review
Record review: §§ 211.100, 211.180(e), 211.192, 211.198(b)(2)
B. Resources
Appropriate allocation of resources is key to creating a robust quality system and complying with the CGMP regulations. This section discusses the role of resources in developing, implementing, and managing a robust quality system that complies with the CGMP regulations.
1. General Arrangements
Under a robust quality system, sufficient resources should be allocated for quality system and operational activities. Under the model, senior management, or a designee, should be responsible for providing adequate resources for the following:
•
•
•
•
To supply and maintain the appropriate facilities and equipment to consistently manufacture a quality product
To acquire and receive materials that are suitable for their intended purpose
For processing the materials to produce the finished drug product
For laboratory analysis of the finished drug product, including collection, storage, and examination of in-process, stability, and reserve samples
12
Contains Nonbinding Recommendations
2. Personnel Development
Under a quality system, senior management should support a problem-solving and communicative organizational culture. Managers should encourage communication by creating an environment that values employee suggestions and acts on suggestions for improvement. Management should also develop cross-functional groups to share ideas to improve procedures and processes.
In a quality system, personnel should be qualified to do the operations that are assigned to them in accordance with the nature of, and potential risk of, their operational activities. Under a quality system, managers should define appropriate qualifications for each position to help ensure that individuals are assigned appropriate responsibilities. Personnel should also understand the effect of their activities on the product and the customer. Although QU personnel should not take on the responsibilities of other units of the organization, these personnel should be selected based on their scientific and technical understanding, product knowledge, process knowledge and/or risk assessment abilities to appropriately execute certain quality functions (this quality systems feature is also found in the CGMP regulations, which identify specific qualifications, such as education, training, and experience or any combination thereof (see § 211.25(a) and (b)).
Under a quality system, continued training is critical to ensure that the employees remain proficient in their operational functions and in their understanding of CGMP regulations. Typical quality systems training should address the policies, processes, procedures, and written instructions related to operational activities, the product/service, the quality system, and the desired work culture (e.g., team building, communication, change, behavior). Under a quality system (and the CGMP regulations), training should focus on both the employees’ specific job functions and the related CGMP regulatory requirements.
Under a quality system, managers are expected to establish training programs that include the following:
•
•
•
•
Evaluation of training needs
Provision of training to satisfy these needs
Evaluation of effectiveness of training
Documentation of training and/or re-training
When operating in a robust quality system environment, it is important that managers verify that skills gained from training are implemented in day-to-day performance.
3. Facilities and Equipment
Under a quality system, the technical experts (e.g., engineers, development scientists), who have an understanding of pharmaceutical science, risk factors, and manufacturing processes related to the product, are responsible for defining specific facility and equipment requirements. 13
Contains Nonbinding Recommendations
21 CFR CGMP Regulations Related to Resources
Quality System Element Regulatory Citation
1. General Arrangements
2. Develop Personnel
Qualifications: § 211.25(a)
Staff training: § 211.25(a-b)
211.58, 211.173
211.63 – —
and Equipment
Staff number: § 211.25(c)
Under the CGMP regulations, the quality unit (QU) has the responsibility of reviewing and approving all initial design criteria and procedures pertaining to facilities and equipment and any subsequent changes (§ 211.22(c)).
Under the CGMP regulations, equipment must be qualified, calibrated, cleaned, and maintained to prevent contamination and mix-ups (§§ 211.63, 211.67, 211.68). Note that the CGMP regulations require a higher standard for calibration and maintenance than most non-pharmaceutical quality system models. The CGMP regulations place as much emphasis on process equipment as on testing equipment (§§ 211.160, 211.63, 211.67, and 211.68) while most quality systems focus only on testing equipment.11
4. Control Outsourced Operations
Outsourcing involves hiring a second party under a contract to perform the operational processes that are part of a manufacturer’s inherent responsibilities. For example, a manufacturer may hire another firm to package and label or perform CGMP regulatory training. Quality systems call for contracts (quality agreements) that clearly describe the materials or service, quality specification responsibilities, and communication mechanisms.
Under a quality system, the manufacturer should ensure that a contract firm is qualified before signing a contract with that firm. The contract firm’s personnel should be adequately trained and monitored for performance according to their quality system, and the contract firm's and contracting manufacturer’s quality standards should not conflict. It is critical in a quality system to ensure that the management of the contractor be familiar with the specific requirements of the contract. However, under the CGMP requirements, the manufacturer’s QU is responsible for approving or rejecting products or services provided under a contract (§ 211.22(a)).
As the following table illustrates, the CGMP regulations are consistent with the elements of a quality system in many areas in this section. However, manufacturers should always refer to the specific regulations to ensure that they are complying with all regulations.
3. Facilities andEquipment
Buildings and facilities: §§ 211.22(b), 211.28(c),
211.42 – 211.58, 211.173
Equipment: §§ 211.63–211.72, 211.105, 211.160(b)(4),
211.182
Lab facilities: § 211.22(b)
11 See Reference # 4.
14
Contains Nonbinding Recommendations
•
•
•
•
•
•
•
4. Control Outsourced Operations
Consultants:§ 211.34
Outsourcing: § 211.22(a)
C. Manufacturing
Significant overlap exists between the elements of a quality system and the CGMP regulation requirements for manufacturing operations. It is important to emphasize again that FDA’s enforcement programs and inspectional coverage remain based on the CGMP regulations. When quality system elements in this section do not correlate to the CGMP regulations, the guidance makes recommendations to help facilitate compliance with the CGMP regulations. The language in this section has been tailored to the pharmaceutical manufacturing environment.
1. Design, Develop, and Document Product and Processes
In a modern quality systems manufacturing environment, the significant characteristics of the product being manufactured should be defined from design to delivery, and control should be exercised over all changes. In addition, quality and manufacturing processes and procedures — and changes to them — must be defined, approved, and controlled (§ 211.100). It is important to establish responsibility for designing or changing products. Documenting processes, associated controls, and changes to these processes will help ensure that sources of variability are identified.
Documentation includes:
Resources and facilities used
Procedures to carry out the process
Identification of the process owner who will maintain and update the process as needed
Identification and control of important variables
Quality control measures, necessary data collection, monitoring, and appropriate controls for the product and process
Any validation activities, including operating ranges and acceptance criteria
Effects on related process, functions, or personnel
As discussed under section IV.A., above, the model calls for managers to ensure that product specifications and process parameters are determined by the appropriate technical experts (e.g., engineers, development scientists). In the pharmaceutical environment, experts would have an understanding of pharmaceutical science, equipment, facilities, and process types and of how variations in materials and processes can ultimately affect the finished product.
Packaging and labeling controls, critical stages in the pharmaceutical manufacturing process, are not specifically addressed in quality systems models. However, the Agency recommends that manufacturers always refer to the packaging and labeling control regulations at § 211 Subpart G. In addition — and this is consistent with modern quality systems — FDA recommends that, as
15
Contains Nonbinding Recommendations
part of the design process, before commercial production, the controls for all processes within the packaging and labeling system be planned and documented in written procedures. The procedures should outline quality control activities and the responsible positions. Specifications and controls for the packaging and labeling materials should also be determined before commercial production. Distinct labels with discriminating features for different products, such as a product marketed with different strengths, should be included to prevent mislabeling and resulting recalls.
2. Examine Inputs
In a modern quality systems model, the term input includes any material that goes into a final product, no matter whether the material is purchased by the manufacturer or produced by the manufacturer for the purpose of processing. Materials can include items such as components (e.g., ingredients, process water, and gas), containers, and closures. A robust quality system will ensure that all inputs to the manufacturing process are reliable because quality controls will have been established for the receipt, production, storage, and use of all inputs.
The CGMP regulations require either testing or use of a certificate of analysis (COA) plus an identity analysis (§ 211.84) for the release of materials for manufacturing. In the preamble to the CGMP regulations, these requirements were explicitly interpreted.12 The preamble states that reliability can be validated by conducting tests or examinations and comparing the results to the supplier’s COA. Sufficient initial tests should be done to establish reliability and to determine a schedule for periodic reassessment. As an essential element of purchasing controls, it is recommended that data trends for acceptance and rejection of materials be analyzed for information on supplier performance.13
The quality systems approach also calls for periodic auditing of suppliers based on risk assessment. During the audit, a manufacturer can observe the testing or examinations conducted by the supplier to help determine the reliability of the supplier’s COA. An audit should also include a systematic examination of the supplier’s quality system to ensure that reliability is maintained. It is recommended that a combination approach be used (i.e., verify the suppliers' COA through analysis and audits of the supplier). Under a quality systems approach, if full analytical testing is not done, the audit should cover the supplier’s analysis (i.e., a specific identity test is still required under § 211.84(d)(2)).
Under a quality systems approach, procedures should be established to verify that materials are from qualified sources (for application and licensed products, certain sources are specified in the submissions). Procedures should also be established to encompass the acceptance, use, or the rejection and disposition of materials produced by the facility (e.g., purified water). Systems that produce these in-house materials should be designed, maintained, qualified, and validated where appropriate to ensure that the materials meet their acceptance criteria.
12 See Reference #1, comment 239. 13 The Agency recommends that manufacturers have a measure of the variability of materials that could affect their process controls. For example, certain changes in physical properties may affect the process, which may affect a finished product’s dissolution characteristics.
16
Contains Nonbinding Recommendations
In addition, it is recommended that changes to materials (e.g., specification, supplier, or materials handling) be implemented through a change control system (certain changes require review and approval by the QU (§ 211.100(a)). It is also important to have a system in place to respond to changes in materials from suppliers so that necessary adjustments to the process can be made and unintended consequences avoided.
3. Perform and Monitor Operations
An important purpose of implementing a quality systems approach is to enable a manufacturer to more efficiently and effectively validate, perform, and monitor operations (§ 211.100(a)) and ensure that the controls are scientifically sound and appropriate. The goal of establishing, adhering to, measuring, and documenting specifications and process parameters is to objectively assess whether an operation is meeting its design and product performance objectives. In a robust quality system, production and process controls should be designed to ensure that the finished products have the identity, strength, quality, and purity they purport or are represented to possess (see, e.g., § 211.100(a)).
In a modern quality system, a design concept established during product development typically matures into a commercial design after process experimentation and progressive modification.
Risk management can help identify areas of process weakness or higher risk and factors that can influence critical quality attributes that should receive increased scrutiny. The FDA recommends that scale-up studies be used to help demonstrate that a fundamentally sound design has been fully realized. A sufficiently robust manufacturing process should be in place prior to commercial production. With proper design (see IV.C.1.) and reliable mechanisms to transfer process knowledge from development to commercial production, a manufacturer should be able to validate the manufacturing process.14 Conformance batches provide initial proof that the design of the process produces the intended product quality. Sufficient testing data will provide essential information on performance of the new process, as well as a mechanism for continual improvement. Modern equipment with the potential for continual monitoring and control can further enhance this knowledge base. Although initial commercial batches can provide evidence to support the validity and consistency of the process,15 the entire product life cycle should be addressed by the establishment of continual improvement mechanisms in the quality system.16 Thus, in accordance with the quality systems approach, process validation is not a one-time event, but an activity that continues throughout a product’s life.
As experience is gained in commercial production, opportunities for process improvements may become evident. (CGMP regulations § 211.180 require the review and evaluation of records to determine the need for any change. These records contain data and information from production that provide insight into the product’s state of control. Change control systems should provide a
14 See Reference #5. 15 Even with good design and development work, initial conformance batches will provide confidence that future batches will meet specifications only if the process is repeated within defined operating parameters, equipment tolerances, personnel practices, environmental attributes, and material quality. 16 See Reference #6.
17
Contains Nonbinding Recommendations
dependable mechanism for prompt implementation of technically sound manufacturing improvements.)
Under a quality system, written procedures are followed and deviations from them are justified and documented (CGMP requires this; see § 211.100(b)) to ensure that the manufacturer can trace the history of the product, as appropriate, concerning personnel, materials, equipment, and chronology and that processes for product release are complete and recorded.
Both the CGMP regulations (§ 211.110) and quality systems models call for the monitoring of critical processes that may be responsible for causing variability during production. For example:
•
•
Process steps must be verified by a second person (§ 211.188). Process steps can also be performed using a validated computer system. Batch production records must be prepared contemporaneously with each phase of production (§ 211.100(b)). Although time limits for production can be established when they are important to the quality of the finished product (CGMP addresses this; see § 211.111), the manufacturer should have the ability to establish production controls using in-process parameters that are based on desired process endpoints measured using real time testing or monitoring apparatus (e.g., blend until mixed vs. blend for 10 minutes).
Procedures must be in place to prevent objectionable microorganisms in finished products not required to be sterile and to prevent microbial contamination of finished products purported to be sterile. Sterilization processes must be validated for sterile drugs (§ 211.113(b)).17
Manufacturing processes must consistently meet their parameters, and in-process materials must meet acceptance criteria or limits (§ 211.110(b) and (c)) so that, ultimately, finished pharmaceutical products will meet their acceptance criteria. Under a quality system, selected data are used to evaluate the quality of a process or product. In addition, data collection can provide a means to encourage and analyze potential suggestions for improvement. A quality systems approach calls for the manufacturer to develop procedures that monitor, measure, and analyze the operations (including analytical methods and/or statistical techniques). Monitoring of the process is important due to the limitations of testing. Knowledge continues to accumulate from development through the entire commercial life of a product. Significant unanticipated variables should be detected by a well-managed quality system and adjustments implemented. Procedures should be revisited as needed to refine operational design based on new knowledge. Process understanding increases with experience and helps identify when change will lead to continual improvement. When implementing data collection procedures, consider the following:
Are data collection methods documented?
When in the product life cycle will the data be collected?
How and to whom will measurement and monitoring activities be assigned?
When should analysis and evaluation (e.g. trending) of laboratory data be performed? (see IV.D.1)
•
•
•
•
17 See Reference #7.
18
Contains Nonbinding Recommendations
•
What records should be collected?
A modern quality system approach indicates that change control is warranted when data analysis or other information reveals an area for improvement. Changes to an established process must be controlled and documented to ensure that desired attributes for the finished product will be met (§ 211.100(a)).
Change control with regard to pharmaceuticals is addressed in more detail in the CGMP regulations. When developing a process change, it is important to keep the process design and scientific knowledge of the product in mind. If major design issues are encountered through process experience, a firm may want to revisit the adequacy of the design of the manufacturing facility (§ 211.42), the design of the manufacturing equipment (§ 211.63), the design of the production and control procedures (§ 211.100), or the design of laboratory controls (§ 211.160). When implementing a change, its effect should be determined by monitoring and evaluating those specific elements that may be affected based on an understanding of the process. This approach allows the steps taken to implement a change and the effects of the change on the process to be considered systematically. Application of risk analysis may facilitate evaluating the potential effect of the change. Evaluating the effects of a change can entail additional tests or examinations of subsequent batches (e.g., additional in-process testing or additional stability studies). The quality system elements identified in this guidance, if implemented and maintained, will help a manufacturer manage change and implement continual improvement in manufacturing.
Under a quality systems approach, procedures should be in place to ensure the accuracy of test results. Test results that are out of specification may be due to testing problems or manufacturing problems and should be investigated. Any invalidation of a test result should be scientifically sound and justified.
To maintain quality, the Agency recommends that prior to completion of manufacturing, the manufacturer should consider storage and shipment requirements to meet special handling needs (in the case of pharmaceuticals, one example might be refrigeration).
Under a quality system, trends should be continually identified and evaluated. One way of accomplishing this is the use of statistical process control. The information from trend analyses can be used to continually monitor quality, identify potential variances before they become problems, bolster data already collected for the annual review, and facilitate improvement throughout the product life cycle. Process capability assessment can serve as a basis for determining the need for changes that can result in process improvements and efficiency (see IV.D.1.).
4. Address Nonconformities
A key component in any quality system is handling nonconformities and/or deviations. The investigation, conclusion, and follow-up must be documented (§ 211.192). To ensure that a product conforms to requirements and expectations, it is important to measure the process and the product attributes (e.g., specified control parameters, strength) as planned. Discrepancies may be detected during any stage of the process or during quality control activities. Not all discrepancies will result in product defects; however, it is important to document and handle
19
Contains Nonbinding Recommendations
discrepancies appropriately. A discrepancy investigation process is critical when a discrepancy is found that affects product quality (CGMP also requires this; see § 211.192).
In a quality system, it is important to develop and document procedures that define who is responsible for halting and resuming operations, recording non-conformities, investigating discrepancies, and taking remedial action. Under a quality system, if a product or process does not meet requirements, it is essential to identify and/or segregate the product so that it is not distributed to the customer. Remedial action can include any of the following:
••
•
•
Correct the non-conformity
With proper authorization, allow the product to proceed with justification of the conclusions regarding the problem’s impact
Use the product for another application where the deficiency does not affect the products’ quality
Reject the product
The corrected product or process should also be re-examined for conformance and assessed for the significance of the non-conformity (see, e.g., § 211.115). If the non-conformity is significant, based on consequences to process control, process efficiency, product quality, safety, efficacy, and product availability, it is important to evaluate how to prevent recurrence (see IV.D.4.). If an individual product that does not meet requirements has been released, the product can be recalled.18 Customer complaints must be reviewed and then investigated if a discrepancy is identified (§ 211.198).
The following table shows how the CGMP regulations correlate to specific elements in the quality systems model. Manufacturers should always refer to the specific regulations to ensure that they are complying with all regulations.
18 See 21 CFR Part 7.
20
Contains Nonbinding Recommendations
21 CFR CGMP Regulations Related to Manufacturing Operations
Quality SystemElement
Regulatory Citation
1.Design andDevelop Product and Processes
Production: § 211.100(a)
2.Examine Inputs
Materials: §§ 210.3(b), 211.80211.122,211.125 –211.94, 211.101,
3. Perform and Monitor Operations
Production: §§ 211.100, 211.103, 211.113 211.110, 211.111,
QC criteria: §§211.22(a-c), 211.115(b), 211.160(a), 211.165(d), 211.188
QC checkpoints: §§ 211.110(c) 211.22 (a),211.84(a),211.87,
4.Address Nonconformities
Discrepancy investigation: §§ 211.22(a), 211.100, 211.115, 211.192, 211.198
Recalls: 21 CFR Part 7
D. Evaluation Activities
As in the previous section, the elements of a quality system correlate closely with the requirements in the CGMP regulations. See the table at the end of the section for the specifics.
1. Analyze Data for Trends
Quality systems call for continually monitoring trends and improving systems. This can be achieved by monitoring data and information, identifying and resolving problems, and anticipating and preventing problems.
Quality systems procedures involve collecting data from monitoring, measurement, complaint handling, or other activities, and tracking this data over time, as appropriate. Analysis of data can provide indications that controls are losing effectiveness. The information generated will be essential to achieving problem resolution or problem prevention (see IV.D.3.).
Although the CGMP regulations (§ 211.180(e)) require product review on at least an annual basis, a quality systems approach calls for trending on a more frequent basis as determined by risk. Trending enables the detection of potential problems as early as possible to plan corrective and preventive actions. Another important concept of modern quality systems is the use of trending to examine processes as a whole; this is consistent with the annual review approach. Trending analyses can help focus internal audits (see IV.D.2.).
2. Conduct Internal Audits
A quality systems approach calls for audits to be conducted at planned intervals to evaluate effective implementation and maintenance of the quality system and to determine if processes and products meet established parameters and specifications. As with other procedures, audit
21
Contains Nonbinding Recommendations
procedures should be developed and documented to ensure that the planned audit schedule takes into account the relative risks of the various quality system activities, the results of previous audits and corrective actions, and the need to audit the complete system. Procedures should describe how auditors are trained in objective evidence gathering, their responsibilities, and auditing procedures. Procedures should also define auditing activities such as the scope and methodology of the audit, selection of auditors, and audit conduct (audit plans, opening meetings, interviews, closing meeting and reports). It is critical to maintain records of audit findings and assign responsibility for follow-up to prevent problems from recurring (see IV.D.3.).
The quality systems model calls for managers who are responsible for the areas audited to take timely action to resolve audit findings and ensure that follow-up actions are completed, verified, and recorded. (FDA’s policy is to refrain from both reviewing and copying reports or records that result from internal audits per Compliance Policy Guide 130.300.19)
3. Quality Risk Management
Effective decision-making in a quality systems environment is based on an informed understanding of quality issues. Elements of risk should be considered relative to intended use of a product, and in the case of pharmaceuticals, patient safety and ensuring availability of medically necessary drug products. Management should assign priorities to activities or actions based on an assessment of the risk including both the probability of occurrence of harm and of the severity of that harm. It is important to engage appropriate parties in assessing the risk. Such parties include customers, appropriate manufacturing personnel, and other stakeholders. Implementation of quality risk management includes assessing the risks, selecting and implementing risk management controls commensurate with the level of risk, and evaluating the results of the risk management efforts. Since risk management is an iterative process, it should be repeated if new information is developed that changes the need for, or nature of, risk management.
In a manufacturing quality systems environment, risk management is used as a tool in the development of product specifications and critical process parameters. Used in conjunction with process understanding, quality risk management helps manage and control change.
4. Corrective Action
Corrective action is a reactive tool for system improvement to ensure that significant problems do not recur. Both quality systems and the CGMP regulations emphasize corrective actions. Quality systems approaches call for procedures to be developed and documented to ensure that the need for action is evaluated relevant to the possible consequences, the root cause of the problem is investigated, possible actions are determined, a selected action is taken within a defined timeframe, and the effectiveness of the action taken is evaluated. It is essential to document corrective actions taken (CGMP also requires this; see § 211.192).
It is essential to determine what actions will reduce the likelihood of a problem recurring. Examples of sources that can be used to gather such information include the following:
19 See Reference #8.
22
Contains Nonbinding Recommendations
••
•••
•
Nonconformance reports and rejections
Returns
Complaints
Internal and external audits
Data and risk assessment related to operations and quality system processes
Management review decisions
5. Preventive Actions
Being proactive is an essential tool in quality systems management. Succession planning, training, capturing institutional knowledge, and planning for personnel, policy, and process changes are preventive actions that will help ensure that potential problems and root causes are identified, possible consequences assessed, and appropriate actions considered.
The selected preventive action should be evaluated and recorded, and the system should be monitored for the effectiveness of the action. Problems can be anticipated and their occurrence prevented by reviewing data and analyzing risks associated with operational and quality system processes, and by keeping abreast of changes in scientific developments and regulatory requirements.
6.
Promote Improvement
The effectiveness and efficiency of a quality system can be improved through the quality activities described in this guidance. Management may choose to use other improvement activities as appropriate. It is critical that senior management be involved in the evaluation of this improvement process (see IV.D.3.).
The following table shows how the CGMP regulations correlate to specific elements in the quality systems model for this section. Manufacturers should always refer to the specific regulations to make sure they are complying with all regulations.
23
Contains Nonbinding Recommendations
21 CFR CGMP Regulations Related to Evaluation Activities
Quality System Element
Regulatory Citation
1.Analyze DataforTrends
Annual Review: § 211.180(e)
2. Conduct Internal Audits
--
3. RiskAssessment
4.Corrective Action
Discrepancy investigation: §§ 211.22(a), 211.192
5.Preventive Action
—
6. Promote Improvement
§ 211.110
V. CONCLUSION
Implementation of a comprehensive quality systems model for human and veterinary pharmaceutical products, including biological products, will facilitate compliance with 21 CFR parts 210 and 211. The central goal of a quality system is the consistent production of safe and effective products and ensuring that these activities are sustainable. Quality professionals are aware that good intentions alone will not ensure good products. A robust quality system will promote process consistency by integrating effective knowledge-building mechanisms into daily operational decisions. Specifically, successful quality systems share the following characteristics, each of which has been discussed in detail above:
•
•
•
•
•
•
•
•
Science-based approaches
Decisions based on an understanding of the intended use of a product
Proper identification and control of areas of potential process weakness
Responsive deviation and investigation systems that lead to timely remediation
Sound methods for assessing and reducing risk
Well-defined processes and products, starting from development and extending throughout the product life cycle
Systems for careful analysis of product quality
Supportive management (philosophically and financially)
Both good manufacturing practice and good business practice require a robust quality system. When fully developed and effectively managed, a quality system will lead to consistent, predictable processes that ensure that pharmaceuticals are safe, effective, and available for the consumer.
24
Contains Nonbinding Recommendations
