import React, { useState, useEffect } from 'react';
import Navigation from '../../common/Navigation/Navigation';
import Layout from '../../common/Layout/Layout';
import LoadingSpinner from '../../common/LoadingSpinner/LoadingSpinner';
import sopService from '../../../services/sopService';
import regulationService from '../../../services/regulationService';
import './Dashboard.css';

const Dashboard = () => {
  const [loading, setLoading] = useState(true);
  const [regulationLoading, setRegulationLoading] = useState(true);

  // Initialize stats with placeholders - removed Active Users
  const [stats, setStats] = useState([
    { title: 'Total SOPs', value: '...', color: '#666' },
    { title: 'Total Regulations', value: '...', color: '#f39c12' },
    { title: "Identified Gaps", value: '...', color: '#d63031' },
  ]);

  const updates = [
    { text: 'IT Security Policy updated', time: '2 hours ago' },
    { text: 'New user <PERSON> added', time: 'Yesterday' },
    { text: 'HR Onboarding SOP completed', time: '2 days ago' },
    { text: 'Gap identified in Finance procedures', time: '3 days ago' },
    { text: 'Quarterly compliance assessment created', time: '1 week ago' },
  ];

  // Fetch SOP count and calculate identified gaps
  useEffect(() => {
    const fetchSOPData = async () => {
      try {
        setLoading(true);
        const sops = await sopService.getAllSOPs();
        
        // Calculate total identified gaps from all SOPs
        const totalIdentifiedGaps = sops.total_identified_gaps;
        
        // Update the stats array with the actual SOP count and identified gaps
        setStats(prevStats => {
          const newStats = [...prevStats];
          newStats[0] = { ...newStats[0], value: sops.sops_data.length.toString() };
          newStats[2] = { ...newStats[2], value: totalIdentifiedGaps.toString() };
          return newStats;
        });
      } catch (err) {
        console.error('Error fetching SOP data:', err);
        
        // Update the stats array with error indicators
        setStats(prevStats => {
          const newStats = [...prevStats];
          newStats[0] = { ...newStats[0], value: 'Error' };
          newStats[2] = { ...newStats[2], value: 'Error' };
          return newStats;
        });
      } finally {
        setLoading(false);
      }
    };

    fetchSOPData();
  }, []);

  // Fetch Regulation count
  useEffect(() => {
    const fetchRegulationCount = async () => {
      try {
        setRegulationLoading(true);
        const regulations = await regulationService.getOrganizationRegulations();
        
        // Update the stats array with the actual regulation count
        setStats(prevStats => {
          const newStats = [...prevStats];
          newStats[1] = { ...newStats[1], value: regulations.length.toString() };
          return newStats;
        });
      } catch (err) {
        console.error('Error fetching regulation count:', err);
        
        // Update the stats array with an error indicator
        setStats(prevStats => {
          const newStats = [...prevStats];
          newStats[1] = { ...newStats[1], value: 'Error' };
          return newStats;
        });
      } finally {
        setRegulationLoading(false);
      }
    };

    fetchRegulationCount();
  }, []);

  return (
    <div className="dashboard">
      <Navigation />
      <main className="dashboard-content">
        <div className="welcome-section">
          <h2>Welcome back</h2>
          <p>Here's what's happening in your organization</p>
        </div>

        <div className="stats-grid">
          {stats.map((stat, index) => (
            <div key={index} className="stat-card">
              <h3>{stat.title}</h3>
              <div className="stat-value-container">
                {index === 0 && loading ? (
                  <LoadingSpinner size="small" />
                ) : index === 1 && regulationLoading ? (
                  <LoadingSpinner size="small" />
                ) : index === 2 && loading ? (
                  <LoadingSpinner size="small" />
                ) : (
                  <p style={{ color: stat.color }}>{stat.value}</p>
                )}
              </div>
            </div>
          ))}
        </div>

        <div className="recent-updates">
          <h3>Recent Updates</h3>
          <div className="updates-list">
            {updates.map((update, index) => (
              <div key={index} className="update-item">
                <span className="update-text">{update.text}</span>
                <span className="update-time">{update.time}</span>
              </div>
            ))}
          </div>
        </div>
      </main>
    </div>
  );
};

export default Dashboard;
