SOP for Process Validation
Standard operating procedure for validation and re-validation of manufacturing process to produce the quality product consistently.
1.0 OBJECTIVE
The purpose of this SOP is to lay down the minimum requirements for validation or revalidation of manufacturing processes applicable to marketed
drug products in order to provide documented evidence that each specific process will consistently yield a product meeting all quality and design
specifications.
2.0 SCOPE
Process validation may be prospective, retrospective or concurrent and is based on the assumption that equipment, environment and material
validation have been completed prior to commencing process validation for any product.
3.0 RESPONSIBILITIES
3.1 Officer-Quality Assurance
3.1.1 To prepare validation protocol
3.1.2 To ensure the activities to be followed as per the approved protocol.
3.1.3 To withdraw the sample as per sampling program.
3.2. Executive-Quality Assurance
3.2.1 To evaluate the analytical report.
3.2.2 To prepare the validation report.
3.3 Officer-Quality Control
3.3.1 To analyze the samples
3.4 Executive- Quality Control
3.4.1 To review the protocol
3.4.2 To check the result of the analysis.
3.5 Manager Quality
3.5.1 To approve the protocol
3.5.2 To co-ordinate the activity.
3.5.3 To review and approve the validation report.
3.5.4 To organize the training and impart the training before validation.
3.6 Manager-Production:
3.6.1 To review validation protocol & report & organize the activity.
3.7 Executive-Maintenance:
3.7.1 To review the protocol and provide the required services.
4.0 ACCOUNTABILITY
4.1 Head Quality Assurance
5.0 PRODUCTION
5.1 Draw up a detailed flow chart of the process to be validated with each major and minor stages being differentiated (incorporate sub-stages, if
required in the event of any major stage).
5.2 Prepare detailed flow sheet for each stage corresponding to individual operations in the process.
5.3 Define the critical factors/operations, which assure the reliability of the process.
Note: Critical factors/operations may be defined as a step(s) of the operation whose variability has to be controlled to produce a quality product.
5.4 Prepare a written validation protocol for each critical operation/stage for any related minor operation/stage.
5.5 A validation protocol includes the following:
5.5.1 Purpose
5.5.2 Scope
5.5.3 Reference & Attachments
5.5.4 Responsibility Matrix
5.5.5 Equipments required
5.5.6 Training
5.5.7 Critical stages
5.5.8 Flow diagram of the process
5.5.9 Specification of active material, In process material, Finished product.
5.5.10 Product details including batch details.
5.5.11 Operation
5.5.12 Test(s) to be performed.
5.5.13 Stability
5.5.14 Summary & conclusion
5.5.15 Approval & Comments.
5.6 Run the test as per the written protocols and under conditions conforming to the actual production condition.
5.7 Verify the performance of the critical factors by evaluation of data for conformance to specifications.
5.8 Interpret the results and in case the process performs as per the predetermined specification, consider the process validated.
5.9 For non-confirming validation data, critically scrutinize the data collected, recommend the corrective actions and revalidate the process.
5.10 File the completed validation document of the process along with the test results of analysis and interpretation confirming its satisfactory
performance in a master file for validation of the process along with such other data like stability studies for the process or product.
5.11 Get the approval of the Quality head for each validation performed.
6.0 ABBREVIATIONS
6.1 SOP: Standard Operating Procedure
Ankur Choudhary is India's first professional pharmaceutical blogger, author and founder of pharmaguideline.com, a widely-read pharmaceutical
blog since 2008. Sign-up for the free email updates for your daily dose of pharmaceutical tips.
Need Help: Ask Question
 
© Pharmaguideline, 2008-2024. All Rights Reserved.
Setup ❘ Terms of Use ❘ FAQ
