#!/usr/bin/env python3

import os
import sys
import argparse
import json
from dotenv import load_dotenv
from supabase import create_client, Client
from uuid import UUID

# Load environment variables
load_dotenv()

def setup_supabase() -> Client:
    """Initialize and return a Supabase client."""
    supabase_url = os.environ.get("SUPABASE_URL")
    supabase_key = os.environ.get("SUPABASE_SERVICE_KEY")
    
    if not supabase_url or not supabase_key:
        print("❌ Error: Missing SUPABASE_URL or SUPABASE_SERVICE_KEY environment variables")
        sys.exit(1)
    
    return create_client(supabase_url, supabase_key)

def validate_uuid(value):
    """Validate if a string is a valid UUID."""
    try:
        UUID(value)
        return True
    except ValueError:
        return False

def read_file_content(file_path):
    """Read the content of a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read()
    except Exception as e:
        print(f"❌ Error reading file: {str(e)}")
        sys.exit(1)

def add_regulation(supabase: Client, args) -> None:
    """Add a new regulation to the database."""
    try:
        data = {
            "name": args.name,
            "description": args.description,
            "industry": args.industry,
            "region": args.region,
            "compliance_type": args.compliance_type,
        }
        
        # Handle source from file if provided
        if args.source_file:
            if not os.path.exists(args.source_file):
                print(f"❌ Error: Source file '{args.source_file}' does not exist")
                sys.exit(1)
            data["source"] = read_file_content(args.source_file)
            print(f"✅ Read {len(data['source'])} characters from {args.source_file}")
        elif args.source:
            data["source"] = args.source
        
        if args.uploaded_by and validate_uuid(args.uploaded_by):
            data["uploaded_by"] = args.uploaded_by
        
        result = supabase.table("regulations").insert(data).execute()
        
        if result.data:
            print(f"✅ Regulation '{args.name}' added successfully")
            print(f"  ID: {result.data[0]['id']}")
        else:
            print(f"❌ Error adding regulation: {result.error.message if result.error else 'Unknown error'}")
            sys.exit(1)
    except Exception as e:
        print(f"❌ Error adding regulation: {str(e)}")
        sys.exit(1)

def delete_regulation(supabase: Client, regulation_id: str) -> None:
    """Delete a regulation from the database."""
    try:
        if not validate_uuid(regulation_id):
            print("❌ Error: Invalid regulation ID format")
            sys.exit(1)
            
        # First check if regulation exists
        check = supabase.table("regulations").select("id").eq("id", regulation_id).execute()
        if not check.data:
            print(f"❌ Error: Regulation with ID {regulation_id} not found")
            sys.exit(1)
            
        # Check if the regulation is associated with any organizations
        org_reg_check = supabase.table("organization_regulations").select("id").eq("regulation_id", regulation_id).execute()
        if org_reg_check.data:
            print(f"⚠️ Warning: This regulation is associated with {len(org_reg_check.data)} organization(s)")
            confirm = input("Do you want to delete these associations as well? (y/n): ")
            if confirm.lower() != 'y':
                print("Operation cancelled")
                sys.exit(0)
                
            # Delete from organization_regulations
            supabase.table("organization_regulations").delete().eq("regulation_id", regulation_id).execute()
            print(f"✅ Removed regulation associations from organization_regulations table")
            
        # Delete from regulations
        result = supabase.table("regulations").delete().eq("id", regulation_id).execute()
        
        if result.data:
            print(f"✅ Regulation with ID {regulation_id} deleted successfully")
        else:
            print(f"❌ Error deleting regulation: {result.error.message if result.error else 'Unknown error'}")
            sys.exit(1)
    except Exception as e:
        print(f"❌ Error deleting regulation: {str(e)}")
        sys.exit(1)

def update_regulation(supabase: Client, args) -> None:
    """Update an existing regulation."""
    try:
        if not validate_uuid(args.id):
            print("❌ Error: Invalid regulation ID format")
            sys.exit(1)
            
        # Check if regulation exists
        check = supabase.table("regulations").select("id").eq("id", args.id).execute()
        if not check.data:
            print(f"❌ Error: Regulation with ID {args.id} not found")
            sys.exit(1)
            
        # Build update data
        data = {}
        if args.name:
            data["name"] = args.name
        if args.description is not None:  # Allow empty descriptions
            data["description"] = args.description
        if args.industry is not None:
            data["industry"] = args.industry
        if args.region is not None:
            data["region"] = args.region
        if args.compliance_type is not None:
            data["compliance_type"] = args.compliance_type
            
        # Handle source update
        if args.source_file:
            if not os.path.exists(args.source_file):
                print(f"❌ Error: Source file '{args.source_file}' does not exist")
                sys.exit(1)
            data["source"] = read_file_content(args.source_file)
            print(f"✅ Read {len(data['source'])} characters from {args.source_file}")
        elif args.source is not None:
            data["source"] = args.source
            
        if args.uploaded_by:
            if validate_uuid(args.uploaded_by):
                data["uploaded_by"] = args.uploaded_by
            else:
                print("❌ Error: Invalid uploaded_by UUID format")
                sys.exit(1)
                
        # Add updated_at timestamp
        data["updated_at"] = "now()"
        
        if not data:
            print("❌ Error: No fields provided for update")
            sys.exit(1)
            
        result = supabase.table("regulations").update(data).eq("id", args.id).execute()
        
        if result.data:
            print(f"✅ Regulation with ID {args.id} updated successfully")
        else:
            print(f"❌ Error updating regulation: {result.error.message if result.error else 'Unknown error'}")
            sys.exit(1)
    except Exception as e:
        print(f"❌ Error updating regulation: {str(e)}")
        sys.exit(1)

def list_regulations(supabase: Client, filters=None) -> None:
    """List all regulations with optional filtering."""
    try:
        query = supabase.table("regulations").select("*")
        
        # Apply filters if any
        if filters:
            if filters.industry:
                query = query.eq("industry", filters.industry)
            if filters.region:
                query = query.eq("region", filters.region)
            if filters.compliance_type:
                query = query.eq("compliance_type", filters.compliance_type)
        
        result = query.order("name").execute()
        
        if result.data:
            print("\nRegulations:")
            print("=" * 100)
            print(f"{'ID':<36} | {'Name':<25} | {'Industry':<15} | {'Region':<15} | {'Compliance Type':<15}")
            print("-" * 100)
            
            for item in result.data:
                id_str = str(item.get('id', 'N/A'))
                name_str = str(item.get('name', 'N/A'))[:25]
                industry_str = str(item.get('industry', 'N/A'))[:15]
                region_str = str(item.get('region', 'N/A'))[:15]
                compliance_str = str(item.get('compliance_type', 'N/A'))[:15]
                
                print(f"{id_str:<36} | {name_str:<25} | {industry_str:<15} | {region_str:<15} | {compliance_str:<15}")
            
            print(f"\nTotal: {len(result.data)} regulations")
        else:
            print("No regulations found")
    except Exception as e:
        print(f"❌ Error listing regulations: {str(e)}")
        sys.exit(1)

def get_regulation(supabase: Client, regulation_id: str, save_source=None) -> None:
    """Get detailed information about a regulation."""
    try:
        if not validate_uuid(regulation_id):
            print("❌ Error: Invalid regulation ID format")
            sys.exit(1)
            
        result = supabase.table("regulations").select("*").eq("id", regulation_id).execute()
        
        if result.data and len(result.data) > 0:
            reg = result.data[0]
            print("\nRegulation Details:")
            print("=" * 50)
            print(f"ID:              {reg.get('id', 'N/A')}")
            print(f"Name:            {reg.get('name', 'N/A')}")
            print(f"Description:     {reg.get('description', 'N/A')}")
            print(f"Industry:        {reg.get('industry', 'N/A')}")
            print(f"Region:          {reg.get('region', 'N/A')}")
            print(f"Compliance Type: {reg.get('compliance_type', 'N/A')}")
            print(f"Created At:      {reg.get('created_at', 'N/A')}")
            print(f"Updated At:      {reg.get('updated_at', 'N/A')}")
            print(f"Uploaded By:     {reg.get('uploaded_by', 'N/A')}")
            
            # Source info
            source = reg.get('source', '')
            if source:
                print(f"Source:          {len(source)} characters")
                
                # Save source to file if requested
                if save_source:
                    try:
                        with open(save_source, 'w', encoding='utf-8') as f:
                            f.write(source)
                        print(f"✅ Source saved to file: {save_source}")
                    except Exception as e:
                        print(f"❌ Error saving source to file: {str(e)}")
            else:
                print("Source:          None")
            
            # Get associated organizations
            org_regs = supabase.table("organization_regulations").select("*,organizations(name)").eq("regulation_id", regulation_id).execute()
            
            if org_regs.data and len(org_regs.data) > 0:
                print("\nAssociated Organizations:")
                print("-" * 50)
                for org_reg in org_regs.data:
                    org_name = org_reg.get('organizations', {}).get('name', 'Unknown')
                    is_applicable = "Applicable" if org_reg.get('is_applicable', True) else "Not Applicable"
                    print(f"- {org_name} ({is_applicable})")
            else:
                print("\nNot associated with any organizations")
        else:
            print(f"❌ Regulation with ID {regulation_id} not found")
    except Exception as e:
        print(f"❌ Error getting regulation details: {str(e)}")
        sys.exit(1)

def main():
    """Main function to parse arguments and execute commands."""
    parser = argparse.ArgumentParser(description="Manage regulations in the database")
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")
    
    # Add command
    add_parser = subparsers.add_parser("add", help="Add a new regulation")
    add_parser.add_argument("--name", "-n", required=True, help="Regulation name")
    add_parser.add_argument("--description", "-d", help="Regulation description")
    add_parser.add_argument("--industry", "-i", help="Industry the regulation applies to")
    add_parser.add_argument("--region", "-r", help="Geographic region of the regulation")
    add_parser.add_argument("--compliance-type", "-c", help="Type of compliance")
    add_parser.add_argument("--source", "-s", help="Source text (for small content)")
    add_parser.add_argument("--source-file", "-f", help="Path to file containing regulation text")
    add_parser.add_argument("--uploaded-by", "-u", help="UUID of the user who uploaded this regulation")
    
    # Delete command
    delete_parser = subparsers.add_parser("delete", help="Delete a regulation")
    delete_parser.add_argument("id", help="UUID of the regulation to delete")
    
    # Update command
    update_parser = subparsers.add_parser("update", help="Update a regulation")
    update_parser.add_argument("id", help="UUID of the regulation to update")
    update_parser.add_argument("--name", "-n", help="New regulation name")
    update_parser.add_argument("--description", "-d", help="New regulation description")
    update_parser.add_argument("--industry", "-i", help="New industry")
    update_parser.add_argument("--region", "-r", help="New region")
    update_parser.add_argument("--compliance-type", "-c", help="New compliance type")
    update_parser.add_argument("--source", "-s", help="New source text (for small content)")
    update_parser.add_argument("--source-file", "-f", help="Path to file containing new regulation text")
    update_parser.add_argument("--uploaded-by", "-u", help="New uploader UUID")
    
    # List command
    list_parser = subparsers.add_parser("list", help="List all regulations")
    list_parser.add_argument("--industry", "-i", help="Filter by industry")
    list_parser.add_argument("--region", "-r", help="Filter by region")
    list_parser.add_argument("--compliance-type", "-c", help="Filter by compliance type")
    
    # Get command
    get_parser = subparsers.add_parser("get", help="Get details about a specific regulation")
    get_parser.add_argument("id", help="UUID of the regulation to view")
    get_parser.add_argument("--save-source", "-s", help="Save regulation source to this file path")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    supabase = setup_supabase()
    
    if args.command == "add":
        add_regulation(supabase, args)
    elif args.command == "delete":
        delete_regulation(supabase, args.id)
    elif args.command == "update":
        update_regulation(supabase, args)
    elif args.command == "list":
        list_regulations(supabase, args)
    elif args.command == "get":
        get_regulation(supabase, args.id, args.save_source)

if __name__ == "__main__":
    main()
