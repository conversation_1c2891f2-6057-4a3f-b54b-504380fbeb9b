import jwt
from datetime import datetime, timedelta
import asyncio
from fastapi.security import HTTPAuthorizationCredentials
from app.services.auth_utils import verify_token, APIResponse
import sys

class MockCredentials:
    def __init__(self, token):
        self.credentials = token

async def test_auth_utils():
    # Create test tokens
    current_time = datetime.utcnow().timestamp()
    
    # Create a valid token (expires in 1 hour)
    valid_payload = {
        "sub": "test_user",
        "exp": current_time + 3600  # 1 hour from now
    }
    
    # Create an expired token (expired 1 hour ago)
    expired_payload = {
        "sub": "test_user",
        "exp": current_time - 3600  # 1 hour ago
    }
    
    print(f"Current time: {current_time}")
    print(f"Valid exp time: {valid_payload['exp']}")
    print(f"Expired exp time: {expired_payload['exp']}")
    
    # For testing, use a simple secret
    test_secret = "test_secret"
    
    valid_token = jwt.encode(valid_payload, test_secret, algorithm="HS256")
    expired_token = jwt.encode(expired_payload, test_secret, algorithm="HS256")
    
    # Test valid token
    print("\n=== Testing with valid token ===")
    try:
        mock_credentials = MockCredentials(valid_token)
        result = await verify_token(mock_credentials)
        print(f"Successfully verified token: {result}")
    except Exception as e:
        print(f"Error with valid token: {str(e)}")
    
    # Test expired token
    print("\n=== Testing with expired token ===")
    try:
        mock_credentials = MockCredentials(expired_token)
        result = await verify_token(mock_credentials)
        print(f"Successfully verified token (should not happen): {result}")
    except Exception as e:
        print(f"Expected error with expired token: {str(e)}")
        # Check if it's the right type of error
        if hasattr(e, 'detail') and isinstance(e.detail, dict) and e.detail.get('error_code') == 'TOKEN_EXPIRED':
            print("SUCCESS: Caught TOKEN_EXPIRED error as expected!")
        else:
            print(f"FAILED: Got wrong error type: {type(e)}, details: {getattr(e, 'detail', 'No details')}")

if __name__ == "__main__":
    asyncio.run(test_auth_utils()) 