import os
import mimetypes
from fastapi import HTTPException
from azure.storage.blob import BlobServiceClient, ContentSettings
import logging

# Configure logging
logging.basicConfig(level=logging.CRITICAL)
logger = logging.getLogger(__name__)

AZURE_STORAGE_CONNECTION_STRING = os.getenv("AZURE_STORAGE_CONNECTION_STRING")
AZURE_STORAGE_CONTAINER_NAME = os.getenv("AZURE_STORAGE_CONTAINER_NAME")

# Optionally extend mimetypes with any common missing types
COMMON_MIME_OVERRIDES = {
    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
}
mimetypes.add_type('text/plain', '.txt')

for ext, mime in COMMON_MIME_OVERRIDES.items():
    mimetypes.add_type(mime, ext)

def get_content_type(filename: str, allowed_types: list[str]) -> str | None:
    """
    Get the MIME type of a file and check if it's in the list of allowed types.

    Args:
        filename (str): The name of the file.
        allowed_types (list[str]): List of allowed MIME types.

    Returns:
        str | None: The MIME type if it's allowed, otherwise None.
    """
    mime_type, _ = mimetypes.guess_type(filename)
    return mime_type if mime_type in allowed_types else None

def upload_to_azure_blob(content: bytes, blob_name: str, content_type: str) -> str:
    if not AZURE_STORAGE_CONNECTION_STRING or not AZURE_STORAGE_CONTAINER_NAME:
        logger.error("Azure Storage config missing")
        raise HTTPException(status_code=500, detail="Internal configuration error. Please try again later.")

    try:
        blob_service_client = BlobServiceClient.from_connection_string(AZURE_STORAGE_CONNECTION_STRING)
        container_client = blob_service_client.get_container_client(AZURE_STORAGE_CONTAINER_NAME)
        blob_client = container_client.get_blob_client(blob_name)

        blob_client.upload_blob(content, overwrite=True, content_settings=ContentSettings(content_type=content_type))
        logger.info('File uploaded to Azure: ' + blob_client.url)
        return blob_client.url
    except Exception as e:
        logger.exception("Azure upload failed")
        raise HTTPException(status_code=500, detail="Failed to upload file. Please try again later.")

def download_from_azure_blob(blob_name: str) -> bytes:
    """
    Download a file from Azure Blob storage.

    Args:
        blob_name (str): The name of the blob to download.

    Returns:
        bytes: The file content.
    """
    if not AZURE_STORAGE_CONNECTION_STRING or not AZURE_STORAGE_CONTAINER_NAME:
        logger.error("Azure Storage config missing")
        raise HTTPException(status_code=500, detail="Internal configuration error. Please try again later.")

    try:
        blob_service_client = BlobServiceClient.from_connection_string(AZURE_STORAGE_CONNECTION_STRING)
        container_client = blob_service_client.get_container_client(AZURE_STORAGE_CONTAINER_NAME)
        blob_client = container_client.get_blob_client(blob_name)

        download_stream = blob_client.download_blob()
        return download_stream.readall()
    except Exception as e:
        print(e)
        logger.exception("Azure download failed")
        raise HTTPException(status_code=500, detail="Failed to download file. Please try again later.")

