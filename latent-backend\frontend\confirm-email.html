<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Confirmation - Supabase Auth Demo</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <h1>Email Confirmation</h1>
        <div id="processing">
            <p>Processing your confirmation...</p>
            <div class="loader"></div>
        </div>
        <div id="confirmation-message" style="display: none;">
            <p id="message-text"></p>
            <a href="index.html" class="btn">Back to Login</a>
        </div>
    </div>

    <style>
        .loader {
            border: 5px solid #f3f3f3;
            border-radius: 50%;
            border-top: 5px solid #4c6ef5;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script>
        // Initialize the Supabase client
        const supabaseUrl = 'YOUR_SUPABASE_URL'; // Replace with your Supabase URL
        const supabaseKey = 'YOUR_SUPABASE_ANON_KEY'; // Replace with your Supabase anon key
        const supabase = supabase.createClient(supabaseUrl, supabaseKey);
        
        // DOM elements
        const processingDiv = document.getElementById('processing');
        const confirmationDiv = document.getElementById('confirmation-message');
        const messageText = document.getElementById('message-text');
        
        // Function to show confirmation result
        function showConfirmationResult(success, message) {
            processingDiv.style.display = 'none';
            confirmationDiv.style.display = 'block';
            messageText.textContent = message;
            messageText.style.color = success ? '#2b8a3e' : '#e03131';
        }
        
        // Check for confirmation token in URL
        document.addEventListener('DOMContentLoaded', async () => {
            // Get URL hash parameters
            const hashParams = new URLSearchParams(window.location.hash.substring(1));
            const accessToken = hashParams.get('access_token');
            const refreshToken = hashParams.get('refresh_token');
            const type = hashParams.get('type');
            
            if (type === 'signup' && accessToken) {
                try {
                    // Set session with tokens
                    const { data, error } = await supabase.auth.setSession({
                        access_token: accessToken,
                        refresh_token: refreshToken
                    });
                    
                    if (error) throw error;
                    
                    showConfirmationResult(true, 'Your email has been confirmed successfully! You can now log in.');
                } catch (error) {
                    showConfirmationResult(false, `Error confirming your email: ${error.message}`);
                }
            } else {
                showConfirmationResult(false, 'Invalid confirmation link. Please try signing up again.');
            }
        });
    </script>
</body>
</html> 