import os
import logging
import hashlib
import requests
import json
from typing import Optional, List, Dict, Any, Union
from contextlib import asynccontextmanager
from dataclasses import dataclass, field

# from IPython.display import Image
from app.services.gap_analysis_utils.config import Config
from anthropic import AnthropicVertex
from app.services.gap_analysis_utils.anthropic_client import AnthropicClient
from app.services.gap_analysis_utils.google_genai_client import GoogleGenAIClient
from openai import AzureOpenAI
# import chromadb

from google.genai import types
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
    before_sleep_log,
    wait_random_exponential
)
from app.services.error_logger import log_interaction_sync

@dataclass
class LLMConfig:
    """Configuration for LLM clients"""
    max_retries: int = 5
    base_delay: float = 1.0
    max_delay: float = 60.0
    jitter: bool = True
    timeout: float = 30.0
    rate_limit_rpm: int = 60  # Conservative default for free tier

def create_retry_decorator(config: LLMConfig):
    """Create a retry decorator with exponential backoff"""
    if config.jitter:
        wait_strategy = wait_random_exponential(
            multiplier=config.base_delay,
            max=config.max_delay
        )
    else:
        wait_strategy = wait_exponential(
            multiplier=config.base_delay,
            max=config.max_delay
        )
    
    return retry(
        stop=stop_after_attempt(config.max_retries),
        wait=wait_strategy,
        retry=retry_if_exception_type((
            requests.exceptions.RequestException,
            Exception
        )),
        before_sleep=before_sleep_log(logging.getLogger(__name__), logging.WARNING),
        reraise=True
    )

class LLMInteract:
    def __init__(self, config: Config, auto_cache=False, cache_file="llm_cache.json"):
        # Initialize cloud infrastructure clients
        service_key_path = config.get("service_account_gcp.service_key_path")
        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = service_key_path
        self.gcp_project_id = config.get("service_account_gcp.PROJECT_ID")
        self.gcp_location = config.get("service_account_gcp.LOCATION")
        
        # Initialize GCP Vertex AI client for Claude
        self.sonnet_client_gcp = AnthropicVertex(
            region=self.gcp_location, project_id=self.gcp_project_id
        )
        
        # Initialize Azure OpenAI client
        self.openai_client = AzureOpenAI(
            api_key=config.get("azure_openai.api_key"),
            azure_endpoint=config.get("azure_openai.azure_endpoint"),
            api_version=config.get("azure_openai.api_version"),
        )
        self.azure_model = "gpt-4o"
        self.azure_embedding_model = config.get("azure_openai.embedding")
        
        # Initialize Google GenAI client
        self.google_genai_client = GoogleGenAIClient(config)
        
        # Initialize Anthropic client
        self.anthropic_client = AnthropicClient(config)
        
        # Initialize LLM configuration
        self.llm_config = LLMConfig()
        
        # Initialize prompt cache
        self.prompt_cache = {}
        self.auto_cache = auto_cache
        self.cache_file = cache_file
        
        # Load existing cache if auto_cache is enabled
        if self.auto_cache:
            self.load_cache_from_file(self.cache_file)

        # Setup logging
        log_level = getattr(logging, config.get("logging.level", "INFO"))
        logging.basicConfig(
            filename="logs.txt",
            level=log_level,
            format="%(asctime)s - %(levelname)s - %(message)s",
        )

    def __del__(self):
        """Destructor to save cache when object is destroyed"""
        if hasattr(self, 'auto_cache') and self.auto_cache and hasattr(self, 'prompt_cache'):
            try:
                self.save_cache_to_file(self.cache_file)
            except:
                pass  # Ignore errors during destruction

    def _create_cache_key(self, prompt: str, model: str, **kwargs) -> str:
        """Create a unique cache key based on prompt, model, and additional parameters"""
        cache_data = {
            "prompt": prompt,
            "model": model,
            **kwargs
        }
        cache_string = json.dumps(cache_data, sort_keys=True)
        return hashlib.sha256(cache_string.encode()).hexdigest()

    def _get_from_cache(self, cache_key: str) -> Optional[str]:
        """Retrieve response from cache if it exists"""
        return self.prompt_cache.get(cache_key)

    def _store_in_cache(self, cache_key: str, response: str):
        """Store response in cache"""
        self.prompt_cache[cache_key] = response
        logging.info(f"Cached response for key: {cache_key[:16]}...")
        
        if self.auto_cache and len(self.prompt_cache) % 10 == 0:
            self.save_cache_to_file(self.cache_file)

    def save_cache_to_file(self, cache_file_path: str = "llm_cache.json"):
        """Save the current cache to a JSON file"""
        try:
            with open(cache_file_path, 'w') as f:
                json.dump(self.prompt_cache, f, indent=2)
            logging.info(f"Cache saved to {cache_file_path} with {len(self.prompt_cache)} entries")
        except Exception as e:
            logging.error(f"Error saving cache to {cache_file_path}: {str(e)}")

    def load_cache_from_file(self, cache_file_path: str = "llm_cache.json"):
        """Load cache from a JSON file"""
        try:
            if os.path.exists(cache_file_path):
                with open(cache_file_path, 'r') as f:
                    self.prompt_cache = json.load(f)
                logging.info(f"Cache loaded from {cache_file_path} with {len(self.prompt_cache)} entries")
            else:
                logging.warning(f"Cache file {cache_file_path} not found, starting with empty cache")
        except Exception as e:
            logging.error(f"Error loading cache from {cache_file_path}: {str(e)}")
            self.prompt_cache = {}

    @create_retry_decorator(LLMConfig())
    def get_embeddings(self, texts):
        """Get embeddings with retry logic"""
        response = self.openai_client.embeddings.create(
            input=texts, model=self.azure_embedding_model
        )
        return [i.embedding for i in response.data]

    @create_retry_decorator(LLMConfig())
    def qna_azure(self, user_message, system_message="You are a helpful assistant", request_id=None):
        """Query Azure OpenAI with retry logic and caching"""
        full_prompt = f"System: {system_message}\nUser: {user_message}"
        cache_key = self._create_cache_key(full_prompt, self.azure_model)
        
        # Check cache first
        cached_response = self._get_from_cache(cache_key)
        if cached_response:
            logging.info(f"CACHE HIT for Azure model: {self.azure_model}")
            return cached_response
        
        logging.info(f"CACHE MISS for Azure model: {self.azure_model}")
        logging.info(f"PROMPT:\nSystem: {system_message}\nUser: {user_message}")

        try:
            if request_id:
                log_interaction_sync(request_id, "qna_azure_call", "Calling Azure OpenAI LLM", params={"prompt": user_message})

            response = self.openai_client.chat.completions.create(
                model=self.azure_model,
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message},
                ],
            )
            result = response.choices[0].message.content

            if self._is_valid_response_for_caching(result):
                self._store_in_cache(cache_key, result)

            if request_id:
                log_interaction_sync(request_id, "qna_azure_response", "Received Azure OpenAI LLM response", response={"result": result})

            logging.info(f"RESPONSE:\n{result}")
            return result
        except Exception as e:
            if request_id:
                log_interaction_sync(request_id, "qna_azure_exception", "Exception in qna_azure", status="error", response={"error": str(e)})
            logging.error(f"Error in qna_azure: {str(e)}")
            raise Exception("A temporary issue occurred while analyzing the SOP.")

    @create_retry_decorator(LLMConfig())
    def qna_gemini(
        self,
        user_message,
        system_message="You are a helpful assistant",
        model="gemini-2.0-flash-thinking-exp",
        temperature=0,
        request_id=None
    ):
        """Query Gemini with retry logic and caching"""
        full_prompt = f"{system_message}\n\nUser: {user_message}"
        cache_key = self._create_cache_key(full_prompt, model, temperature=temperature)

        # Check cache first
        cached_response = self._get_from_cache(cache_key)
        if cached_response:
            logging.info(f"CACHE HIT for Gemini model: {model}")
            return cached_response

        logging.info(f"CACHE MISS for Gemini model: {model}")

        try:
            if request_id:
                log_interaction_sync(request_id, "qna_gemini_call", "Calling Gemini LLM", params={"prompt": user_message})

            response = self.google_genai_client.generate_content(
                full_prompt,
                model=model,
                temperature=temperature,
            )

            result = response.text
            if self._is_valid_response_for_caching(result):
                self._store_in_cache(cache_key, result)

            if request_id:
                log_interaction_sync(request_id, "qna_gemini_response", "Received Gemini LLM response", response={"result": getattr(response, 'text', str(response))})

            logging.info(f"PROMPT:\n{full_prompt[:100]}\n{'='*100}\nRESPONSE:\n{result}\n{'='*100}")
            return result
        except Exception as e:
            if request_id:
                log_interaction_sync(request_id, "qna_gemini_exception", "Exception in qna_gemini", status="error", response={"error": str(e)})
            logging.error(f"Error in qna_gemini: {str(e)}")
            raise Exception("A temporary issue occurred while analyzing the SOP.")

    @create_retry_decorator(LLMConfig())
    def qna_anthropic_direct(self, user_message, request_id=None):
        """Query Anthropic with retry logic and caching"""
        model_id = "claude-3-7-sonnet"
        cache_key = self._create_cache_key(user_message, model_id)

        # Check cache first
        cached_response = self._get_from_cache(cache_key)
        if cached_response:
            logging.info(f"CACHE HIT for Anthropic model: {model_id}")
            return cached_response

        logging.info(f"CACHE MISS for Anthropic model: {model_id}")

        try:
            if request_id:
                log_interaction_sync(request_id, "qna_anthropic_call", "Calling Anthropic LLM", params={"prompt": user_message})

            response = self.anthropic_client.generate_response(user_message)

            if self._is_valid_response_for_caching(response):
                self._store_in_cache(cache_key, response)

            if request_id:
                log_interaction_sync(request_id, "qna_anthropic_response", "Received Anthropic LLM response", response={"result": str(response)})

            logging.info(f"calling anthropic: PROMPT:\n{user_message[:100]}" + '\n' + '='*100 + f'\nResponse:\n{response}' + '\n' + '='*100)
            return response
        except Exception as e:
            if request_id:
                log_interaction_sync(request_id, "qna_anthropic_exception", "Exception in qna_anthropic_direct", status="error", response={"error": str(e)})
            logging.error(f"Error in qna_anthropic_direct: {str(e)}")
            raise Exception("A temporary issue occurred while analyzing the SOP.")

    def _is_valid_response_for_caching(self, response: str) -> bool:
        """Check if a response is valid and should be cached"""
        if not response or not response.strip():
            return False
        
        response_lower = response.lower().strip()
        
        error_patterns = [
            "error", "failed", "unable to", "cannot process",
            "something went wrong", "try again", "rate limit",
            "quota exceeded", "service unavailable", "timeout",
            "connection failed", "invalid request", "access denied",
            "authentication failed", "api key", "permission denied",
            "server error", "internal error", "bad request",
            "not found", "forbidden"
        ]
        
        for pattern in error_patterns:
            if pattern in response_lower:
                logging.warning(f"Detected error pattern '{pattern}' in response - not caching")
                return False
        
        if len(response.strip()) < 5:
            logging.warning(f"Response too short ({len(response)} chars) - not caching")
            return False
        
        return True

    def call_llm(self, prompt, model="gemini-20-flash", system_message="You are a helpful assistant", temperature=0, request_id=None):
        """Unified interface for all models with caching"""
        if model == "gemini-20-flash":
            return self.qna_gemini(prompt, system_message=system_message, model="gemini-2.0-flash", temperature=temperature, request_id=request_id)
        elif model == "gemini-2.0-flash-thinking":
            return self.qna_gemini(prompt, system_message=system_message, model="gemini-2.0-flash-thinking-exp", temperature=temperature, request_id=request_id)
        elif model == "claude-3-7-sonnet":
            full_prompt = f"{system_message}\n\n{prompt}" if system_message != "You are a helpful assistant" else prompt
            return self.qna_anthropic_direct(full_prompt, request_id=request_id)
        elif model == "azure-gpt4o":
            return self.qna_azure(prompt, system_message=system_message, request_id=request_id)
        else:
            logging.warning(f"Unknown model '{model}', defaulting to gemini-2.0-flash")
            return self.qna_gemini(prompt, system_message=system_message, model="gemini-2.0-flash", temperature=temperature, request_id=request_id)


if __name__ == "__main__":
    config = Config("user_config.yaml")
    llm = LLMInteract(config)

    # # Dummy test for get_embeddings
    # embeddings = llm.get_embeddings(["Hello, world!", "How are you?"])
    # print("Embeddings:", embeddings)

    # # Dummy test for qna
    # answer = llm.qna_azure("You are a helpful assistant.", "What is the capital of France?")
    # print("Answer:", answer)

    # # Dummy test for perform_vector_search_with_score
    # search_results = llm.perform_vector_search_with_score("What is the capital of France?")
    # print("Search Results:", search_results)

    # Dummy test for qna_azure
    with open("extraction_prompt.txt", "r") as f:
        prompt = f.read()
    # answer = llm.qna_azure(prompt)
    # print("Answer:", answer)

    # Dummy test for qna_gemini
    # answer = llm.qna_gemini(prompt, model="gemini-2.0-flash-thinking-exp")
    answer = llm.qna_anthropic_direct(prompt)
    print("Answer:", answer)

    # # Dummy test for qna_gemini_with_image
    # answer = llm.qna_gemini_with_image("What is the capital of France?", "path/to/image.jpg")
    # print("Answer:", answer)
