-- ===========================================
-- RLS POLICIES
-- ===========================================

-- Create a function to get a user's organization_id that bypasses RLS
-- This is closely tied to the policy, hence kept it here instead of sql/migrations/functions.sql
CREATE OR REPLACE FUNCTION get_auth_user_org_id()
RETURNS uuid
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT organization_id FROM users WHERE id = auth.uid()
$$;

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE regulations ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_regulations ENABLE ROW LEVEL SECURITY;
ALTER TABLE sops ENABLE ROW LEVEL SECURITY;
ALTER TABLE gap_analysis_results ENABLE ROW LEVEL SECURITY;
ALTER TABLE allowed_emails ENABLE ROW LEVEL SECURITY;

-- USERS
CREATE POLICY "Users can read others from their org"
ON users
FOR SELECT
USING (
  organization_id = get_auth_user_org_id()
);

CREATE POLICY "Users can update their own record"
ON users
FOR UPDATE
USING (
  id = auth.uid()
);

-- ORGANIZATIONS
CREATE POLICY "Users can read their organization"
ON organizations
FOR SELECT
USING (
  id = (
    SELECT organization_id FROM users WHERE id = auth.uid()
  )
);

CREATE POLICY "Only admins can update their organization"
ON organizations
FOR UPDATE
USING (
  id = (
    SELECT organization_id FROM users WHERE id = auth.uid()
  ) AND
  EXISTS (
    SELECT 1 FROM users
    WHERE id = auth.uid() AND org_role = 'admin'
  )
);

-- REGULATIONS
CREATE POLICY "Users can read all regulations"
ON regulations
FOR SELECT
USING (auth.uid() IS NOT NULL);


-- Users shouldn't be able to change the global regulations
-- CREATE POLICY "Users can insert regulations"
-- ON regulations
-- FOR INSERT
-- WITH CHECK (
--   uploaded_by = auth.uid()
-- );

-- CREATE POLICY "Users can update their uploaded regulations"
-- ON regulations
-- FOR UPDATE
-- USING (
--   uploaded_by = auth.uid()
-- );

-- ORGANIZATION_REGULATIONS
CREATE POLICY "Users can read their org's regulations"
ON organization_regulations
FOR SELECT
USING (
  organization_id = (
    SELECT organization_id FROM users WHERE id = auth.uid()
  )
);

CREATE POLICY "Only admins can insert for their org"
ON organization_regulations
FOR INSERT
WITH CHECK (
  organization_id = (
    SELECT organization_id FROM users WHERE id = auth.uid()
  )
  AND EXISTS (
    SELECT 1 FROM users WHERE id = auth.uid() AND org_role = 'admin'
  )
);

CREATE POLICY "Only admins can update for their org"
ON organization_regulations
FOR UPDATE
USING (
  organization_id = (
    SELECT organization_id FROM users WHERE id = auth.uid()
  )
  AND EXISTS (
    SELECT 1 FROM users WHERE id = auth.uid() AND org_role = 'admin'
  )
);

-- SOPS

CREATE POLICY "Admins can read all org SOPs; users only their own"
ON sops
FOR SELECT
USING (
  organization_id = (
    SELECT organization_id FROM users WHERE id = auth.uid()
  )
  AND (
    uploaded_by = auth.uid()
    OR EXISTS (
      SELECT 1 FROM users
      WHERE id = auth.uid() AND org_role = 'admin'
    )
  )
);

CREATE POLICY "Users can insert SOPs for their org"
ON sops
FOR INSERT
WITH CHECK (
  organization_id = (
    SELECT organization_id FROM users WHERE id = auth.uid()
  ) AND uploaded_by = auth.uid()
);

CREATE POLICY "Users can update their uploaded SOPs"
ON sops
FOR UPDATE
USING (
  uploaded_by = auth.uid()
);

-- GAP_ANALYSIS_RESULTS
CREATE POLICY "Admins get full access; users only on their uploaded SOPs"
ON gap_analysis_results
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM sops
    WHERE id = gap_analysis_results.sop_id
    AND (
      uploaded_by = auth.uid()
      OR EXISTS (
        SELECT 1 FROM users
        WHERE id = auth.uid() AND org_role = 'admin'
      )
    )
  )
);

CREATE POLICY "Users can insert gap results"
ON gap_analysis_results
FOR INSERT
WITH CHECK (
  sop_id IN (
    SELECT id FROM sops WHERE organization_id = (
      SELECT organization_id FROM users WHERE id = auth.uid()
    )
  )
);

CREATE POLICY "Users can update gap results"
ON gap_analysis_results
FOR UPDATE
USING (
  sop_id IN (
    SELECT id FROM sops WHERE organization_id = (
      SELECT organization_id FROM users WHERE id = auth.uid()
    )
  )
);

-- ALLOWED_EMAILS
CREATE POLICY "Users can view allowed emails for their organization"
ON allowed_emails
FOR SELECT
USING (
  organization_id = (
    SELECT organization_id FROM users WHERE id = auth.uid()
  )
);

-- Allow anonymous users to check if an email is in the allowlist
CREATE POLICY "Anonymous users can check if an email is allowed"
ON allowed_emails
FOR SELECT
USING (
  email = current_setting('request.jwt.claims', true)::json->>'email'
);

CREATE POLICY "Only org admins can insert allowed emails"
ON allowed_emails
FOR INSERT
WITH CHECK (
  organization_id = (
    SELECT organization_id FROM users WHERE id = auth.uid()
  ) AND
  EXISTS (
    SELECT 1 FROM users
    WHERE id = auth.uid() AND org_role = 'admin'
  )
);
