```mermaid
erDiagram
    Users ||--o{ Organizations : associated_with
    Organizations ||--o{ OrganizationRegulations : has
    Users ||--o{ SOPs : uploads
    Regulations ||--o{ OrganizationRegulations : applied_to
    SOPs ||--o{ GapAnalysisResults : analyzed_in
    Regulations ||--o{ GapAnalysisResults : referenced_in
    Users ||--o{ Regulations : uploads

    Users {
        uuid id PK
        string email UK
        string first_name
        string last_name
        string password_hash
        datetime created_at
        datetime last_login
        boolean is_admin
        uuid organization_id FK
        boolean new_user
        jsonb metadata
        string Org_role
    }
 
    Organizations {
        uuid id PK
        string name
        string industry
        string location
        integer size
        datetime created_at
        datetime updated_at
        jsonb metadata
    }
 
    Regulations {
        uuid id PK
        string name
        string description
        string industry
        string region
        string compliance_type
        datetime created_at
        datetime updated_at
        uuid uploaded_by FK
        string source
    }
 
    OrganizationRegulations {
        uuid id PK
        uuid organization_id FK
        uuid regulation_id FK
        boolean is_applicable
        string customization_notes
        datetime created_at
        datetime updated_at
        jsonb metadata
        datetime last_updated
    }
 
    SOPs {
        uuid id PK
        string title
        string file_name
        string blob_storage_url
        string content_type
        integer file_size
        uuid organization_id FK
        uuid uploaded_by FK
        datetime created_at
        datetime updated_at
    }
 
    GapAnalysisResults {
        uuid id PK
        uuid sop_id FK
        uuid regulation_id FK
        jsonb gap_details
        integer compliance_score
        datetime analyzed_at
        jsonb metadata
    }
```