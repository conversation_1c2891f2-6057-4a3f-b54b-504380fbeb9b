.footer {
  background-color: var(--bg-white);
  border-top: 1px solid var(--border-slate-200);
  margin-top: auto;
  width: 100%;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 3rem 2rem 2rem;
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 2rem;
}

.footer-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.footer-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-slate-900);
  margin: 0;
  margin-bottom: 0.5rem;
}

.footer-description {
  color: var(--text-slate-600);
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
  max-width: 280px;
}

.footer-heading {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-slate-900);
  margin: 0 0 0.5rem 0;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.footer-link {
  color: var(--text-slate-600);
  text-decoration: none;
  font-size: 14px;
  transition: color 0.2s ease;
}

.footer-link:hover {
  color: var(--primary-blue);
}

.footer-social {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
}

.footer-social-link {
  color: var(--text-slate-600);
  transition: color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 6px;
  border: 1px solid var(--border-slate-200);
  text-decoration: none;
}

.footer-social-link:hover {
  color: var(--primary-blue);
  border-color: var(--primary-blue);
  background-color: var(--stat-blue-bg);
}

.footer-bottom {
  border-top: 1px solid var(--border-slate-200);
  background-color: #fafafa;
}

.footer-bottom-content {

  margin: 0 auto;
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.footer-copyright {
  color: var(--text-slate-600);
  font-size: 14px;
  margin: 0;
}

.footer-legal {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.footer-legal-link {
  color: var(--text-slate-600);
  text-decoration: none;
  font-size: 14px;
  transition: color 0.2s ease;
}

.footer-legal-link:hover {
  color: var(--primary-blue);
}

.footer-separator {
  color: var(--text-slate-400);
  font-size: 12px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footer-content {
    grid-template-columns: 1fr 1fr 1fr;
    gap: 1.5rem;
  }
  
  .footer-section:first-child {
    grid-column: 1 / -1;
    margin-bottom: 1rem;
  }
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    padding: 2rem 1rem 1.5rem;
  }
  
  .footer-section:first-child {
    grid-column: 1 / -1;
    margin-bottom: 1rem;
  }
  
  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
    padding: 1.5rem 1rem;
  }
  
  .footer-legal {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 2rem 1rem 1.5rem;
  }
  
  .footer-section:first-child {
    grid-column: 1;
    margin-bottom: 0.5rem;
  }
  
  .footer-social {
    justify-content: center;
  }
  
  .footer-legal {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .footer-separator {
    display: none;
  }
} 