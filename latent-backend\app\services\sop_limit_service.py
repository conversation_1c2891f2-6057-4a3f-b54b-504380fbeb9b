"""
SOP Upload Limit Service

This module handles checking and enforcing SOP upload limits for organizations.
"""

import os
import httpx
from typing import Dict, Any, Optional
import logging
from app.services.error_logger import log_interaction

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_PUBLIC_KEY = os.getenv("SUPABASE_PUBLIC_KEY")

class SOPLimitService:
    """Service for managing SOP upload limits."""
    
    @staticmethod
    def get_headers(payload: Dict[str, Any]) -> Dict[str, str]:
        """Get headers for Supabase requests."""
        return {
            "apikey": SUPABASE_PUBLIC_KEY,
            "Authorization": f"Bearer {payload.get('access_token', '')}",
            "Content-Type": "application/json"
        }
    
    @staticmethod
    async def check_sop_upload_limit(
        organization_id: str, 
        payload: Dict[str, Any], 
        request_id: str
    ) -> Dict[str, Any]:
        """
        Check if organization can upload more SOPs.
        
        Args:
            organization_id: The organization ID to check
            payload: JWT payload for authentication
            request_id: Request ID for logging
            
        Returns:
            Dictionary with limit information and whether upload is allowed
        """
        try:
            await log_interaction(
                request_id, 
                "check_sop_limit_start", 
                f"Checking SOP upload limit for organization: {organization_id}",
                params={"organization_id": organization_id}
            )
            
            # Get organization details including max_sop_uploads
            async with httpx.AsyncClient() as client:
                org_response = await client.get(
                    f"{SUPABASE_URL}/rest/v1/organizations",
                    headers=SOPLimitService.get_headers(payload),
                    params={
                        "id": f"eq.{organization_id}",
                        "select": "id,name,max_sop_uploads"
                    }
                )
                
                if org_response.status_code != 200:
                    error_msg = f"Failed to fetch organization details: {org_response.text}"
                    await log_interaction(
                        request_id, 
                        "check_sop_limit_org_fetch_error", 
                        error_msg,
                        status="error"
                    )
                    return {
                        "can_upload": False,
                        "error": error_msg,
                        "current_count": 0,
                        "max_limit": None
                    }
                
                org_data = org_response.json()
                if not org_data:
                    error_msg = f"Organization {organization_id} not found"
                    await log_interaction(
                        request_id, 
                        "check_sop_limit_org_not_found", 
                        error_msg,
                        status="error"
                    )
                    return {
                        "can_upload": False,
                        "error": error_msg,
                        "current_count": 0,
                        "max_limit": None
                    }
                
                organization = org_data[0]
                max_limit = organization.get("max_sop_uploads")
                org_name = organization.get("name", "Unknown")
                
                await log_interaction(
                    request_id, 
                    "check_sop_limit_org_details", 
                    f"Organization details: {org_name}, max_limit: {max_limit}",
                    params={"org_name": org_name, "max_limit": max_limit}
                )
                
                # If max_limit is None, there's no limit
                if max_limit is None:
                    await log_interaction(
                        request_id, 
                        "check_sop_limit_no_limit", 
                        f"No SOP upload limit set for organization: {org_name}"
                    )
                    return {
                        "can_upload": True,
                        "current_count": None,
                        "max_limit": None,
                        "remaining_uploads": None,
                        "organization_name": org_name
                    }
                
                # Count current non-deleted SOPs for the organization
                sop_response = await client.get(
                    f"{SUPABASE_URL}/rest/v1/sops",
                    headers=SOPLimitService.get_headers(payload),
                    params={
                        "organization_id": f"eq.{organization_id}",
                        "is_deleted": "eq.false",
                        "select": "id"
                    }
                )
                
                if sop_response.status_code != 200:
                    error_msg = f"Failed to fetch SOP count: {sop_response.text}"
                    await log_interaction(
                        request_id, 
                        "check_sop_limit_count_error", 
                        error_msg,
                        status="error"
                    )
                    return {
                        "can_upload": False,
                        "error": error_msg,
                        "current_count": 0,
                        "max_limit": max_limit
                    }
                
                sop_data = sop_response.json()
                current_count = len(sop_data) if sop_data else 0
                can_upload = current_count < max_limit
                remaining_uploads = max(0, max_limit - current_count)
                
                result = {
                    "can_upload": can_upload,
                    "current_count": current_count,
                    "max_limit": max_limit,
                    "remaining_uploads": remaining_uploads,
                    "organization_name": org_name
                }
                
                if can_upload:
                    await log_interaction(
                        request_id, 
                        "check_sop_limit_allowed", 
                        f"SOP upload allowed for {org_name}: {current_count}/{max_limit} used",
                        params=result
                    )
                else:
                    await log_interaction(
                        request_id, 
                        "check_sop_limit_exceeded", 
                        f"SOP upload limit reached for {org_name}: {current_count}/{max_limit} used",
                        params=result
                    )
                
                return result
                
        except Exception as e:
            error_msg = f"Error checking SOP upload limit: {str(e)}"
            logger.error(error_msg)
            await log_interaction(
                request_id, 
                "check_sop_limit_exception", 
                error_msg,
                status="error"
            )
            return {
                "can_upload": False,
                "error": error_msg,
                "current_count": 0,
                "max_limit": None
            }
    
    @staticmethod
    async def get_organization_sop_info(
        organization_id: str, 
        payload: Dict[str, Any], 
        request_id: str
    ) -> Dict[str, Any]:
        """
        Get detailed SOP information for an organization.
        
        Args:
            organization_id: The organization ID
            payload: JWT payload for authentication
            request_id: Request ID for logging
            
        Returns:
            Dictionary with detailed SOP information
        """
        try:
            limit_info = await SOPLimitService.check_sop_upload_limit(
                organization_id, payload, request_id
            )
            
            if "error" in limit_info:
                return limit_info
            
            # Get additional organization details
            async with httpx.AsyncClient() as client:
                org_response = await client.get(
                    f"{SUPABASE_URL}/rest/v1/organizations",
                    headers=SOPLimitService.get_headers(payload),
                    params={
                        "id": f"eq.{organization_id}",
                        "select": "*"
                    }
                )
                
                if org_response.status_code == 200:
                    org_data = org_response.json()
                    if org_data:
                        limit_info["organization_details"] = org_data[0]
            
            return limit_info
            
        except Exception as e:
            error_msg = f"Error getting organization SOP info: {str(e)}"
            logger.error(error_msg)
            await log_interaction(
                request_id, 
                "get_org_sop_info_exception", 
                error_msg,
                status="error"
            )
            return {
                "can_upload": False,
                "error": error_msg,
                "current_count": 0,
                "max_limit": None
            }
