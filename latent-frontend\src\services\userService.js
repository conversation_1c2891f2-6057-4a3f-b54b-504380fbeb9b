import apiService from './api';
import API_URLS from '../config/apiUrls';

const userService = {
  // Get user profile
  getUserProfile: async () => {
    return await apiService.get(API_URLS.USER.PROFILE);
  },

  // Update user profile
  updateUserProfile: async (userData, userId) => {
    try {
      return await apiService.patch(API_URLS.USER.UPDATE_USER(userId), userData);
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  },

  // Other user-related API methods can be added here
};

export default userService; 