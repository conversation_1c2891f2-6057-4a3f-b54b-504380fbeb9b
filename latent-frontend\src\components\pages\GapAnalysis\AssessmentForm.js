import React, { useState, useEffect, useRef } from 'react';
import sopService from '../../../services/sopService';
import regulationService from '../../../services/regulationService';
import departmentService from '../../../services/departmentService';
import apiService from '../../../services/api';
import API_URLS from '../../../config/apiUrls';
import { toast } from 'react-toastify';
import LoadingSpinner from '../../common/LoadingSpinner/LoadingSpinner';
import './AssessmentForm.css';
import { sanitizeText } from '../../../utils/sanitize';

const AssessmentForm = ({ onClose, onSubmit, onSuccess, onProcessingStart }) => {
  const [sops, setSops] = useState([]);
  const [regulations, setRegulations] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [showSOPDropdown, setShowSOPDropdown] = useState(false);
  const [showDepartmentDropdown, setShowDepartmentDropdown] = useState(false);
  const [showRegulationDropdown, setShowRegulationDropdown] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const sopDropdownRef = useRef(null);
  const departmentDropdownRef = useRef(null);
  const regulationDropdownRef = useRef(null);
  
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    selectedDepartment: null,
    category: '',
    selectedComplianceRequirements: [],
    selectedSOP: null
  });
  
  // First, add state variables to track loading state for each dropdown
  const [sopsLoading, setSOPsLoading] = useState(true);
  const [regulationsLoading, setRegulationsLoading] = useState(true);
  
  // Add state for SOP search and error handling
  const [sopSearchTerm, setSopSearchTerm] = useState('');
  const [sopsError, setSopsError] = useState(null);
  
  // Add state for regulation search
  const [regulationSearchTerm, setRegulationSearchTerm] = useState('');
  
  // Prevent body scroll when modal is open
  useEffect(() => {
    // Prevent scrolling when modal is open
    document.body.style.overflow = 'hidden';

    // Cleanup function to restore scrolling when component unmounts
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);
  
  // Fetch SOPs, departments, and regulations on component mount
  useEffect(() => {
    const fetchSOPs = async () => {
      try {
        setSOPsLoading(true);
        setSopsError(null);
        const sopData = await sopService.getAllSOPs();
        setSops(sopData.sops_data || []);
      } catch (err) {
        console.error('Error fetching SOPs:', err);
        setSopsError('Failed to load SOPs. Please try again.');
      } finally {
        setSOPsLoading(false);
      }
    };
    
    const fetchDepartments = async () => {
      try {
        const departmentData = await departmentService.getAllDepartments();
        setDepartments(departmentData);
      } catch (err) {
        console.error('Error fetching departments:', err);
      }
    };
    
    const fetchRegulations = async () => {
      try {
        setRegulationsLoading(true);
        const regulationData = await regulationService.getOrganizationRegulations();
        let regulationData_details = regulationData.map(regulation => regulation.regulation_details);
        setRegulations(regulationData_details);
      } catch (err) {
        console.error('Error fetching regulations:', err);
        // Handle error
      } finally {
        setRegulationsLoading(false);
      }
    };
    
    fetchSOPs();
    fetchDepartments();
    fetchRegulations();
  }, []);
  
  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (sopDropdownRef.current && !sopDropdownRef.current.contains(event.target)) {
        setShowSOPDropdown(false);
      }
      if (departmentDropdownRef.current && !departmentDropdownRef.current.contains(event.target)) {
        setShowDepartmentDropdown(false);
      }
      if (regulationDropdownRef.current && !regulationDropdownRef.current.contains(event.target)) {
        setShowRegulationDropdown(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };
  
  // Handle SOP selection
  const handleSOPSelect = (sop) => {
    setFormData({
      ...formData,
      selectedSOP: sop
    });
    setShowSOPDropdown(false);
  };
  
  // Handle department selection
  const selectDepartment = (department) => {
    setFormData({
      ...formData,
      selectedDepartment: department
    });
    setShowDepartmentDropdown(false);
  };
  
  // Handle regulation selection (toggle)
  const handleRegulationToggle = (regulation) => {
    setFormData(prev => {
      const isAlreadySelected = prev.selectedComplianceRequirements.some(r => r.id === regulation.id);
      
      if (isAlreadySelected) {
        // Remove the regulation if already selected
        return {
          ...prev,
          selectedComplianceRequirements: prev.selectedComplianceRequirements.filter(r => r.id !== regulation.id)
        };
      } else {
        // Add the regulation if not already selected
        return {
          ...prev,
          selectedComplianceRequirements: [...prev.selectedComplianceRequirements, regulation]
        };
      }
    });
  };
  
  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };
  
  // Add validation logic to check if form is complete
  const isFormValid = () => {
    return (
      formData.selectedSOP && // SOP must be selected
      formData.selectedComplianceRequirements && 
      formData.selectedComplianceRequirements.length > 0 // At least one compliance requirement must be selected
    );
  };
  
  // Filter regulations based on search term
  const filteredRegulations = regulations.filter(regulation => 
    regulation.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (loading) return;
    
    // Validate form
    if (!formData.selectedSOP) {
      toast.error('Please select a SOP for assessment', {
        toastId: `sop-required-${Date.now()}`
      });
      return;
    }
    
    if (!formData.selectedComplianceRequirements || formData.selectedComplianceRequirements.length === 0) {
      toast.error('Please select at least one compliance requirement', {
        toastId: `compliance-required-${Date.now()}`
      });
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      // Sanitize form data before submission
      const sanitizedFormData = {
        ...formData,
        title: sanitizeText(formData.title),
        department: sanitizeText(formData.selectedDepartment?.name),
        description: sanitizeText(formData.description),
        // Sanitize any other text fields
      };
      
      const regulationIds = sanitizedFormData.selectedComplianceRequirements.map(req => req.id);
      
      // Call the processing start handler to close modal and show processing state
      if (typeof onProcessingStart === 'function') {
        onProcessingStart(sanitizedFormData.selectedSOP.id);
      }
      
      // Make the API call
      const response = await apiService.post(
        `${API_URLS.ANALYSIS.ANALYZE}/${sanitizedFormData.selectedSOP.id}`,
        regulationIds
      );
      
      console.log('Assessment API response:', response);
      
      // Show success message
      toast.success('Assessment created successfully', {
        toastId: `assessment-created-${Date.now()}`
      });
      
      // Call the parent's refresh function if provided
      if (typeof onSuccess === 'function') {
        onSuccess();
      } else if (typeof onSubmit === 'function') {
        onSubmit();
      }
      
    } catch (err) {
      console.error('Error creating assessment:', err);
      setError('Failed to create assessment. Please try again later.');
      
      toast.error('Failed to create assessment', {
        toastId: `assessment-error-${Date.now()}`
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Update the SOP dropdown to include search functionality
  const renderSOPDropdown = () => {
    // Sort SOPs by date (most recent first)
    const sortedSOPs = [...sops].sort((a, b) => {
      // Get the date from updated_at or created_at, fallback to current date
      const dateA = new Date(a.updated_at || a.created_at || Date.now());
      const dateB = new Date(b.updated_at || b.created_at || Date.now());
      return dateB - dateA; // Sort in descending order (newest first)
    });
    
    return (
      <div className="sop-selector" ref={sopDropdownRef}>
        <div 
          className="selected-sop-display"
          onClick={() => setShowSOPDropdown(!showSOPDropdown)}
          aria-expanded={showSOPDropdown}
        >
          {sopsLoading ? (
            <div className="dropdown-loading">
              <LoadingSpinner size="small" />
              <span>Loading SOPs...</span>
            </div>
          ) : formData.selectedSOP ? (
            <div className="selected-sop">
              <span>{sanitizeText(formData.selectedSOP.title || 'Unnamed SOP')}</span>
              <button 
                className="clear-sop"
                onClick={(e) => {
                  e.stopPropagation();
                  setFormData({ ...formData, selectedSOP: null });
                }}
              >
                ×
              </button>
            </div>
          ) : (
            <span className="placeholder">Select a SOP</span>
          )}
          <span className="dropdown-arrow">▼</span>
        </div>
        
        {showSOPDropdown && !sopsLoading && (
          <div className="dropdown-menu">
            {/* Add search input */}
            <div className="dropdown-search">
              <input
                type="text"
                placeholder="Search SOPs..."
                value={sopSearchTerm}
                onChange={(e) => setSopSearchTerm(e.target.value)}
                onClick={(e) => e.stopPropagation()}
                autoFocus
              />
            </div>
            
            {sopsError ? (
              <div className="dropdown-message error">
                {sopsError}
              </div>
            ) : sortedSOPs.length === 0 ? (
              <div className="dropdown-message">
                No SOPs available. Please add SOPs first.
              </div>
            ) : (
              <div className="dropdown-options">
                {/* Filter SOPs based on search term */}
                {sortedSOPs
                  .filter(sop => 
                    (sop.title || 'Unnamed SOP').toLowerCase().includes(sopSearchTerm.toLowerCase())
                  )
                  .map(sop => (
                    <div
                      key={sop.id}
                      className={`sop-option ${formData.selectedSOP?.id === sop.id ? 'selected' : ''}`}
                      onClick={() => handleSOPSelect(sop)}
                    >
                      <div className="sop-option-content">
                        <span className="sop-title">{sanitizeText(sop.title || 'Unnamed SOP')}</span>
                      </div>
                    </div>
                  ))}
                
                {/* Show message when no SOPs match the search */}
                {sortedSOPs.filter(sop => 
                  (sop.title || 'Unnamed SOP').toLowerCase().includes(sopSearchTerm.toLowerCase())
                ).length === 0 && (
                  <div className="dropdown-message">
                    No SOPs match your search.
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    );
  };
  
  // Update the regulation dropdown to include search functionality
  const renderRegulationDropdown = () => {
    return (
      <div className="regulation-selector" ref={regulationDropdownRef}>
        <div 
          className="selected-sop-display"
          onClick={() => setShowRegulationDropdown(!showRegulationDropdown)}
        >
          {regulationsLoading ? (
            <div className="dropdown-loading">
              <LoadingSpinner size="small" />
              <span>Loading requirements...</span>
            </div>
          ) : formData.selectedComplianceRequirements && formData.selectedComplianceRequirements.length > 0 ? (
            <div className="selected-sops-tags">
              {formData.selectedComplianceRequirements.map(req => (
                <div key={req.id} className="sop-tag">
                  {req.name}
                  <button 
                    className="remove-tag"
                    onClick={(e) => {
                      e.stopPropagation();
                      setFormData(prev => ({
                        ...prev,
                        selectedComplianceRequirements: prev.selectedComplianceRequirements.filter(r => r.id !== req.id)
                      }));
                    }}
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
          ) : (
            <span className="placeholder">Select compliance requirements</span>
          )}
          <span className="dropdown-arrow">▼</span>
        </div>
        
        {showRegulationDropdown && !regulationsLoading && (
          <div className="dropdown-menu">
            {/* Add search input */}
            <div className="dropdown-search">
              <input
                type="text"
                placeholder="Search compliance..."
                value={regulationSearchTerm}
                onChange={(e) => setRegulationSearchTerm(e.target.value)}
                onClick={(e) => e.stopPropagation()}
                autoFocus
              />
            </div>
            
            {regulations.length === 0 ? (
              <div className="dropdown-message">
                No compliance requirements available.
              </div>
            ) : (
              <div className="dropdown-options">
                {/* Filter regulations based on search term */}
                {regulations
                  .filter(reg => 
                    (reg.name || '').toLowerCase().includes(regulationSearchTerm.toLowerCase())                  )
                  .map(reg => {
                    const isSelected = formData.selectedComplianceRequirements.some(r => r.id === reg.id);
                    
                    return (
                      <div
                        key={reg.id}
                        className={`sop-option ${isSelected ? 'selected' : ''}`}
                        onClick={() => handleRegulationToggle(reg)}
                      >
                        <div className="sop-option-content">
                          <div className="checkbox">
                            {isSelected && '✓'}
                          </div>
                          <span className="sop-title">{reg.name || 'Unnamed Requirement'}</span>
                        </div>
                      </div>
                    );
                  })}
                
                {/* Show message when no regulations match the search */}
                {regulations.filter(reg => 
                  (reg.name || '').toLowerCase().includes(regulationSearchTerm.toLowerCase()) 
                ).length === 0 && (
                  <div className="dropdown-message">
                    No requirements match your search.
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    );
  };
  
  // Add useEffect to handle dynamic spacing when dropdowns open/close
  useEffect(() => {
    const updateDropdownSpacing = () => {
      if (showSOPDropdown && sopDropdownRef.current) {
        const dropdownMenu = sopDropdownRef.current.querySelector('.dropdown-menu');
        if (dropdownMenu) {
          const dropdownHeight = dropdownMenu.offsetHeight;
          sopDropdownRef.current.style.marginBottom = `${dropdownHeight + 20}px`;
        }
      } else if (sopDropdownRef.current) {
        sopDropdownRef.current.style.marginBottom = '0px';
      }
    };

    // Small delay to ensure DOM is updated
    const timeoutId = setTimeout(updateDropdownSpacing, 10);
    return () => clearTimeout(timeoutId);
  }, [showSOPDropdown, sops.length, sopSearchTerm]);

  useEffect(() => {
    const updateDropdownSpacing = () => {
      if (showRegulationDropdown && regulationDropdownRef.current) {
        const dropdownMenu = regulationDropdownRef.current.querySelector('.dropdown-menu');
        if (dropdownMenu) {
          const dropdownHeight = dropdownMenu.offsetHeight;
          regulationDropdownRef.current.style.marginBottom = `${dropdownHeight + 20}px`;
        }
      } else if (regulationDropdownRef.current) {
        regulationDropdownRef.current.style.marginBottom = '0px';
      }
    };

    // Small delay to ensure DOM is updated
    const timeoutId = setTimeout(updateDropdownSpacing, 10);
    return () => clearTimeout(timeoutId);
  }, [showRegulationDropdown, regulations.length, regulationSearchTerm]);
  
  return (
    <div className="assessment-form">
      <h2>Create Gap Assessment</h2>
      
      <form onSubmit={handleSubmit}>
        {/* <div className="form-group">
          <label htmlFor="title">Assessment Title</label>
          <input 
            type="text" 
            id="title" 
            name="title" 
            value={formData.title} 
            onChange={handleInputChange} 
            placeholder="Enter assessment title"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="description">Description</label>
          <textarea 
            id="description" 
            name="description" 
            value={formData.description} 
            onChange={handleInputChange} 
            placeholder="Enter assessment description"
          />
        </div> */}
        
        {/* <div className="form-row"> */}
          <div className="form-group">
            <label>Select SOPs for assessment</label>
            {renderSOPDropdown()}
          </div>
          
          {/* <div className="form-group">
            <label>Department</label>
            <div className="department-selector" ref={departmentDropdownRef}>
              <div 
                className="selected-sop-display" 
                onClick={() => setShowDepartmentDropdown(!showDepartmentDropdown)}
                aria-expanded={showDepartmentDropdown}
              >
                {!formData.selectedDepartment ? (
                  <span className="placeholder">Select Department</span>
                ) : (
                  <span className="selected-value">
                    {formData.selectedDepartment.name || 'Selected Department'}
                  </span>
                )}
                <span className="dropdown-arrow">▼</span>
              </div>
              
              {showDepartmentDropdown && (
                <div className="dropdown-menu">
                  <input 
                    type="text" 
                    placeholder="Search departments..." 
                    value={searchTerm}
                    onChange={handleSearchChange}
                    onClick={(e) => e.stopPropagation()}
                  />
                  
                  <div className="dropdown-options">
                    {departments.map(department => (
                      <div 
                        key={department.id} 
                        className={`dropdown-option ${formData.selectedDepartment && formData.selectedDepartment.id === department.id ? 'selected' : ''}`}
                        onClick={() => selectDepartment(department)}
                      >
                        {department.name || 'Unnamed Department'}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div> */}
        {/* </div> */}
        
        <div className="form-group">
          <label>Compliance Requirements</label>
          {renderRegulationDropdown()}
        </div>
        
        {error && <div className="error-message">{error}</div>}
        
        <div className="assessment-form-actions">
          <button 
            type="button" 
            className="btn-cancel" 
            onClick={onClose}
            disabled={loading}
          >
            Cancel
          </button>
          <button 
            type="submit" 
            className={`create-assessment-btn ${loading ? 'creating' : ''} ${!isFormValid() ? 'disabled' : ''}`}
            disabled={loading || !isFormValid()}
            title={!isFormValid() ? 'Please select a SOP and at least one compliance requirement' : ''}
          >
            {loading ? (
              <>
                <LoadingSpinner size="small" />
                <span>Creating...</span>
              </>
            ) : 'Create Assessment'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AssessmentForm; 