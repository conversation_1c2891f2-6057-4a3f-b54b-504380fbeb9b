import json
import os
from typing import List, Dict
from app.services.gap_analysis_utils.llm_interact import LLMInteract
from app.services.gap_analysis_utils.config import Config  # Use the correct import path
import pandas as pd
import yaml
import logging
from app.services.error_logger import log_interaction, log_interaction_sync

logging.basicConfig(level=logging.INFO)


class SOPGapAnalyzer:
    def __init__(self, llm: LLMInteract, prompts: Dict[str, str] = None, model: str = "gemini-20-flash"):
        self.llm = llm
        self.model = model
        # self.chroma_interact = chroma_interact
        
        # Load default prompts if none provided
        if prompts is None:
            prompts_path = os.path.join(os.path.dirname(__file__), "gap_analysis_prompts.yaml")
            if os.path.exists(prompts_path):
                with open(prompts_path, "r") as f:
                    self.prompts = yaml.safe_load(f)
            else:
                # Fallback to hardcoded prompts
                self.prompts = {
                    "gap_analysis_prompt": self._default_gap_analysis_prompt(),
                    "deduplication_prompt": self._default_deduplication_prompt()
                }
        else:
            self.prompts = prompts

    def call_llm(self, prompt, request_id=None):
        """Call the appropriate LLM based on the model specified"""
        if self.model == "gemini-20-flash":
            return self.llm.qna_gemini(prompt, model="gemini-2.0-flash", request_id=request_id)
        elif self.model == "gemini-2.0-flash-thinking":
            return self.llm.qna_gemini(prompt, model="gemini-2.0-flash-thinking-exp", request_id=request_id)
        elif self.model == "claude-3-7-sonnet":
            return self.llm.qna_anthropic_direct(prompt, request_id=request_id)
        else:
            # Default to gemini-20-flash if model is not recognized
            return self.llm.qna_gemini(prompt, model="gemini-2.0-flash", request_id=request_id)

    async def analyze_sop_gaps(self, sop_text, documents, request_id=None):
        try:
            if request_id:
                import asyncio
                asyncio.create_task(log_interaction(request_id, "analyze_sop_gaps_start", "Starting analyze_sop_gaps", params={}))
            print('Processing total {} documents'.format(len(documents)))

            from concurrent.futures import ThreadPoolExecutor
            import threading

            # Create thread-local storage for results
            thread_local = threading.local()

            def analyze_single_document(document, doc_index):
                try:
                    # Log start of document analysis (using sync version for thread safety)
                    if request_id:
                        log_interaction_sync(request_id, f"analyze_document_{doc_index}_start", f"Starting analysis of document {doc_index}", params={})

                    # Create prompt for gap analysis
                    gap_analysis_prompt = self._create_gap_analysis_prompt(sop_text, document)

                    # Get gap analysis from LLM using the call_llm method
                    gap_analysis_result = self.call_llm(gap_analysis_prompt, request_id=request_id)

                    # Log completion of document analysis
                    if request_id:
                        log_interaction_sync(request_id, f"analyze_document_{doc_index}_end", f"Completed analysis of document {doc_index}", params={})

                    return gap_analysis_result
                except Exception as e:
                    if request_id:
                        log_interaction_sync(request_id, f"analyze_document_{doc_index}_error", f"Error analyzing document {doc_index}", status="error", response={"error": str(e)})
                    raise e

            # Create a thread pool with max workers equal to number of documents, but at least 1
            max_workers = max(1, len(documents))
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # Submit analysis tasks for each document with index
                future_to_doc = {
                    executor.submit(analyze_single_document, doc, idx): doc for idx, doc in enumerate(documents)
                }

                # Collect results as they complete
                gaps = []
                for future in future_to_doc:
                    try:
                        result = future.result()
                        if result:
                            gaps.append(result)
                    except Exception as e:
                        print(f"Analysis failed with error: {e}")
                        if request_id:
                            import asyncio
                            asyncio.create_task(log_interaction(request_id, "document_analysis_thread_error", "Thread analysis failed", status="error", response={"error": str(e)}))

            # Concatenate all gap analysis results
            if request_id:
                import asyncio
                asyncio.create_task(log_interaction(request_id, "process_gaps_start", "Starting gap processing", params={"gaps": gaps}))

            processed_gaps = []
            for gap in gaps:
                if not gap.startswith("```json"):
                    continue
                gap = gap.split("```json")[1]
                gap = gap.split("```")[0]
                json_data = json.loads(gap)
                processed_gaps.extend(json_data)

            processed_gaps_str = json.dumps(processed_gaps)

            if request_id:
                import asyncio
                asyncio.create_task(log_interaction(request_id, "deduplication_start", "Starting gap deduplication", params={"processed_gaps": processed_gaps_str}))

            deduplicated_gaps = self.call_llm(
                self._create_deduplicated_gaps_prompt(processed_gaps_str),
                request_id=request_id
            )

            if request_id:
                import asyncio
                asyncio.create_task(log_interaction(request_id, "deduplication_complete", "Gap deduplication completed", params={}))

            if deduplicated_gaps.startswith("```json"):
                deduplicated_gaps = deduplicated_gaps.split("```json")[1]
                deduplicated_gaps = deduplicated_gaps.split("```")[0]
            deduplicated_gaps = json.loads(deduplicated_gaps)

            self.processed_gaps = processed_gaps
            self.deduplicated_gaps = deduplicated_gaps

            if request_id:
                import asyncio
                asyncio.create_task(log_interaction(request_id, "analyze_sop_gaps_end", "Finished analyze_sop_gaps", response={"deduplicated_gaps": self.deduplicated_gaps}))
            return self.deduplicated_gaps
        except Exception as e:
            if request_id:
                import asyncio
                asyncio.create_task(log_interaction(request_id, "analyze_sop_gaps_exception", "Exception in analyze_sop_gaps", status="error", response={"error": str(e), "gaps": gaps if 'gaps' in locals() else None}))
            raise Exception("A temporary issue occurred while analyzing the SOP.")

    def _create_gap_analysis_prompt(self, sop_text, guidelines):
        prompt_template = self.prompts.get("gap_analysis_prompt", self._default_gap_analysis_prompt())
        return prompt_template.replace("__guidelines__", guidelines).replace("__sop_text__", sop_text)

    def _create_deduplicated_gaps_prompt(self, gaps):
        prompt_template = self.prompts.get("deduplication_prompt", self._default_deduplication_prompt())
        return prompt_template.replace("__gaps__", gaps)
        
    def _default_gap_analysis_prompt(self):
        return """You are a pharmaceutical quality assurance expert. You analyse the manufacturing SOP according to the compliance document provided for potential improvements based on the provided guidelines and the gaps in Good Manufacturing Practices (GMP) if any in the SOP.

        Format your response as a JSON array where each object has these fields:
        - "critique": The specific gap or issue identified 
        - "guidelines_reference": The quoted section from the compliance document that is relevant to the gap
        - "sop_reference": The exact quoted text from the SOP document that is relevant to the gap
        - "priority": Priority level based on:
            - High: Direct impact on product quality, safety, or regulatory compliance
            - Medium: Indirect impact on processes or documentation  
            - Low: Suggestions for improvement with minimal risk

        Rules to follow:
        - ALWAYS and ONLY output a JSON has the format as the example provided and do not output anything else.
        - The JSON should be enclosed within ```json <json_text> ``` tags
        - Only use the compliance document provided to identify the gaps and improvements
        - Do not assume any gaps on your own, return an empty array if no gaps are identified
        - A critique should only be made if it is contradictory to the compliance document
        - Each critique must have a corresponding reference from the compliance document
        - Use consistent priority levels (High/Medium/Low)

        Sample response format:
        ```json
        [
            {
                "critique": "sample critique",
                "guidelines_reference": "sample reference",
                "sop_reference": "sample sop reference",
                "priority": "sample priority"
            }
            ...
        ]
        ```
        ALWAYS and ONLY output a JSON that has the format as the example provided and do not output anything else.
        
        Compliance Document:
        __guidelines__
        
        SOP Text:
        __sop_text__

        """
        
    def _default_deduplication_prompt(self):
        return """You are a pharmaceutical quality assurance expert. You need to deduplicate the provided json string of gaps.
        These gaps are generated for a given SOP from different compliance documents and are concatenated. If a similar gap is appearing in multiple places, you should just have on instance of it. 
        Do not leave out any information. Do not add in or remove any of the original information. Maintain the structure of json as it is.

        Format your response as a JSON array where each object has these fields:
        - "critique": The consolidated gap description that combines similar issues
        - "guidelines_reference": All relevant guideline references combined (with sources clearly indicated)
        - "sop_reference": All relevant SOP references combined
        - "priority": The highest priority level among the consolidated gaps (High/Medium/Low)

        The JSON Structure should be as follows:
        ```json
        [
            {
                "critique": "sample critique",
                "guidelines_reference": "sample reference",
                "sop_reference": "sample sop reference",
                "priority": "sample priority"
            }
            ...
        ]
        ```
        

        Rules to follow:
        - ALWAYS and ONLY output a JSON in the format provided and do not output anything else
        - The JSON should be enclosed within ```json <json_text> ``` tags
        - Consolidate gaps based on similar issues, not just similar wording
        - Preserve all relevant information from the original gaps
        - If gaps are completely unique, keep them as separate entries
        - Ensure the consolidated critique is clear and comprehensive

        Original Gaps:
        __gaps__

        ALWAYS and ONLY output a JSON that has the format as specified and do not output anything else.
        """


def main():
    config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "user_config.yaml")

    cfg = Config(config_path)
    llm = LLMInteract(cfg)

    # Initialize gap analyzer
    gap_analyzer = SOPGapAnalyzer(llm)
    documents = []

    guidelines_path = 'app/services/gap_analysis_utils/demo_guidelines'
    for file in os.listdir(guidelines_path):
        if not file.endswith(".txt"):
            continue
        with open(
            os.path.join(guidelines_path, file), "r"
        ) as f:
            documents.append(f.read())

    # Read sample SOP
    # with open(
    #     os.path.join(
    #         os.path.dirname(__file__),
    #         "..",
    #         "processed_sops",
    #         "SOP for Process Validation.txt",
    #     ),
    #     "r",
    # ) as f:
    with open(
        os.path.join(os.path.dirname(__file__), "..", "demo_sops", "sop.txt"),
        "r",
    ) as f:
        sop_text = f.read()

    # Perform gap analysis
    gaps_df = gap_analyzer.analyze_sop_gaps(sop_text, documents)
    print(gaps_df)  # TODO: deduplicate gaps
    print(len(gaps_df))


if __name__ == "__main__":
    import sys
    import os

    main()

##
