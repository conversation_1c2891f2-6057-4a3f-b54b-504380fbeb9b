from fastapi import APIRouter, Depends, File, UploadFile, HTTPException, Form, Request
from fastapi.responses import JSONResponse, StreamingResponse
from app.services.auth_utils import verify_token
from app.services.file_service import get_content_type, upload_to_azure_blob, download_from_azure_blob
import os
from urllib.parse import unquote, quote
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>ear<PERSON>
import uuid
import json
import httpx
from typing import Any, Dict, Optional
import logging
from app.services.error_logger import log_user_request, log_interaction
import asyncio

# Configure logging
logging.basicConfig(level=logging.CRITICAL)
logger = logging.getLogger(__name__)

SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_PUBLIC_KEY = os.getenv("SUPABASE_PUBLIC_KEY")

router = APIRouter()

def getHeaders(payload: Any):
    return {
        "apikey": SUPABASE_PUBLIC_KEY,
        "Authorization": f"Bearer {payload.get('access_token', '')}",
        "Content-Type": "application/json"
    }

@router.get('/')
async def list_sops(request: Request, payload: Dict[Any, Any] = Depends(verify_token)):
    # Create request_id for logging
    request_id = str(uuid.uuid4())
    user_id = payload.get("sub")
    user_email = payload.get("email", "")
    ip_address = request.client.host
    endpoint = str(request.url)
    method = request.method
    request_params = {"user_id": user_id}

    # Log the user request
    await log_user_request(
        user_id, user_email, ip_address, endpoint, method, request_params, request_id=request_id
    )

    # Log start of function
    asyncio.create_task(log_interaction(request_id, "list_sops_start", "Starting list_sops", params=request_params))

    sops_url = f"{SUPABASE_URL}/rest/v1/sops?is_deleted=eq.false"

    try:
        asyncio.create_task(log_interaction(request_id, "fetch_sops_attempt", "Attempting to fetch SOPs", params={"sops_url": sops_url}))

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Fetch SOPs first
            sops_response = await client.get(sops_url, headers=getHeaders(payload))

            if sops_response.status_code != 200:
                logger.error(f"SOP fetch failed: {sops_response.text}")
                asyncio.create_task(log_interaction(request_id, "fetch_sops_failure", "Failed to fetch SOPs",
                                                  status="error", response={"status_code": sops_response.status_code, "error": sops_response.text}))
                raise HTTPException(status_code=sops_response.status_code, detail="We couldn't retrieve your SOPs at the moment. Please try again later.")

            sops_data = sops_response.json()
            asyncio.create_task(log_interaction(request_id, "fetch_sops_success", "Successfully fetched SOPs",
                                              response={"sops_count": len(sops_data) if isinstance(sops_data, list) else 0}))

            if not isinstance(sops_data, list):
                asyncio.create_task(log_interaction(request_id, "invalid_sops_data", "Received invalid SOPs data format",
                                                  status="error", response={"data_type": type(sops_data).__name__}))
                raise HTTPException(status_code=500, detail="We received unexpected data from our database. Please contact support if this continues.")

            # Initialize organization-wide counters
            total_identified_gaps = 0

            # Now, for each SOP, fetch gap_analysis_results by sop_id
            asyncio.create_task(log_interaction(request_id, "process_gap_analysis_start", "Starting gap analysis processing for SOPs",
                                              params={"total_sops": len(sops_data)}))

            processed_sops = 0
            for sop in sops_data:
                sop_id = sop.get("id")
                if not sop_id:
                    logger.warning(f"Skipping SOP with missing 'id': {sop}")
                    asyncio.create_task(log_interaction(request_id, "sop_missing_id", "Skipping SOP with missing ID",
                                                      status="error", response={"sop": sop}))
                    continue

                # Fetch gap_analysis_results for this SOP
                gap_url = f"{SUPABASE_URL}/rest/v1/gap_analysis_results?sop_id=eq.{sop_id}&select=gap_details,metadata"
                try:
                    asyncio.create_task(log_interaction(request_id, "fetch_gap_analysis_attempt", f"Fetching gap analysis for SOP: {sop_id}",
                                                      params={"sop_id": sop_id, "gap_url": gap_url}))

                    gap_response = await client.get(gap_url, headers=getHeaders(payload))
                    if gap_response.status_code != 200:
                        logger.warning(f"Gap fetch failed for SOP ID {sop_id}: {gap_response.text}")
                        asyncio.create_task(log_interaction(request_id, "fetch_gap_analysis_failure", f"Failed to fetch gap analysis for SOP: {sop_id}",
                                                          status="error", response={"sop_id": sop_id, "status_code": gap_response.status_code, "error": gap_response.text}))
                        continue

                    gap_result = gap_response.json()
                    if not isinstance(gap_result, list) or not gap_result:
                        asyncio.create_task(log_interaction(request_id, "gap_analysis_empty", f"No gap analysis found for SOP: {sop_id}",
                                                          params={"sop_id": sop_id, "result_type": type(gap_result).__name__}))
                        continue

                    # Prefer changed_gap_detail if available and non-empty
                    metadata = gap_result[0].get("metadata", {})
                    changed_gap_detail = metadata.get('changed_gap_detail',[]);

                    if changed_gap_detail and len(changed_gap_detail) > 0:
                        gap_details = changed_gap_detail
                        asyncio.create_task(log_interaction(request_id, "gap_analysis_using_changed", f"Using changed gap details for SOP: {sop_id}",
                                                          params={"sop_id": sop_id, "changed_gaps_count": len(changed_gap_detail)}))
                    else:
                        gap_details = gap_result[0].get("gap_details", [])
                        asyncio.create_task(log_interaction(request_id, "gap_analysis_using_original", f"Using original gap details for SOP: {sop_id}",
                                                          params={"sop_id": sop_id, "original_gaps_count": len(gap_details) if isinstance(gap_details, list) else 0}))

                    if not isinstance(gap_details, list):
                        logger.warning(f"Invalid gap_details for SOP ID {sop_id}: {gap_details}")
                        asyncio.create_task(log_interaction(request_id, "gap_analysis_invalid_format", f"Invalid gap details format for SOP: {sop_id}",
                                                          status="error", response={"sop_id": sop_id, "gap_details_type": type(gap_details).__name__}))
                        gap_details = []

                    sop_total_gaps = len(gap_details)
                    sop["total_gaps"] = sop_total_gaps
                    total_identified_gaps += sop_total_gaps  # Add to organization-wide total

                    sop["high_priority_gaps"] = sum(
                        1 for gap in gap_details
                        if isinstance(gap, dict) and gap.get("priority", "").lower() == "high"
                    )
                    sop["medium_priority_gaps"] =  sum(
                        1 for gap in gap_details
                        if isinstance(gap, dict) and gap.get("priority", "").lower() == "medium"
                    )
                    sop["low_priority_gaps"] = sum(
                        1 for gap in gap_details
                        if isinstance(gap, dict) and gap.get("priority", "").lower() == "low"
                    )


                    asyncio.create_task(log_interaction(request_id, "gap_analysis_processed", f"Successfully processed gap analysis for SOP: {sop_id}",
                                                      response={"sop_id": sop_id, "total_gaps": sop["total_gaps"], "high_priority_gaps": sop["high_priority_gaps"]}))
                    processed_sops += 1

                except httpx.RequestError as re:
                    logger.error(f"HTTP error fetching gap analysis for SOP {sop_id}: {str(re)}")
                    asyncio.create_task(log_interaction(request_id, "gap_analysis_http_error", f"HTTP error fetching gap analysis for SOP: {sop_id}",
                                                      status="error", response={"sop_id": sop_id, "error": str(re)}))
                    sop["total_gaps"] = 0
                    sop["high_priority_gaps"] = 0
                    sop["medium_priority_gaps"] = 0
                    sop["low_priority_gaps"] = 0
                    continue

            # Add organization-wide total to each SOP
            asyncio.create_task(log_interaction(request_id, "list_sops_success", "Successfully completed list_sops",
                                              response={"total_sops": len(sops_data), "processed_sops": processed_sops}))
            return JSONResponse(status_code=200, content={
                "sops_data": sops_data, 
                "total_identified_gaps": total_identified_gaps
    })

    except httpx.RequestError as re:
        logger.exception("HTTP client error during SOP fetch.")
        asyncio.create_task(log_interaction(request_id, "list_sops_http_error", "HTTP client error during SOP fetch",
                                          status="error", response={"error": str(re)}))
        raise HTTPException(status_code=502, detail="We couldn't connect to our database. Please try again later.")

    except Exception as e:
        logger.exception("Unexpected error in SOP listing.")
        asyncio.create_task(log_interaction(request_id, "list_sops_exception", "Unexpected error in SOP listing",
                                          status="error", response={"error": str(e)}))
        raise HTTPException(status_code=500, detail="We couldn't list your SOPs right now. Please try again in a few minutes.")

@router.get('/departments')
async def get_unique_departments_of_organization(request: Request, payload: Dict[Any, Any] = Depends(verify_token)):
    # Create request_id for logging
    request_id = str(uuid.uuid4())
    user_id = payload.get("sub")
    user_email = payload.get("email", "")
    ip_address = request.client.host
    endpoint = str(request.url)
    method = request.method
    request_params = {"user_id": user_id}

    # Log the user request
    await log_user_request(
        user_id, user_email, ip_address, endpoint, method, request_params, request_id=request_id
    )

    # Log start of function
    asyncio.create_task(log_interaction(request_id, "get_departments_start", "Starting get_unique_departments_of_organization", params=request_params))

    try:
        asyncio.create_task(log_interaction(request_id, "fetch_user_sops_attempt", f"Fetching SOPs for user: {user_id}", params={"user_id": user_id}))

        async with httpx.AsyncClient() as client:
            # Directly fetch SOPs uploaded by user
            sop_response = await client.get(
                f"{SUPABASE_URL}/rest/v1/sops",
                headers=getHeaders(payload),
                params={
                    "uploaded_by": f"eq.{user_id}",
                    "is_deleted": "eq.false",
                    "select": "metadata"
                }
            )

            if sop_response.status_code != 200:
                logger.error(f"SOP fetch failed: {sop_response.text}")
                asyncio.create_task(log_interaction(request_id, "fetch_user_sops_failure", "Failed to fetch user SOPs",
                                                  status="error", response={"status_code": sop_response.status_code, "error": sop_response.text}))
                raise HTTPException(status_code=sop_response.status_code, detail="We couldn't retrieve the details for this SOP. Please check the SOP ID or try again later.")

            sops_data = sop_response.json()
            asyncio.create_task(log_interaction(request_id, "fetch_user_sops_success", f"Successfully fetched user SOPs",
                                              response={"sops_count": len(sops_data)}))

            # Extract unique departments
            asyncio.create_task(log_interaction(request_id, "extract_departments_start", "Extracting unique departments from SOPs"))
            departments = {
                sop.get("metadata", {}).get("department")
                for sop in sops_data
                if sop.get("metadata", {}).get("department")
            }

            departments_list = list(departments)
            asyncio.create_task(log_interaction(request_id, "get_departments_success", "Successfully completed get_unique_departments_of_organization",
                                              response={"unique_departments_count": len(departments_list), "departments": departments_list}))

            return JSONResponse(status_code=200, content={"departments": departments_list})

    except HTTPException as http_error:
        asyncio.create_task(log_interaction(request_id, "get_departments_http_error", "HTTPException in get_unique_departments_of_organization",
                                          status="error", response={"detail": str(http_error.detail)}))
        raise http_error
    except Exception as e:
        logger.exception("Error listing Departments")
        asyncio.create_task(log_interaction(request_id, "get_departments_exception", "Unexpected exception in get_unique_departments_of_organization",
                                          status="error", response={"error": str(e)}))
        raise HTTPException(status_code=500, detail="Something went wrong while listing departments. Please try again later.")

@router.get('/{sop_id}')
async def get_sop(request: Request, sop_id: str, payload: Dict[Any, Any] = Depends(verify_token)):
    # Create request_id for logging
    request_id = str(uuid.uuid4())
    user_id = payload.get("sub")
    user_email = payload.get("email", "")
    ip_address = request.client.host
    endpoint = str(request.url)
    method = request.method
    request_params = {"sop_id": sop_id, "user_id": user_id}

    # Log the user request
    await log_user_request(
        user_id, user_email, ip_address, endpoint, method, request_params, request_id=request_id
    )

    # Log start of function
    asyncio.create_task(log_interaction(request_id, "get_sop_start", f"Starting get_sop for SOP: {sop_id}", params=request_params))

    try:
        asyncio.create_task(log_interaction(request_id, "fetch_sop_attempt", f"Fetching SOP details: {sop_id}", params={"sop_id": sop_id}))

        async with httpx.AsyncClient() as client:
            response = await client.get(f"{SUPABASE_URL}/rest/v1/sops?id=eq.{sop_id}&is_deleted=eq.false", headers=getHeaders(payload))

        if response.status_code != 200:
            asyncio.create_task(log_interaction(request_id, "fetch_sop_failure", f"Failed to fetch SOP: {sop_id}",
                                              status="error", response={"status_code": response.status_code, "error": response.text}))
            raise HTTPException(status_code=response.status_code, detail="We couldn't retrieve the SOP. Please try again later or contact support.")

        sop_data = response.json()

        if not sop_data:
            asyncio.create_task(log_interaction(request_id, "sop_not_found", f"SOP not found: {sop_id}",
                                              status="error", params={"sop_id": sop_id}))
            raise HTTPException(status_code=404, detail="The requested SOP could not be found. It may have been deleted or does not exist.")

        asyncio.create_task(log_interaction(request_id, "get_sop_success", f"Successfully retrieved SOP: {sop_id}",
                                          response={"sop_id": sop_id, "sop_title": sop_data[0].get("title", "N/A")}))

        return JSONResponse(status_code=200, content=sop_data[0])
    except HTTPException as http_error:
        asyncio.create_task(log_interaction(request_id, "get_sop_http_error", f"HTTPException in get_sop: {sop_id}",
                                          status="error", response={"detail": str(http_error.detail)}))
        raise http_error
    except Exception as e:
        logger.exception("Failed to retrieve SOP")
        asyncio.create_task(log_interaction(request_id, "get_sop_exception", f"Unexpected exception in get_sop: {sop_id}",
                                          status="error", response={"error": str(e)}))
        raise HTTPException(status_code=500, detail="Something went wrong while retrieving the SOP. Please try again.")

import os

@router.post('/upload')
async def upload_content(
    request: Request,
    file: UploadFile = File(...),
    title: str = Form(...),
    description: Optional[str] = Form(default=None),
    department: Optional[str] = Form(default=None),
    payload: Dict[Any, Any] = Depends(verify_token)
):
    # Create request_id for logging
    request_id = str(uuid.uuid4())
    user_id = payload.get("sub")
    user_email = payload.get("email", "")
    ip_address = request.client.host
    endpoint = str(request.url)
    method = request.method
    request_params = {"title": title, "description": description, "department": department, "filename": file.filename if file else None}

    # Log the user request
    await log_user_request(
        user_id, user_email, ip_address, endpoint, method, request_params, request_id=request_id
    )

    # Log start of function
    asyncio.create_task(log_interaction(request_id, "upload_content_start", f"Starting upload_content for file: {file.filename if file else 'None'}", params=request_params))

    if not file:
        asyncio.create_task(log_interaction(request_id, "upload_no_file", "No file provided for upload", status="error"))
        raise HTTPException(400, detail="No file was provided for upload. Please select a file and try again.")

    MAX_FILE_SIZE = int(os.getenv("MAX_FILE_SIZE_BYTES", 10 * 1024 * 1024))

    asyncio.create_task(log_interaction(request_id, "file_validation_start", f"Starting file validation for: {file.filename}",
                                      params={"filename": file.filename, "max_file_size": MAX_FILE_SIZE}))

    file_content = await file.read()
    file_size = len(file_content)

    if file_size > MAX_FILE_SIZE:
        asyncio.create_task(log_interaction(request_id, "file_size_exceeded", f"File size exceeds limit: {file.filename}",
                                          status="error", response={"file_size": file_size, "max_size": MAX_FILE_SIZE}))
        return JSONResponse(
            status_code=400,
            content={
                "message": f"File size exceeds the limit of {MAX_FILE_SIZE // (1024 * 1024)} MB.",
                "file_name": file.filename,
                "file_size": file_size
            }
        )

    if not file.filename.lower().endswith(('.docx', '.pdf', '.txt')):
        asyncio.create_task(log_interaction(request_id, "invalid_file_format", f"Invalid file format: {file.filename}",
                                          status="error", response={"filename": file.filename}))
        raise HTTPException(400, detail="The file format is not supported. Please upload a .docx, .pdf, or .txt file.")

    asyncio.create_task(log_interaction(request_id, "file_validation_success", f"File validation passed: {file.filename}",
                                      response={"file_size": file_size, "filename": file.filename}))

    asyncio.create_task(log_interaction(request_id, "fetch_user_details_attempt", f"Fetching user details: {user_id}", params={"user_id": user_id}))

    async with httpx.AsyncClient() as client:
        user_response = await client.get(
            f"{SUPABASE_URL}/rest/v1/users",
            headers=getHeaders(payload),
            params={
                "id": f"eq.{user_id}",
                "select": "id,organization_id"
            }
        )

        if user_response.status_code != 200 or not user_response.json():
            logger.error(f"User fetch failed: {user_response.text}")
            asyncio.create_task(log_interaction(request_id, "fetch_user_details_failure", f"Failed to fetch user details: {user_id}",
                                              status="error", response={"status_code": user_response.status_code, "error": user_response.text}))
            raise HTTPException(400, detail="Unable to verify user details.")

        user_details = user_response.json()[0]
        asyncio.create_task(log_interaction(request_id, "fetch_user_details_success", f"Successfully fetched user details: {user_id}",
                                          response={"organization_id": user_details.get("organization_id")}))

    try:
        asyncio.create_task(log_interaction(request_id, "azure_upload_start", f"Starting Azure blob upload: {file.filename}",
                                          params={"filename": file.filename, "file_size": file_size}))

        content_type = get_content_type(
            file.filename,
            ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain']
        )
        blob_name = f"{uuid.uuid4()}-{file.filename}"
        file_url = upload_to_azure_blob(file_content, blob_name, content_type)

        asyncio.create_task(log_interaction(request_id, "azure_upload_success", f"Successfully uploaded to Azure: {file.filename}",
                                          response={"blob_name": blob_name, "file_url": file_url, "content_type": content_type}))

        metadata = {
            "id": str(uuid.uuid4()),
            "title": title,
            "blob_file_name": blob_name,
            "file_name": file.filename,
            "blob_storage_url": file_url,
            "content_type": content_type,
            "organization_id": user_details["organization_id"],
            "file_size": file_size,
            "uploaded_by": user_details["id"],
            "metadata": {
                "department": department,
                "description": description
            }
        }

        asyncio.create_task(log_interaction(request_id, "metadata_prepared", f"Prepared metadata for SOP: {title}",
                                          params={"sop_id": metadata["id"], "title": title, "department": department}))

        # Save metadata
        asyncio.create_task(log_interaction(request_id, "save_metadata_attempt", f"Saving metadata to database: {title}",
                                          params={"sop_id": metadata["id"], "title": title}))

        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{SUPABASE_URL}/rest/v1/sops",
                headers=getHeaders(payload),
                json=metadata
            )

            if response.status_code != 201:
                logger.error(f"Metadata insertion failed: {response.text}")
                asyncio.create_task(log_interaction(request_id, "save_metadata_failure", f"Failed to save metadata: {title}",
                                                  status="error", response={"status_code": response.status_code, "error": response.text}))
                raise HTTPException(status_code=response.status_code, detail="Failed to save file metadata.")

        asyncio.create_task(log_interaction(request_id, "upload_content_success", f"Successfully uploaded SOP: {title}",
                                          response={"sop_id": metadata["id"], "filename": file.filename, "file_url": file_url}))

        return JSONResponse(status_code=200, content={
            "message": "File uploaded successfully.",
            "file_name": file.filename,
            "blob_storage_url": file_url
        })

    except HTTPException as http_error:
        asyncio.create_task(log_interaction(request_id, "upload_content_http_error", f"HTTPException in upload_content: {file.filename if file else 'None'}",
                                          status="error", response={"detail": str(http_error.detail)}))
        raise http_error
    except Exception as e:
        logger.exception("Upload failed")
        asyncio.create_task(log_interaction(request_id, "upload_content_exception", f"Unexpected exception in upload_content: {file.filename if file else 'None'}",
                                          status="error", response={"error": str(e)}))
        raise HTTPException(status_code=500, detail="Unexpected error during upload. Please try again.")

@router.delete('/{sop_id}')
async def delete_sop(request: Request, sop_id: str, payload: Dict[Any, Any] = Depends(verify_token)):
    # Create request_id for logging
    request_id = str(uuid.uuid4())
    user_id = payload.get("sub")
    user_email = payload.get("email", "")
    ip_address = request.client.host
    endpoint = str(request.url)
    method = request.method
    request_params = {"sop_id": sop_id, "user_id": user_id}

    # Log the user request
    await log_user_request(
        user_id, user_email, ip_address, endpoint, method, request_params, request_id=request_id
    )

    # Log start of function
    asyncio.create_task(log_interaction(request_id, "delete_sop_start", f"Starting delete_sop for SOP: {sop_id}", params=request_params))

    try:
        asyncio.create_task(log_interaction(request_id, "verify_sop_exists_attempt", f"Verifying SOP exists: {sop_id}", params={"sop_id": sop_id}))

        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{SUPABASE_URL}/rest/v1/sops?id=eq.{sop_id}&is_deleted=eq.false",
                headers=getHeaders(payload)
            )

        if response.status_code != 200 or not response.json():
            asyncio.create_task(log_interaction(request_id, "sop_not_found_for_delete", f"SOP not found for deletion: {sop_id}",
                                              status="error", params={"sop_id": sop_id, "status_code": response.status_code}))
            raise HTTPException(response.status_code or 404, detail="SOP not found")

        asyncio.create_task(log_interaction(request_id, "verify_sop_exists_success", f"SOP verified for deletion: {sop_id}",
                                          response={"sop_id": sop_id}))

        update_url = f"{SUPABASE_URL}/rest/v1/sops?id=eq.{sop_id}"

        asyncio.create_task(log_interaction(request_id, "mark_sop_deleted_attempt", f"Marking SOP as deleted: {sop_id}", params={"sop_id": sop_id}))

        async with httpx.AsyncClient() as client:
            response = await client.patch(
                update_url,
                headers={**getHeaders(payload), "Prefer": "return=representation"},
                json={"is_deleted": True}
            )

        if response.status_code != 200:
            logger.error(f"SOP delete failed: {response.text}")
            asyncio.create_task(log_interaction(request_id, "mark_sop_deleted_failure", f"Failed to mark SOP as deleted: {sop_id}",
                                              status="error", response={"status_code": response.status_code, "error": response.text}))
            raise HTTPException(response.status_code or 400, detail="Failed to delete SOP.")

        asyncio.create_task(log_interaction(request_id, "delete_sop_success", f"Successfully deleted SOP: {sop_id}",
                                          response={"sop_id": sop_id}))

        return JSONResponse({
            "message": "SOP marked as deleted.",
            "sop_id": sop_id
        })

    except HTTPException as http_error:
        asyncio.create_task(log_interaction(request_id, "delete_sop_http_error", f"HTTPException in delete_sop: {sop_id}",
                                          status="error", response={"detail": str(http_error.detail)}))
        raise http_error
    except Exception as e:
        logger.exception("Error during SOP deletion")
        asyncio.create_task(log_interaction(request_id, "delete_sop_exception", f"Unexpected exception in delete_sop: {sop_id}",
                                          status="error", response={"error": str(e)}))
        raise HTTPException(status_code=400, detail="Something went wrong while deleting the SOP. Please try again.")

@router.get('/download/{sop_id}')
async def download_sop(request: Request, sop_id: str, payload: Dict[Any, Any] = Depends(verify_token)):
    # Create request_id for logging
    request_id = str(uuid.uuid4())
    user_id = payload.get("sub")
    user_email = payload.get("email", "")
    ip_address = request.client.host
    endpoint = str(request.url)
    method = request.method
    request_params = {"sop_id": sop_id, "user_id": user_id}

    # Log the user request
    await log_user_request(
        user_id, user_email, ip_address, endpoint, method, request_params, request_id=request_id
    )

    # Log start of function
    asyncio.create_task(log_interaction(request_id, "download_sop_start", f"Starting download_sop for SOP: {sop_id}", params=request_params))

    try:
        # Get SOP metadata from Supabase
        asyncio.create_task(log_interaction(request_id, "fetch_sop_metadata_attempt", f"Fetching SOP metadata for download: {sop_id}", params={"sop_id": sop_id}))

        async with httpx.AsyncClient() as client:
            url = f"{SUPABASE_URL}/rest/v1/sops?id=eq.{sop_id}&is_deleted=eq.false"
            response = await client.get(url, headers=getHeaders(payload))

        if response.status_code != 200:
            asyncio.create_task(log_interaction(request_id, "fetch_sop_metadata_failure", f"Failed to fetch SOP metadata: {sop_id}",
                                              status="error", response={"status_code": response.status_code, "error": response.text}))
            raise HTTPException(status_code=response.status_code, detail="Failed to fetch SOP details.")

        sop_data = response.json()

        if not sop_data:
            asyncio.create_task(log_interaction(request_id, "sop_not_found_for_download", f"SOP not found for download: {sop_id}",
                                              status="error", params={"sop_id": sop_id}))
            raise HTTPException(status_code=404, detail="SOP not found")

        sop = sop_data[0]
        blob_url = sop.get("blob_storage_url")
        file_name = sop.get("file_name")
        content_type = sop.get("content_type")

        asyncio.create_task(log_interaction(request_id, "fetch_sop_metadata_success", f"Successfully fetched SOP metadata: {sop_id}",
                                          response={"file_name": file_name, "content_type": content_type}))

        if not blob_url:
            asyncio.create_task(log_interaction(request_id, "blob_url_missing", f"Blob URL missing for SOP: {sop_id}",
                                              status="error", params={"sop_id": sop_id}))
            raise HTTPException(status_code=404, detail="File reference not found")

        # Extract the blob name from the URL
        blob_name = unquote(blob_url.split("/")[-1])

        try:
            asyncio.create_task(log_interaction(request_id, "azure_download_attempt", f"Downloading file from Azure: {file_name}",
                                              params={"blob_name": blob_name, "file_name": file_name}))

            # Download the file content from Azure
            file_content = download_from_azure_blob(blob_name)

            asyncio.create_task(log_interaction(request_id, "download_sop_success", f"Successfully downloaded SOP: {sop_id}",
                                              response={"sop_id": sop_id, "file_name": file_name, "file_size": len(file_content)}))

            # Return the file as a streaming response for download
            return StreamingResponse(
                iter([file_content]),
                media_type=content_type,
                headers={
                    "Content-Disposition": f"attachment; filename={file_name}"
                }
            )
        except Exception as e:
            asyncio.create_task(log_interaction(request_id, "azure_download_failure", f"Failed to download from Azure: {file_name}",
                                              status="error", response={"blob_name": blob_name, "error": str(e)}))
            raise HTTPException(status_code=500, detail="Failed to download file from storage")

    except HTTPException as http_error:
        asyncio.create_task(log_interaction(request_id, "download_sop_http_error", f"HTTPException in download_sop: {sop_id}",
                                          status="error", response={"detail": str(http_error.detail)}))
        raise http_error
    except Exception as e:
        asyncio.create_task(log_interaction(request_id, "download_sop_exception", f"Unexpected exception in download_sop: {sop_id}",
                                          status="error", response={"error": str(e)}))
        raise HTTPException(status_code=500, detail="Something went wrong while downloading. Please try again.")
 