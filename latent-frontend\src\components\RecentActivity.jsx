import React, { useState, useEffect } from 'react';
import activityService from '../services/activityService';
import './RecentActivity.css';

const RecentActivity = () => {
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchRecentActivity();
  }, []);

  const fetchRecentActivity = async () => {
    try {
      setLoading(true);
      const response = await activityService.getRecentActivity();
      setActivities(response.recent_activities || []);
      setError(null);
    } catch (err) {
      console.error('Error fetching recent activity:', err);
      setError('Failed to load recent activity');
    } finally {
      setLoading(false);
    }
  };

  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  const getActivityIcon = (title) => {
    if (title.includes('added SOP')) {
      return '📄';
    } else if (title.includes('Gap analysis')) {
      return '🔍';
    } else {
      return '📋';
    }
  };

  if (loading) {
    return (
      <div className="recent-activity">
        <h3>Recent Activity</h3>
        <div className="loading">Loading recent activity...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="recent-activity">
        <h3>Recent Activity</h3>
        <div className="error">{error}</div>
        <button onClick={fetchRecentActivity}>Retry</button>
      </div>
    );
  }

  return (
    <div className="recent-activity">
      <h3>Recent Activity</h3>
      {activities.length === 0 ? (
        <div className="no-activity">No recent activity found</div>
      ) : (
        <div className="activity-list">
          {activities.map((activity, index) => (
            <div key={index} className="activity-item">
              <div className="activity-icon">
                {getActivityIcon(activity.title)}
              </div>
              <div className="activity-content">
                <div className="activity-description">
                  {activity.title}
                </div>
                <div className="activity-timestamp">
                  {formatTimestamp(activity.timestamp)}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default RecentActivity;
