{"gaps": [{"critique": "The SOP mentions responsibilities for Quality Assurance (QA) and Quality Control (QC) officers and executives, but it does not explicitly define the role of a Quality Unit (QU) as a whole, which is essential for ensuring the various operations associated with all systems are appropriately planned, approved, conducted, and monitored.", "guidelines_reference": "This guidance uses the term quality unit7 (QU) to reflect modern practice while remaining consistent with the CGMP definition in § 210.3(b)(15). The concept of a quality unit is also consistent with modern quality systems in ensuring that the various operations associated with all systems are appropriately planned, approved, conducted, and monitored.", "sop_reference": "3.1 Officer-Quality Assurance\n3.1.1 To prepare validation protocol\n3.1.2 To ensure the activities to be followed as per the approved protocol.\n3.1.3 To withdraw the sample as per sampling program.\n3.2. Executive-Quality Assurance\n3.2.1 To evaluate the analytical report.\n3.2.2 To prepare the validation report.\n3.3 Officer-Quality Control\n3.3.1 To analyze the samples\n3.4 Executive- Quality Control\n3.4.1 To review the protocol\n3.4.2 To check the result of the analysis.", "priority": "Medium"}, {"critique": "The SOP does not explicitly mention the need for a formal process to change procedures in a controlled manner, which is a key element of a robust quality system. Additionally, the SOP does not explicitly mention the need to implement changes to materials (e.g., specification, supplier, or materials handling) through a change control system.", "guidelines_reference": "It is recommended under a modern quality systems approach that a formal process be established to change procedures in a controlled manner. In addition, it is recommended that changes to materials (e.g., specification, supplier, or materials handling) be implemented through a change control system (certain changes require review and approval by the QU (§ 211.100(a)). It is also important to have a system in place to respond to changes in materials from suppliers so that necessary adjustments to the process can be made and unintended consequences avoided.", "sop_reference": "5.4 Prepare a written validation protocol for each critical operation/stage for any related minor operation/stage.\nN/A", "priority": "Medium"}, {"critique": "The SOP does not explicitly mention the need to document control procedures to complete, secure, protect, and archive records, including data, which provide evidence of operational and quality system activities.", "guidelines_reference": "It is also recommended that, when operating under a quality system, manufacturers develop and document control procedures to complete, secure, protect, and archive records, including data, which provide evidence of operational and quality system activities.", "sop_reference": "5.10 File the completed validation document of the process along with the test results of analysis and interpretation confirming its satisfactory\nperformance in a master file for validation of the process along with such other data like stability studies for the process or product.", "priority": "Medium"}, {"critique": "The SOP does not explicitly mention the need for senior management to conduct reviews of the quality system’s performance according to a planned schedule. Also, the SOP does not explicitly mention the need to allocate sufficient resources for quality system and operational activities.", "guidelines_reference": "Under a quality system, senior managers should conduct reviews of the quality system’s performance according to a planned schedule. Such a review typically includes assessments of the process, product, and customer needs (in this section, customer is defined as the recipient of the product and the product is the goods or services provided). Under a robust quality system, sufficient resources should be allocated for quality system and operational activities. Under the model, senior management, or a designee, should be responsible for providing adequate resources for the following:", "sop_reference": "3.5 Manager Quality\n3.5.1 To approve the protocol\n3.5.2 To co-ordinate the activity.\n3.5.3 To review and approve the validation report.\n3.5.4 To organize the training and impart the training before validation.\n3.5 Manager Quality\n3.5.2 To co-ordinate the activity.", "priority": "Medium"}, {"critique": "The SOP does not explicitly mention the need for continued training to ensure that the employees remain proficient in their operational functions and in their understanding of CGMP regulations.", "guidelines_reference": "Under a quality system, continued training is critical to ensure that the employees remain proficient in their operational functions and in their understanding of CGMP regulations. Typical quality systems training should address the policies, processes, procedures, and written instructions related to operational activities, the product/service, the quality system, and the desired work culture (e.g., team building, communication, change, behavior). Under a quality system (and the CGMP regulations), training should focus on both the employees’ specific job functions and the related CGMP regulatory requirements.", "sop_reference": "3.5 Manager Quality\n3.5.4 To organize the training and impart the training before validation.", "priority": "Medium"}, {"critique": "The SOP does not explicitly mention the need for contracts (quality agreements) that clearly describe the materials or service, quality specification responsibilities, and communication mechanisms when outsourcing operations.", "guidelines_reference": "Quality systems call for contracts (quality agreements) that clearly describe the materials or service, quality specification responsibilities, and communication mechanisms.", "sop_reference": "N/A", "priority": "Medium"}, {"critique": "The SOP does not explicitly mention the need to analyze data trends for information on supplier performance. The SOP does not explicitly mention the need for periodic auditing of suppliers based on risk assessment.", "guidelines_reference": "As an essential element of purchasing controls, it is recommended that data trends for acceptance and rejection of materials be analyzed for information on supplier performance. The quality systems approach also calls for periodic auditing of suppliers based on risk assessment. During the audit, a manufacturer can observe the testing or examinations conducted by the supplier to help determine the reliability of the supplier’s COA. An audit should also include a systematic examination of the supplier’s quality system to ensure that reliability is maintained.", "sop_reference": "N/A", "priority": "Medium"}, {"critique": "The SOP does not explicitly mention the need to use risk management to help identify areas of process weakness or higher risk and factors that can influence critical quality attributes that should receive increased scrutiny.", "guidelines_reference": "Risk management can help identify areas of process weakness or higher risk and factors that can influence critical quality attributes that should receive increased scrutiny.", "sop_reference": "5.3 Define the critical factors/operations, which assure the reliability of the process.", "priority": "Medium"}, {"critique": "The SOP does not explicitly mention the need to consider storage and shipment requirements to meet special handling needs prior to completion of manufacturing.", "guidelines_reference": "To maintain quality, the Agency recommends that prior to completion of manufacturing, the manufacturer should consider storage and shipment requirements to meet special handling needs (in the case of pharmaceuticals, one example might be refrigeration).", "sop_reference": "N/A", "priority": "Low"}, {"critique": "The SOP does not explicitly mention the need to use statistical process control to continually monitor quality, identify potential variances before they become problems, bolster data already collected for the annual review, and facilitate improvement throughout the product life cycle.", "guidelines_reference": "Under a quality system, trends should be continually identified and evaluated. One way of accomplishing this is the use of statistical process control. The information from trend analyses can be used to continually monitor quality, identify potential variances before they become problems, bolster data already collected for the annual review, and facilitate improvement throughout the product life cycle. Process capability assessment can serve as a basis for determining the need for changes that can result in process improvements and efficiency (see IV.D.1.).", "sop_reference": "N/A", "priority": "Medium"}, {"critique": "The SOP does not explicitly mention the need to conduct internal audits at planned intervals to evaluate effective implementation and maintenance of the quality system and to determine if processes and products meet established parameters and specifications.", "guidelines_reference": "A quality systems approach calls for audits to be conducted at planned intervals to evaluate effective implementation and maintenance of the quality system and to determine if processes and products meet established parameters and specifications. As with other procedures, audit procedures should be developed and documented to ensure that the planned audit schedule takes into account the relative risks of the various quality system activities, the results of previous audits and corrective actions, and the need to audit the complete system.", "sop_reference": "N/A", "priority": "Medium"}, {"critique": "The SOP does not explicitly mention the need to engage appropriate parties in assessing the risk, including customers, appropriate manufacturing personnel, and other stakeholders.", "guidelines_reference": "It is important to engage appropriate parties in assessing the risk. Such parties include customers, appropriate manufacturing personnel, and other stakeholders.", "sop_reference": "N/A", "priority": "Medium"}, {"critique": "The SOP does not explicitly mention the need to evaluate the effectiveness of the action taken after a corrective action is implemented.", "guidelines_reference": "Quality systems approaches call for procedures to be developed and documented to ensure that the need for action is evaluated relevant to the possible consequences, the root cause of the problem is investigated, possible actions are determined, a selected action is taken within a defined timeframe, and the effectiveness of the action taken is evaluated. It is essential to document corrective actions taken (CGMP also requires this; see § 211.192).", "sop_reference": "5.9 For non-confirming validation data, critically scrutinize the data collected, recommend the corrective actions and revalidate the process.", "priority": "Medium"}, {"critique": "The SOP does not explicitly mention the need for preventive actions such as succession planning, training, capturing institutional knowledge, and planning for personnel, policy, and process changes.", "guidelines_reference": "Being proactive is an essential tool in quality systems management. Succession planning, training, capturing institutional knowledge, and planning for personnel, policy, and process changes are preventive actions that will help ensure that potential problems and root causes are identified, possible consequences assessed, and appropriate actions considered.", "sop_reference": "N/A", "priority": "Medium"}, {"critique": "The SOP lacks detail regarding the specific statistical criteria to be used for sample size and test intervals during process validation, which is essential for ensuring valid estimates of process consistency.", "guidelines_reference": "Sec. 211.110(b) Valid in-process specifications for such characteristics shall be consistent with drug product final specifications and shall be derived from previous acceptable process average and process variability estimates where possible and determined by the application of suitable statistical procedures where appropriate.", "sop_reference": "5.5.12 Test(s) to be performed.", "priority": "Medium"}, {"critique": "The SOP does not explicitly state that the validation protocol and report should be reviewed and approved by the Quality Control Unit, which is a requirement for ensuring that the quality aspects of the validation are adequately addressed.", "guidelines_reference": "Sec. 211.100(a) There shall be written procedures for production and process control designed to assure that the drug products have the identity, strength, quality, and purity they purport or are represented to possess. Such procedures shall include all requirements in this subpart. These written procedures, including any changes, shall be drafted, reviewed, and approved by the appropriate organizational units and reviewed and approved by the quality control unit.", "sop_reference": "3.5 Manager Quality\n3.5.1 To approve the protocol\n3.5.3 To review and approve the validation report.", "priority": "Medium"}, {"critique": "The SOP does not include a requirement to document any deviations from the validation protocol and justify those deviations, which is necessary for maintaining control over the validation process and ensuring data integrity.", "guidelines_reference": "Sec. 211.100(b) Written production and process control procedures shall be followed in the execution of the various production and process control functions and shall be documented at the time of performance. Any deviation from the written procedures shall be recorded and justified.", "sop_reference": "5.6 Run the test as per the written protocols and under conditions conforming to the actual production condition.", "priority": "Medium"}], "gap_steps": [{"critique": "The SOP mentions responsibilities for Quality Assurance (QA) and Quality Control (QC) officers and executives, but it does not explicitly define the role of a Quality Unit (QU) as a whole, which is essential for ensuring the various operations associated with all systems are appropriately planned, approved, conducted, and monitored.", "guidelines_reference": "This guidance uses the term quality unit7 (QU) to reflect modern practice while remaining consistent with the CGMP definition in § 210.3(b)(15). The concept of a quality unit is also consistent with modern quality systems in ensuring that the various operations associated with all systems are appropriately planned, approved, conducted, and monitored.", "sop_reference": "3.1 Officer-Quality Assurance\n3.1.1 To prepare validation protocol\n3.1.2 To ensure the activities to be followed as per the approved protocol.\n3.1.3 To withdraw the sample as per sampling program.\n3.2. Executive-Quality Assurance\n3.2.1 To evaluate the analytical report.\n3.2.2 To prepare the validation report.\n3.3 Officer-Quality Control\n3.3.1 To analyze the samples\n3.4 Executive- Quality Control\n3.4.1 To review the protocol\n3.4.2 To check the result of the analysis.", "priority": "Medium", "missing_step": "Add a new section '3.0 Quality Unit (QU) Responsibilities' before '3.1 Officer-Quality Assurance'. This section should state: 'The Quality Unit (QU) is responsible for ensuring that all validation operations are appropriately planned, approved, conducted, monitored, and documented in accordance with this SOP and relevant GMP guidelines. This includes, but is not limited to, oversight of protocol development, execution, data review, report approval, and ensuring adherence to established quality standards throughout the validation lifecycle.'"}, {"critique": "The SOP does not explicitly mention the need for a formal process to change procedures in a controlled manner, which is a key element of a robust quality system. Additionally, the SOP does not explicitly mention the need to implement changes to materials (e.g., specification, supplier, or materials handling) through a change control system.", "guidelines_reference": "It is recommended under a modern quality systems approach that a formal process be established to change procedures in a controlled manner. In addition, it is recommended that changes to materials (e.g., specification, supplier, or materials handling) be implemented through a change control system (certain changes require review and approval by the QU (§ 211.100(a)). It is also important to have a system in place to respond to changes in materials from suppliers so that necessary adjustments to the process can be made and unintended consequences avoided.", "sop_reference": "5.4 Prepare a written validation protocol for each critical operation/stage for any related minor operation/stage.\nN/A", "priority": "Medium", "missing_step": "Add a new section '5.12 Change Control' after section '5.11 Get the approval of the Quality head for each validation performed'. This section should include the following steps: '5.12.1 Implement changes to this SOP, validation protocols, reports, and materials (including specifications, suppliers, and handling) through a formal change control process. 5.12.2 This process shall include: a) Change request initiation, b) Impact assessment, c) Review and approval by relevant departments including Quality Unit, d) Implementation of the change, e) Documentation of the change, and f) Verification of effectiveness, as applicable.'"}, {"critique": "The SOP does not explicitly mention the need to document control procedures to complete, secure, protect, and archive records, including data, which provide evidence of operational and quality system activities.", "guidelines_reference": "It is also recommended that, when operating under a quality system, manufacturers develop and document control procedures to complete, secure, protect, and archive records, including data, which provide evidence of operational and quality system activities.", "sop_reference": "5.10 File the completed validation document of the process along with the test results of analysis and interpretation confirming its satisfactory\nperformance in a master file for validation of the process along with such other data like stability studies for the process or product.", "priority": "Medium", "missing_step": "Modify section '5.10 File the completed validation document...' to include: '5.10 File the completed validation document of the process along with the test results of analysis and interpretation confirming its satisfactory performance in a master file for validation of the process along with such other data like stability studies for the process or product. Ensure that all validation records and associated data are completed, secured, protected, and archived according to the site's document control procedures to maintain data integrity and facilitate retrieval.'"}, {"critique": "The SOP does not explicitly mention the need for senior management to conduct reviews of the quality system’s performance according to a planned schedule. Also, the SOP does not explicitly mention the need to allocate sufficient resources for quality system and operational activities.", "guidelines_reference": "Under a quality system, senior managers should conduct reviews of the quality system’s performance according to a planned schedule. Such a review typically includes assessments of the process, product, and customer needs (in this section, customer is defined as the recipient of the product and the product is the goods or services provided). Under a robust quality system, sufficient resources should be allocated for quality system and operational activities. Under the model, senior management, or a designee, should be responsible for providing adequate resources for the following:", "sop_reference": "3.5 Manager Quality\n3.5.1 To approve the protocol\n3.5.2 To co-ordinate the activity.\n3.5.3 To review and approve the validation report.\n3.5.4 To organize the training and impart the training before validation.\n3.5 Manager Quality\n3.5.2 To co-ordinate the activity.", "priority": "Medium", "missing_step": "Add a new section '4.2 Senior Management Responsibilities' after '4.1 Head Quality Assurance' under section '4.0 ACCOUNTABILITY'. This section should state: '4.2.1 Senior Management (or designee) is accountable for conducting periodic reviews of the quality system performance, including validation activities, at planned intervals (e.g., annually). 4.2.2 Senior Management is also responsible for allocating sufficient resources, including personnel, equipment, and facilities, for effective implementation and maintenance of the quality system and validation operations.'"}, {"critique": "The SOP does not explicitly mention the need for continued training to ensure that the employees remain proficient in their operational functions and in their understanding of CGMP regulations.", "guidelines_reference": "Under a quality system, continued training is critical to ensure that the employees remain proficient in their operational functions and in their understanding of CGMP regulations. Typical quality systems training should address the policies, processes, procedures, and written instructions related to operational activities, the product/service, the quality system, and the desired work culture (e.g., team building, communication, change, behavior). Under a quality system (and the CGMP regulations), training should focus on both the employees’ specific job functions and the related CGMP regulatory requirements.", "sop_reference": "3.5 Manager Quality\n3.5.4 To organize the training and impart the training before validation.", "priority": "Medium", "missing_step": "Modify section '3.5.4 To organize the training and impart the training before validation' under '3.5 Manager Quality' to: '3.5.4 To establish, organize, and maintain a system for initial and ongoing training for all personnel involved in validation activities. This training shall ensure proficiency in their respective functions, understanding of this SOP, relevant GMP regulations, and any updates or changes to procedures and regulations.'"}, {"critique": "The SOP does not explicitly mention the need for contracts (quality agreements) that clearly describe the materials or service, quality specification responsibilities, and communication mechanisms when outsourcing operations.", "guidelines_reference": "Quality systems call for contracts (quality agreements) that clearly describe the materials or service, quality specification responsibilities, and communication mechanisms.", "sop_reference": "N/A", "priority": "Medium", "missing_step": "Add a new section '5.13 Quality Agreements for Outsourced Activities' after section '5.12 Change Control' (or '5.11 Get the approval of the Quality head...' if change control is not added yet). This section should state: '5.13.1 When outsourcing any validation-related activities (e.g., testing, consulting), ensure that formal quality agreements are in place with the external contractor. 5.13.2 These agreements must clearly define the scope of work, materials or services provided, quality and specification responsibilities of each party, communication protocols, and requirements for data sharing and GMP compliance.'"}, {"critique": "The SOP does not explicitly mention the need to analyze data trends for information on supplier performance. The SOP does not explicitly mention the need for periodic auditing of suppliers based on risk assessment.", "guidelines_reference": "As an essential element of purchasing controls, it is recommended that data trends for acceptance and rejection of materials be analyzed for information on supplier performance. The quality systems approach also calls for periodic auditing of suppliers based on risk assessment. During the audit, a manufacturer can observe the testing or examinations conducted by the supplier to help determine the reliability of the supplier’s COA. An audit should also include a systematic examination of the supplier’s quality system to ensure that reliability is maintained.", "sop_reference": "N/A", "priority": "Medium", "missing_step": "Add a new subsection '5.7.1 Supplier Performance Monitoring' after section '5.7 Verify the performance of the critical factors...'. This subsection should include: '5.7.1.1 Analyze data trends related to the acceptance and rejection of materials used in the validated process to monitor supplier performance. 5.7.1.2 Conduct periodic audits of critical material suppliers based on a risk assessment to evaluate their quality systems and ensure consistent material quality. These audits should verify supplier's testing reliability and GMP compliance.'"}, {"critique": "The SOP does not explicitly mention the need to use risk management to help identify areas of process weakness or higher risk and factors that can influence critical quality attributes that should receive increased scrutiny.", "guidelines_reference": "Risk management can help identify areas of process weakness or higher risk and factors that can influence critical quality attributes that should receive increased scrutiny.", "sop_reference": "5.3 Define the critical factors/operations, which assure the reliability of the process.", "priority": "Medium", "missing_step": "Modify section '5.3 Define the critical factors/operations...' to include: '5.3 Define the critical factors/operations, which assure the reliability of the process. Employ risk management principles and tools to systematically identify potential areas of process weakness, higher risk operations, and factors that could influence critical quality attributes. This risk assessment should guide the focus and intensity of validation efforts.'"}, {"critique": "The SOP does not explicitly mention the need to consider storage and shipment requirements to meet special handling needs prior to completion of manufacturing.", "guidelines_reference": "To maintain quality, the Agency recommends that prior to completion of manufacturing, the manufacturer should consider storage and shipment requirements to meet special handling needs (in the case of pharmaceuticals, one example might be refrigeration).", "sop_reference": "N/A", "priority": "Low", "missing_step": "Modify section '5.6 Run the test as per the written protocols...' to include: '5.6 Run the test as per the written protocols and under conditions conforming to the actual production condition. Consider and document any specific storage and shipment requirements necessary to maintain the quality of in-process materials and finished products prior to the completion of manufacturing, especially for products requiring special handling (e.g., temperature control).'"}, {"critique": "The SOP does not explicitly mention the need to use statistical process control to continually monitor quality, identify potential variances before they become problems, bolster data already collected for the annual review, and facilitate improvement throughout the product life cycle.", "guidelines_reference": "Under a quality system, trends should be continually identified and evaluated. One way of accomplishing this is the use of statistical process control. The information from trend analyses can be used to continually monitor quality, identify potential variances before they become problems, bolster data already collected for the annual review, and facilitate improvement throughout the product life cycle. Process capability assessment can serve as a basis for determining the need for changes that can result in process improvements and efficiency (see IV.D.1.).", "sop_reference": "N/A", "priority": "Medium", "missing_step": "Add a new subsection '5.7.2 Statistical Process Control (SPC)' after subsection '5.7.1 Supplier Performance Monitoring' (or after section '5.7 Verify the performance...' if 5.7.1 is not added). This subsection should include: '5.7.2.1 Implement Statistical Process Control (SPC) methodologies to continuously monitor critical process parameters and quality attributes identified during validation. 5.7.2.2 Utilize SPC data to identify trends, detect potential process variances before they exceed control limits, and proactively implement corrective actions. 5.7.2.3 SPC data should also be used to support annual product quality reviews and drive continuous process improvement throughout the product lifecycle.'"}, {"critique": "The SOP does not explicitly mention the need to conduct internal audits at planned intervals to evaluate effective implementation and maintenance of the quality system and to determine if processes and products meet established parameters and specifications.", "guidelines_reference": "A quality systems approach calls for audits to be conducted at planned intervals to evaluate effective implementation and maintenance of the quality system and to determine if processes and products meet established parameters and specifications. As with other procedures, audit procedures should be developed and documented to ensure that the planned audit schedule takes into account the relative risks of the various quality system activities, the results of previous audits and corrective actions, and the need to audit the complete system.", "sop_reference": "N/A", "priority": "Medium", "missing_step": "Add a new section '5.14 Internal Quality Audits' after section '5.13 Quality Agreements for Outsourced Activities' (or '5.11 Get the approval of the Quality head...' if 5.13 and 5.12 are not added yet). This section should state: '5.14.1 Conduct internal quality audits at planned intervals to assess the effective implementation and maintenance of the quality system, including validation processes. 5.14.2 These audits should evaluate compliance with this SOP, GMP regulations, and determine if validated processes and products consistently meet established parameters and specifications. 5.14.3 Audit schedules should be risk-based, considering the criticality of processes and results of previous audits.'"}, {"critique": "The SOP does not explicitly mention the need to engage appropriate parties in assessing the risk, including customers, appropriate manufacturing personnel, and other stakeholders.", "guidelines_reference": "It is important to engage appropriate parties in assessing the risk. Such parties include customers, appropriate manufacturing personnel, and other stakeholders.", "sop_reference": "N/A", "priority": "Medium", "missing_step": "Modify section '5.3 Define the critical factors/operations...' to include: '5.3 Define the critical factors/operations, which assure the reliability of the process. Risk assessment activities should involve appropriate parties, including but not limited to, relevant manufacturing personnel, Quality Unit representatives, and potentially external stakeholders or customers, to ensure a comprehensive and multi-faceted evaluation of risks.'"}, {"critique": "The SOP does not explicitly mention the need to evaluate the effectiveness of the action taken after a corrective action is implemented.", "guidelines_reference": "Quality systems approaches call for procedures to be developed and documented to ensure that the need for action is evaluated relevant to the possible consequences, the root cause of the problem is investigated, possible actions are determined, a selected action is taken within a defined timeframe, and the effectiveness of the action taken is evaluated. It is essential to document corrective actions taken (CGMP also requires this; see § 211.192).", "sop_reference": "5.9 For non-confirming validation data, critically scrutinize the data collected, recommend the corrective actions and revalidate the process.", "priority": "Medium", "missing_step": "Modify section '5.9 For non-confirming validation data...' to include: '5.9 For non-confirming validation data, critically scrutinize the data collected, recommend the corrective actions and revalidate the process. Following implementation of corrective actions, evaluate and document their effectiveness in addressing the identified non-conformances and preventing recurrence. Re-validation may be necessary to confirm the effectiveness of corrective actions.'"}, {"critique": "The SOP does not explicitly mention the need for preventive actions such as succession planning, training, capturing institutional knowledge, and planning for personnel, policy, and process changes.", "guidelines_reference": "Being proactive is an essential tool in quality systems management. Succession planning, training, capturing institutional knowledge, and planning for personnel, policy, and process changes are preventive actions that will help ensure that potential problems and root causes are identified, possible consequences assessed, and appropriate actions considered.", "sop_reference": "N/A", "priority": "Medium", "missing_step": "Add a new section '5.15 Preventive Actions' after section '5.14 Internal Quality Audits' (or '5.11 Get the approval of the Quality head...' if 5.14, 5.13, 5.12 are not added yet). This section should state: '5.15.1 Implement proactive preventive actions to identify and mitigate potential quality issues before they occur. 5.15.2 These actions may include, but are not limited to, succession planning for key personnel, proactive training programs, mechanisms for capturing and retaining institutional knowledge related to validation, and planning for anticipated personnel, policy, and process changes that could impact validation status.'"}, {"critique": "The SOP lacks detail regarding the specific statistical criteria to be used for sample size and test intervals during process validation, which is essential for ensuring valid estimates of process consistency.", "guidelines_reference": "Sec. 211.110(b) Valid in-process specifications for such characteristics shall be consistent with drug product final specifications and shall be derived from previous acceptable process average and process variability estimates where possible and determined by the application of suitable statistical procedures where appropriate.", "sop_reference": "5.5.12 Test(s) to be performed.", "priority": "Medium", "missing_step": "Modify section '5.5.12 Test(s) to be performed.' under '5.5 A validation protocol includes the following:' to: '5.5.12 Test(s) to be performed and Statistical Rationale: Clearly define all tests to be performed during validation. Include a justification for the selected sample sizes and testing intervals, outlining the statistical criteria used to ensure that the data collected will provide a valid estimate of process consistency and capability. Reference relevant statistical procedures as appropriate.'"}, {"critique": "The SOP does not explicitly state that the validation protocol and report should be reviewed and approved by the Quality Control Unit, which is a requirement for ensuring that the quality aspects of the validation are adequately addressed.", "guidelines_reference": "Sec. 211.100(a) There shall be written procedures for production and process control designed to assure that the drug products have the identity, strength, quality, and purity they purport or are represented to possess. Such procedures shall include all requirements in this subpart. These written procedures, including any changes, shall be drafted, reviewed, and approved by the appropriate organizational units and reviewed and approved by the quality control unit.", "sop_reference": "3.5 Manager Quality\n3.5.1 To approve the protocol\n3.5.3 To review and approve the validation report.", "priority": "Medium", "missing_step": "Modify section '3.4 Executive- Quality Control' and '3.5 Manager Quality' to reflect QC review and final QA approval. Change '3.4 Executive- Quality Control' to: '3.4 Executive- Quality Control: 3.4.1 To review the validation protocol for QC aspects. 3.4.2 To check the result of the analysis. 3.4.3 To approve the validation protocol from a QC perspective. 3.4.4 To review and approve the validation report from a QC perspective.' Modify '3.5 Manager Quality' to: '3.5 Manager Quality: 3.5.1 To approve the protocol after QC approval. 3.5.2 To co-ordinate the activity. 3.5.3 To review and approve the validation report after QC approval. 3.5.4 To organize the training and impart the training before validation.'"}, {"critique": "The SOP does not include a requirement to document any deviations from the validation protocol and justify those deviations, which is necessary for maintaining control over the validation process and ensuring data integrity.", "guidelines_reference": "Sec. 211.100(b) Written production and process control procedures shall be followed in the execution of the various production and process control functions and shall be documented at the time of performance. Any deviation from the written procedures shall be recorded and justified.", "sop_reference": "5.6 Run the test as per the written protocols and under conditions conforming to the actual production condition.", "priority": "Medium", "missing_step": "Modify section '5.6 Run the test as per the written protocols...' to include: '5.6 Run the test as per the written protocols and under conditions conforming to the actual production condition. Any deviation from the approved validation protocol during execution must be documented at the time of occurrence. The deviation report shall include a clear description of the deviation, the reason for the deviation, and a justification for its acceptability or the impact assessment and corrective action if needed. All deviations must be reviewed and approved by the Quality Unit.'"}]}