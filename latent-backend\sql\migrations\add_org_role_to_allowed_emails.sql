-- Add org_role column to allowed_emails table
-- This migration adds the org_role field to allowed_emails table and updates the user creation trigger

-- Add org_role column to allowed_emails table
ALTER TABLE allowed_emails ADD COLUMN org_role TEXT NOT NULL DEFAULT 'admin';

-- Update the handle_new_user function to also copy org_role from allowed_emails to users table
CREATE OR REPLACE FUNCTION public.handle_new_user()
RET<PERSON>NS trigger AS $$
DECLARE
  org_id UUID;
  fname TEXT;
  lname TEXT;
  user_org_role TEXT;
BEGIN
  -- Get organization ID and org_role for this email from allowed_emails table
  SELECT organization_id, org_role INTO org_id, user_org_role
  FROM public.allowed_emails
  WHERE email = NEW.email AND is_active = true
  LIMIT 1;

  -- Extract first and last name from raw_user_meta_data
  fname := NEW.raw_user_meta_data ->> 'first_name';
  lname := NEW.raw_user_meta_data ->> 'last_name';

  -- Insert into public.users with all required fields including org_role
  INSERT INTO public.users (id, email, organization_id, first_name, last_name, org_role)
  VALUES (NEW.id, NEW.email, org_id, fname, lname, COALESCE(user_org_role, 'admin'));

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
