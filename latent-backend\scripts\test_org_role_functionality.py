#!/usr/bin/env python3
"""
Test script to verify org_role functionality in allowed_emails and user creation.

This script tests:
1. Adding emails with different org_roles to allowed_emails
2. Verifying that org_role is stored correctly in allowed_emails
3. Testing that user creation copies org_role from allowed_emails to users table
"""

import os
import sys
import asyncio
from datetime import datetime
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def setup_supabase() -> Client:
    """Set up Supabase client."""
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_service_key = os.getenv("SUPABASE_SERVICE_KEY")
    
    if not supabase_url or not supabase_service_key:
        print("❌ Error: Missing SUPABASE_URL or SUPABASE_SERVICE_KEY environment variables")
        sys.exit(1)
    
    return create_client(supabase_url, supabase_service_key)

def get_organization_id(supabase: Client) -> str:
    """Get the first available organization ID."""
    try:
        result = supabase.table("organizations").select("id").limit(1).execute()
        if result.data:
            return result.data[0]["id"]
        else:
            print("❌ Error: No organizations found. Please create an organization first.")
            return None
    except Exception as e:
        print(f"❌ Error getting organization ID: {str(e)}")
        return None

def test_add_email_with_role(supabase: Client, org_id: str, role: str) -> str:
    """Test adding an email with a specific org_role."""
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        test_email = f"test_{role}_{timestamp}@example.com"
        
        print(f"\n🧪 Testing email addition with role '{role}'")
        print(f"   Email: {test_email}")
        
        data = {
            "email": test_email,
            "organization_id": org_id,
            "org_role": role,
            "added_by": "test_script"
        }
        
        result = supabase.table("allowed_emails").insert(data).execute()
        
        if result.data:
            print(f"✅ Successfully added email with role '{role}'")
            return test_email
        else:
            print(f"❌ Failed to add email: {result}")
            return None
            
    except Exception as e:
        print(f"❌ Error adding email with role '{role}': {str(e)}")
        return None

def verify_allowed_email_role(supabase: Client, email: str, expected_role: str) -> bool:
    """Verify that the email has the correct org_role in allowed_emails table."""
    try:
        result = supabase.table("allowed_emails").select("org_role").eq("email", email).execute()
        
        if result.data:
            actual_role = result.data[0].get("org_role")
            if actual_role == expected_role:
                print(f"✅ Verified: Email {email} has correct role '{actual_role}' in allowed_emails")
                return True
            else:
                print(f"❌ Error: Email {email} has role '{actual_role}', expected '{expected_role}'")
                return False
        else:
            print(f"❌ Error: Email {email} not found in allowed_emails")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying allowed email role: {str(e)}")
        return False

def simulate_user_creation(supabase: Client, email: str, expected_role: str) -> bool:
    """
    Simulate user creation by directly calling the handle_new_user logic.
    Note: This is a simplified test since we can't easily trigger the actual auth trigger.
    """
    try:
        print(f"\n🔄 Simulating user creation for {email}")
        
        # Get the org_role from allowed_emails (simulating what the trigger does)
        result = supabase.table("allowed_emails").select("organization_id, org_role").eq("email", email).eq("is_active", True).execute()
        
        if not result.data:
            print(f"❌ Email {email} not found in allowed_emails or is inactive")
            return False
        
        org_id = result.data[0].get("organization_id")
        org_role = result.data[0].get("org_role", "admin")
        
        print(f"   Found in allowed_emails: org_id={org_id}, org_role={org_role}")
        
        # Generate a test user ID
        import uuid
        test_user_id = str(uuid.uuid4())
        
        # Insert into users table (simulating what the trigger would do)
        user_data = {
            "id": test_user_id,
            "email": email,
            "organization_id": org_id,
            "first_name": "Test",
            "last_name": "User",
            "org_role": org_role
        }
        
        user_result = supabase.table("users").insert(user_data).execute()
        
        if user_result.data:
            actual_role = user_result.data[0].get("org_role")
            if actual_role == expected_role:
                print(f"✅ User created successfully with correct role '{actual_role}'")
                
                # Clean up the test user
                supabase.table("users").delete().eq("id", test_user_id).execute()
                print(f"🧹 Cleaned up test user")
                
                return True
            else:
                print(f"❌ User created with role '{actual_role}', expected '{expected_role}'")
                # Clean up the test user
                supabase.table("users").delete().eq("id", test_user_id).execute()
                return False
        else:
            print(f"❌ Failed to create user: {user_result}")
            return False
            
    except Exception as e:
        print(f"❌ Error simulating user creation: {str(e)}")
        return False

def cleanup_test_data(supabase: Client, emails: list):
    """Clean up test emails from allowed_emails table."""
    print(f"\n🧹 Cleaning up test data...")
    for email in emails:
        try:
            supabase.table("allowed_emails").delete().eq("email", email).execute()
            print(f"   Cleaned up: {email}")
        except Exception as e:
            print(f"   Warning: Could not clean up {email}: {str(e)}")

def main():
    """Main test function."""
    print("🧪 Testing org_role functionality in allowed_emails and user creation")
    print("=" * 70)
    
    # Setup
    supabase = setup_supabase()
    org_id = get_organization_id(supabase)
    
    if not org_id:
        return
    
    print(f"📋 Using organization ID: {org_id}")
    
    # Test different roles
    test_roles = ["admin", "user", "viewer"]
    test_emails = []
    all_tests_passed = True
    
    for role in test_roles:
        print(f"\n{'='*50}")
        print(f"Testing role: {role}")
        print(f"{'='*50}")
        
        # Test 1: Add email with specific role
        email = test_add_email_with_role(supabase, org_id, role)
        if not email:
            all_tests_passed = False
            continue
        
        test_emails.append(email)
        
        # Test 2: Verify role in allowed_emails
        if not verify_allowed_email_role(supabase, email, role):
            all_tests_passed = False
            continue
        
        # Test 3: Simulate user creation and verify role transfer
        if not simulate_user_creation(supabase, email, role):
            all_tests_passed = False
            continue
    
    # Summary
    print(f"\n{'='*70}")
    print("🎯 Test Summary")
    print(f"{'='*70}")
    
    if all_tests_passed:
        print("✅ ALL TESTS PASSED - org_role functionality is working correctly!")
        print("\n📋 What was tested:")
        print("   ✅ Adding emails with different org_roles to allowed_emails")
        print("   ✅ Verifying org_role storage in allowed_emails table")
        print("   ✅ Simulating user creation with org_role transfer")
    else:
        print("❌ SOME TESTS FAILED - org_role functionality needs attention")
    
    # Cleanup
    cleanup_test_data(supabase, test_emails)
    
    print(f"\n🏁 Testing completed!")

if __name__ == "__main__":
    main()
