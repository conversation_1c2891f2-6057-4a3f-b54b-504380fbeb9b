/* Custom styles for the inactivity warning toast */
.inactivity-warning-toast {
  background-color: #fff3cd !important;
  color: #856404 !important;
  border-left: 4px solid #ffc107 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  width: 400px !important;
  max-width: 90% !important;
}

.inactivity-warning-content {
  padding: 8px 0;
}

.inactivity-warning-content p {
  margin: 6px 0;
  line-height: 1.4;
}

.inactivity-warning-content strong {
  font-weight: 600;
  font-size: 16px;
}

/* Style for the countdown timer */
.countdown-timer {
  font-weight: 700;
  color: #dc3545;
  font-size: 18px;
  display: inline-block;
  min-width: 30px;
  text-align: center;
}

/* Style for the auto-logout toast */
.Toastify__toast--info.auto-logout {
  background-color: #d1ecf1 !important;
  color: #0c5460 !important;
  border-left: 4px solid #17a2b8 !important;
}

/* Add a pulsing animation to make the warning more noticeable */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
  }
}

.inactivity-warning-toast {
  animation: pulse 2s infinite;
}

/* Add a flashing animation for the countdown when it's below 10 seconds */
@keyframes flash {
  0%, 50%, 100% {
    opacity: 1;
  }
  25%, 75% {
    opacity: 0.5;
  }
}

.countdown-timer.urgent {
  animation: flash 1s infinite;
}

/* Overlay for the timeout warning */
.timeout-warning-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

/* Container for the timeout warning */
.timeout-warning-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  width: 400px;
  max-width: 90%;
  overflow: hidden;
  animation: slide-up 0.3s ease-out;
}

@keyframes slide-up {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Header of the warning */
.timeout-warning-header {
  background-color: #fff3cd;
  color: #856404;
  padding: 16px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #ffeeba;
}

.warning-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  color: #856404;
}

.timeout-warning-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

/* Body of the warning */
.timeout-warning-body {
  padding: 20px;
  text-align: center;
}

.timeout-warning-body p {
  margin: 0 0 16px;
  color: #333;
  font-size: 15px;
}

.timeout-countdown {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 20px 0;
}

.countdown-number {
  font-size: 48px;
  font-weight: 700;
  color: #dc3545;
  line-height: 1;
  display: block;
  margin-bottom: 8px;
}

.countdown-text {
  font-size: 14px;
  color: #666;
}

.timeout-instruction {
  font-style: italic;
  color: #666 !important;
  font-size: 14px !important;
}

/* Footer with buttons */
.timeout-warning-footer {
  padding: 16px;
  display: flex;
  justify-content: space-between;
  border-top: 1px solid #eee;
  background-color: #f8f9fa;
}

.btn-stay-logged-in {
  padding: 10px 16px;
  background-color: #6c63ff;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-stay-logged-in:hover {
  background-color: #5a52d5;
}

.btn-logout {
  padding: 10px 16px;
  background-color: transparent;
  color: #6c757d;
  border: 1px solid #6c757d;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-logout:hover {
  background-color: #6c757d;
  color: white;
}

/* Animation for urgent countdown */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.countdown-number.urgent {
  animation: pulse 1s infinite;
} 