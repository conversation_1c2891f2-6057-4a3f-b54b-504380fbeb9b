-- Enable UUID extension if not already enabled
create extension if not exists "uuid-ossp";

-- Table: user_requests
create table if not exists public.user_requests (
    id uuid primary key default uuid_generate_v4(),
    user_id text,
    user_email text,
    ip_address text,
    endpoint text,
    method text,
    request_params jsonb,
    created_at timestamptz default now()
);

-- Table: request_interactions
create table if not exists public.request_interactions (
    id uuid primary key default uuid_generate_v4(),
    request_id uuid references public.user_requests(id) on delete cascade,
    step text,
    description text,
    params jsonb,
    response jsonb,
    status text,
    real_error text default null,
    created_at timestamptz default now()
);

-- Optional: Indexes for faster querying
create index if not exists idx_request_interactions_request_id on public.request_interactions(request_id);
create index if not exists idx_user_requests_user_id on public.user_requests(user_id);