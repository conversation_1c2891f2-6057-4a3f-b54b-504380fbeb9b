"""
Example script demonstrating secure email allowlist enforcement for Supabase Auth

This example shows how to implement a secure signup flow where:
1. Email allowlist is enforced at the database level (not frontend)
2. Frontend uses only anonymous/public key to interact with Supabase
3. Database triggers prevent unauthorized signups and assign organization IDs

The key security benefit is that even if a user bypasses the frontend checks,
they cannot create an account unless their email is in the allowlist.
"""

import os
from supabase import create_client, Client
from dotenv import load_dotenv
import json
import random
import string

# Load environment variables
load_dotenv()

# Initialize Supabase client with public key (what frontend would use)
supabase_url = os.environ.get("SUPABASE_URL")
supabase_key = os.environ.get("SUPABASE_PUBLIC_KEY")
supabase: Client = create_client(supabase_url, supabase_key)

# For admin operations only - NOT used in frontend code

def signup_user(email: str, password: str):
    """
    Sign up a new user with Supabase
    
    Security note: 
    - Frontend only uses public key
    - Backend validation through database triggers enforces the allowlist
    - If email is not in allowed_emails table, signup will fail
    """
    try:
        print(f"Attempting to sign up: {email}")
        response = supabase.auth.sign_up({
            "email": email,
            "password": password,
        })
        
        print("✅ Signup successful!")
        return {
            "success": True,
            "user": response.user,
            "session": response.session
        }
        
    except Exception as e:
        error_msg = str(e)
        print(f"❌ Error during signup: {error_msg}")
        
        # Format user-friendly error message
        if "Email not authorized" in error_msg:
            return {"success": False, "error": "Email not on the allowed list"}
        elif "already registered" in error_msg:
            return {"success": False, "error": "Email already registered"}
        else:
            return {"success": False, "error": error_msg}



# Example usage
if __name__ == "__main__":
    # Generate a random test email
    random_email = f"user{random.randint(100000, 999999)}@example.com"
    allowed_email = "<EMAIL>"
    
    # Check allowlist status
    # Test signup with unauthorized email (should fail at database level)
    # print("\n=== Test 1: Unauthorized Email Signup ===")
    # result1 = signup_user(random_email, "securePassword123!")
    # print(f"Result: {json.dumps(result1, indent=2, default=str)}\n")
    
    # Test signup with authorized email
    print("=== Test 2: Authorized Email Signup ===")
    result2 = signup_user(allowed_email, "securePassword123!")
    print(f"Result: {json.dumps(result2, indent=2, default=str)}")
    
    print("\nThis example demonstrates how the email allowlist is enforced at the database level.")
    print("Even if someone bypasses frontend checks, they cannot create an account unless their")
    print("email is in the allowed_emails table.")