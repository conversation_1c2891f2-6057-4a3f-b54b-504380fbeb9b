from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import os
from app.api import user_management, regulation, sop_storage, gap_analysis
import logging
app = FastAPI(redirect_slashes=False)


# Add CORS middleware to allow cross-origin requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        os.getenv("FRONTEND_URL", "https://lemon-field-03dd76e00.6.azurestaticapps.net"),
        "http://localhost:3000",
        "http://127.0.0.1:3000"
    ],  # Explicitly list allowed origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(user_management.router, prefix='/user')
app.include_router(regulation.router, prefix='/regulations')
app.include_router(sop_storage.router, prefix='/sops')
app.include_router(gap_analysis.router, prefix='/analysis')

@app.get('/')
async def health_check():
    """Health check endpoint to verify the API is running"""
    return {"status": "Latent backend is running"}
@app.options('/')
async def options_health_check(request: Request):
    """Handle preflight CORS requests for the health check endpoint"""
    origin = request.headers.get("origin", "")
    # Dynamic origin handling
    allowed_origins = [
        os.getenv("FRONTEND_URL", "https://lemon-field-03dd76e00.6.azurestaticapps.net"),
        "http://localhost:3000",
        "http://127.0.0.1:3000"
    ]
    
    cors_origin = origin if origin in allowed_origins else allowed_origins[0]
    
    return JSONResponse(
        content={"message": "CORS preflight request successful"},
        headers={
            "Access-Control-Allow-Origin": cors_origin,
            "Access-Control-Allow-Methods": "GET, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type, Authorization",
        },
    )

@app.middleware("http")
async def add_cors_headers(request: Request, call_next):
    """Add CORS headers to all responses for debugging"""
    response = await call_next(request)
    # Log the request for debugging
    print(f"Request: {request.method} {request.url.path} - Headers: {request.headers.get('origin', 'No origin')}")
    return response


if __name__ == "__main__":
    uvicorn.run("app.main:app", host="0.0.0.0", reload=True)