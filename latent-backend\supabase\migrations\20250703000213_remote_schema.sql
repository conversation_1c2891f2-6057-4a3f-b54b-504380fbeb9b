SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;
CREATE EXTENSION IF NOT EXISTS "pgsodium";
COMMENT ON SCHEMA "public" IS 'standard public schema';
CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";
CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";
CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";
CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";
CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";
CREATE OR REPLACE FUNCTION "public"."check_if_email_is_allowed"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$DECLARE
  email_exists BOOLEAN;
  org_id UUID;
BEGIN
  -- Check if the email exists in the allowed_emails table
  SELECT EXISTS (
    SELECT 1
    FROM public.allowed_emails
    WHERE email = NEW.email
    AND is_active = true
    AND (expiry_date IS NULL OR expiry_date > now())
  ) INTO email_exists;

  -- If email doesn't exist in the allowed list, raise an exception
  IF NOT email_exists THEN
    RAISE EXCEPTION 'Email not authorized to sign up.';
  END IF;
  
  -- We don't need to set the organization_id here,
  -- as the handle_new_user trigger will handle that
  
  RETURN NEW;
END;$$;
ALTER FUNCTION "public"."check_if_email_is_allowed"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."get_auth_user_org_id"() RETURNS "uuid"
    LANGUAGE "sql" SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
  SELECT organization_id FROM users WHERE id = auth.uid()
$$;
ALTER FUNCTION "public"."get_auth_user_org_id"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."handle_new_user"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  org_id UUID;
  fname TEXT;
  lname TEXT;
BEGIN
  -- Get organization ID for this email from allowed_emails table
  SELECT organization_id INTO org_id 
  FROM public.allowed_emails
  WHERE email = NEW.email AND is_active = true
  LIMIT 1;

  -- Extract first and last name from raw_user_meta_data
  fname := NEW.raw_user_meta_data ->> 'first_name';
  lname := NEW.raw_user_meta_data ->> 'last_name';

  -- Insert into public.users with all required fields
  INSERT INTO public.users (id, email, organization_id, first_name, last_name)
  VALUES (NEW.id, NEW.email, org_id, fname, lname);

  RETURN NEW;
END;
$$;
ALTER FUNCTION "public"."handle_new_user"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."update_updated_at_column"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;
ALTER FUNCTION "public"."update_updated_at_column"() OWNER TO "postgres";
CREATE OR REPLACE FUNCTION "public"."update_updated_at_column_for_gap_analysis_results"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;
ALTER FUNCTION "public"."update_updated_at_column_for_gap_analysis_results"() OWNER TO "postgres";
SET default_tablespace = '';
SET default_table_access_method = "heap";
CREATE TABLE IF NOT EXISTS "public"."allowed_emails" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "email" "text" NOT NULL,
    "organization_id" "uuid",
    "is_active" boolean DEFAULT true,
    "expiry_date" timestamp with time zone,
    "added_at" timestamp with time zone DEFAULT "now"(),
    "added_by" "text"
);
ALTER TABLE "public"."allowed_emails" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."gap_analysis_results" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "sop_id" "uuid",
    "regulation_id" "uuid",
    "gap_details" "jsonb",
    "compliance_score" integer,
    "analyzed_at" timestamp with time zone DEFAULT "now"(),
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "regulation_refs" "jsonb" DEFAULT '[]'::"jsonb" NOT NULL,
    "status" "text" DEFAULT 'processing'::"text" NOT NULL,
    "changed_gap_detail" "jsonb" DEFAULT '[]'::"jsonb",
    "updated_at" timestamp with time zone,
    CONSTRAINT "gap_analysis_results_compliance_score_check" CHECK ((("compliance_score" >= 0) AND ("compliance_score" <= 100)))
);
ALTER TABLE "public"."gap_analysis_results" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."organization_regulations" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "organization_id" "uuid",
    "regulation_id" "uuid",
    "is_applicable" boolean DEFAULT true,
    "customization_notes" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "last_updated" timestamp with time zone DEFAULT "now"(),
    "metadata" "jsonb" DEFAULT '{}'::"jsonb"
);
ALTER TABLE "public"."organization_regulations" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."organizations" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "industry" "text",
    "location" "text",
    "size" integer,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "metadata" "jsonb" DEFAULT '{}'::"jsonb"
);
ALTER TABLE "public"."organizations" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."regulations" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "industry" "text",
    "region" "text",
    "compliance_type" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "uploaded_by" "uuid",
    "source" "text"
);
ALTER TABLE "public"."regulations" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."request_interactions" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "request_id" "uuid",
    "step" "text",
    "description" "text",
    "params" "jsonb",
    "response" "jsonb",
    "status" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "real_error" "text"
);
ALTER TABLE "public"."request_interactions" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."sops" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "title" "text" NOT NULL,
    "file_name" "text" NOT NULL,
    "blob_storage_url" "text" NOT NULL,
    "content_type" "text" NOT NULL,
    "file_size" integer NOT NULL,
    "organization_id" "uuid",
    "uploaded_by" "uuid",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "is_deleted" boolean DEFAULT false,
    "blob_file_name" "text",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb"
);
ALTER TABLE "public"."sops" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."user_requests" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "text",
    "user_email" "text",
    "ip_address" "text",
    "endpoint" "text",
    "method" "text",
    "request_params" "jsonb",
    "created_at" timestamp with time zone DEFAULT "now"()
);
ALTER TABLE "public"."user_requests" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."users" (
    "id" "uuid" NOT NULL,
    "email" "text" NOT NULL,
    "first_name" "text",
    "last_name" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "last_login" timestamp with time zone,
    "is_admin" boolean DEFAULT false,
    "new_user" boolean DEFAULT true,
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "organization_id" "uuid",
    "org_role" "text" DEFAULT 'admin'::"text" NOT NULL
);
ALTER TABLE "public"."users" OWNER TO "postgres";
ALTER TABLE ONLY "public"."allowed_emails"
    ADD CONSTRAINT "allowed_emails_email_key" UNIQUE ("email");
ALTER TABLE ONLY "public"."allowed_emails"
    ADD CONSTRAINT "allowed_emails_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."gap_analysis_results"
    ADD CONSTRAINT "gap_analysis_results_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."organization_regulations"
    ADD CONSTRAINT "organization_regulations_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."organizations"
    ADD CONSTRAINT "organizations_name_key" UNIQUE ("name");
ALTER TABLE ONLY "public"."organizations"
    ADD CONSTRAINT "organizations_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."regulations"
    ADD CONSTRAINT "regulations_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."request_interactions"
    ADD CONSTRAINT "request_interactions_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."sops"
    ADD CONSTRAINT "sops_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."user_requests"
    ADD CONSTRAINT "user_requests_pkey" PRIMARY KEY ("id");
ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_email_key" UNIQUE ("email");
ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_pkey" PRIMARY KEY ("id");
CREATE INDEX "idx_allowed_emails_email" ON "public"."allowed_emails" USING "btree" ("email");
CREATE INDEX "idx_allowed_emails_org" ON "public"."allowed_emails" USING "btree" ("organization_id");
CREATE INDEX "idx_gap_reg" ON "public"."gap_analysis_results" USING "btree" ("regulation_id");
CREATE INDEX "idx_gap_sop" ON "public"."gap_analysis_results" USING "btree" ("sop_id");
CREATE INDEX "idx_org_reg_org" ON "public"."organization_regulations" USING "btree" ("organization_id");
CREATE INDEX "idx_org_reg_reg" ON "public"."organization_regulations" USING "btree" ("regulation_id");
CREATE INDEX "idx_request_interactions_request_id" ON "public"."request_interactions" USING "btree" ("request_id");
CREATE INDEX "idx_sops_org" ON "public"."sops" USING "btree" ("organization_id");
CREATE INDEX "idx_sops_user" ON "public"."sops" USING "btree" ("uploaded_by");
CREATE INDEX "idx_user_requests_user_id" ON "public"."user_requests" USING "btree" ("user_id");
CREATE INDEX "idx_users_org" ON "public"."users" USING "btree" ("organization_id");
CREATE OR REPLACE TRIGGER "update_gap_analysis_results_updated_at" BEFORE INSERT OR UPDATE ON "public"."gap_analysis_results" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column_for_gap_analysis_results"();
CREATE OR REPLACE TRIGGER "update_organizations_updated_at" BEFORE UPDATE ON "public"."organizations" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();
ALTER TABLE ONLY "public"."allowed_emails"
    ADD CONSTRAINT "allowed_emails_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");
ALTER TABLE ONLY "public"."gap_analysis_results"
    ADD CONSTRAINT "gap_analysis_results_regulation_id_fkey" FOREIGN KEY ("regulation_id") REFERENCES "public"."regulations"("id");
ALTER TABLE ONLY "public"."gap_analysis_results"
    ADD CONSTRAINT "gap_analysis_results_sop_id_fkey" FOREIGN KEY ("sop_id") REFERENCES "public"."sops"("id");
ALTER TABLE ONLY "public"."organization_regulations"
    ADD CONSTRAINT "organization_regulations_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");
ALTER TABLE ONLY "public"."organization_regulations"
    ADD CONSTRAINT "organization_regulations_regulation_id_fkey" FOREIGN KEY ("regulation_id") REFERENCES "public"."regulations"("id");
ALTER TABLE ONLY "public"."regulations"
    ADD CONSTRAINT "regulations_uploaded_by_fkey" FOREIGN KEY ("uploaded_by") REFERENCES "public"."users"("id");
ALTER TABLE ONLY "public"."request_interactions"
    ADD CONSTRAINT "request_interactions_request_id_fkey" FOREIGN KEY ("request_id") REFERENCES "public"."user_requests"("id") ON DELETE CASCADE;
ALTER TABLE ONLY "public"."sops"
    ADD CONSTRAINT "sops_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");
ALTER TABLE ONLY "public"."sops"
    ADD CONSTRAINT "sops_uploaded_by_fkey" FOREIGN KEY ("uploaded_by") REFERENCES "public"."users"("id");
ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") DEFERRABLE INITIALLY DEFERRED;
CREATE POLICY "Anonymous users can check if an email is allowed" ON "public"."allowed_emails" FOR SELECT USING (("email" = (("current_setting"('request.jwt.claims'::"text", true))::"json" ->> 'email'::"text")));
CREATE POLICY "Enable read access for all users" ON "public"."sops" FOR SELECT USING ((("organization_id" = ( SELECT "users"."organization_id"
   FROM "public"."users"
  WHERE ("users"."id" = "auth"."uid"()))) AND (("uploaded_by" = "auth"."uid"()) OR (EXISTS ( SELECT 1
   FROM "public"."users"
  WHERE (("users"."id" = "auth"."uid"()) AND ("users"."org_role" = 'admin'::"text")))))));
CREATE POLICY "Only admins can insert for their org" ON "public"."organization_regulations" FOR INSERT TO "authenticated" WITH CHECK ((("organization_id" = ( SELECT "users"."organization_id"
   FROM "public"."users"
  WHERE ("users"."id" = "auth"."uid"()))) AND (EXISTS ( SELECT 1
   FROM "public"."users"
  WHERE (("users"."id" = "auth"."uid"()) AND ("users"."org_role" = 'admin'::"text"))))));
CREATE POLICY "Only admins can update for their org" ON "public"."organization_regulations" FOR UPDATE USING ((("organization_id" = ( SELECT "users"."organization_id"
   FROM "public"."users"
  WHERE ("users"."id" = "auth"."uid"()))) AND (EXISTS ( SELECT 1
   FROM "public"."users"
  WHERE (("users"."id" = "auth"."uid"()) AND ("users"."org_role" = 'admin'::"text"))))));
CREATE POLICY "Only admins can update their organization" ON "public"."organizations" FOR UPDATE USING ((("id" = ( SELECT "users"."organization_id"
   FROM "public"."users"
  WHERE ("users"."id" = "auth"."uid"()))) AND (EXISTS ( SELECT 1
   FROM "public"."users"
  WHERE (("users"."id" = "auth"."uid"()) AND ("users"."org_role" = 'admin'::"text"))))));
CREATE POLICY "Only org admins can insert allowed emails" ON "public"."allowed_emails" FOR INSERT WITH CHECK ((("organization_id" = ( SELECT "users"."organization_id"
   FROM "public"."users"
  WHERE ("users"."id" = "auth"."uid"()))) AND (EXISTS ( SELECT 1
   FROM "public"."users"
  WHERE (("users"."id" = "auth"."uid"()) AND ("users"."org_role" = 'admin'::"text"))))));
CREATE POLICY "Users can insert SOPs for their org" ON "public"."sops" FOR INSERT WITH CHECK ((("organization_id" = ( SELECT "users"."organization_id"
   FROM "public"."users"
  WHERE ("users"."id" = "auth"."uid"()))) AND ("uploaded_by" = "auth"."uid"())));
CREATE POLICY "Users can insert gap results" ON "public"."gap_analysis_results" FOR INSERT WITH CHECK (("sop_id" IN ( SELECT "sops"."id"
   FROM "public"."sops"
  WHERE ("sops"."organization_id" = ( SELECT "users"."organization_id"
           FROM "public"."users"
          WHERE ("users"."id" = "auth"."uid"()))))));
CREATE POLICY "Users can read all regulations" ON "public"."regulations" FOR SELECT USING (("auth"."uid"() IS NOT NULL));
CREATE POLICY "Users can read gap results for their org's SOPs" ON "public"."gap_analysis_results" FOR SELECT USING (("sop_id" IN ( SELECT "sops"."id"
   FROM "public"."sops"
  WHERE ("sops"."organization_id" = ( SELECT "users"."organization_id"
           FROM "public"."users"
          WHERE ("users"."id" = "auth"."uid"()))))));
CREATE POLICY "Users can read others from their org" ON "public"."users" FOR SELECT USING (("organization_id" = "public"."get_auth_user_org_id"()));
CREATE POLICY "Users can read their org's regulations" ON "public"."organization_regulations" FOR SELECT USING (("organization_id" = ( SELECT "users"."organization_id"
   FROM "public"."users"
  WHERE ("users"."id" = "auth"."uid"()))));
CREATE POLICY "Users can read their organization" ON "public"."organizations" FOR SELECT USING (("id" = ( SELECT "users"."organization_id"
   FROM "public"."users"
  WHERE ("users"."id" = "auth"."uid"()))));
CREATE POLICY "Users can update gap results" ON "public"."gap_analysis_results" FOR UPDATE USING (("sop_id" IN ( SELECT "sops"."id"
   FROM "public"."sops"
  WHERE ("sops"."organization_id" = ( SELECT "users"."organization_id"
           FROM "public"."users"
          WHERE ("users"."id" = "auth"."uid"()))))));
CREATE POLICY "Users can update their own SOPs; admins can update any org SOPs" ON "public"."sops" FOR UPDATE USING ((("uploaded_by" = "auth"."uid"()) OR (("organization_id" = ( SELECT "users"."organization_id"
   FROM "public"."users"
  WHERE ("users"."id" = "auth"."uid"()))) AND (EXISTS ( SELECT 1
   FROM "public"."users"
  WHERE (("users"."id" = "auth"."uid"()) AND ("users"."org_role" = 'admin'::"text")))))));
CREATE POLICY "Users can update their own record" ON "public"."users" FOR UPDATE USING (("id" = "auth"."uid"()));
CREATE POLICY "Users can view allowed emails for their organization" ON "public"."allowed_emails" FOR SELECT USING (("organization_id" = ( SELECT "users"."organization_id"
   FROM "public"."users"
  WHERE ("users"."id" = "auth"."uid"()))));
ALTER TABLE "public"."allowed_emails" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."gap_analysis_results" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."organization_regulations" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."organizations" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."regulations" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."sops" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."users" ENABLE ROW LEVEL SECURITY;
ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";
GRANT ALL ON FUNCTION "public"."get_auth_user_org_id"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_auth_user_org_id"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_auth_user_org_id"() TO "service_role";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "service_role";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "service_role";
GRANT ALL ON FUNCTION "public"."update_updated_at_column_for_gap_analysis_results"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_updated_at_column_for_gap_analysis_results"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_updated_at_column_for_gap_analysis_results"() TO "service_role";
GRANT ALL ON TABLE "public"."allowed_emails" TO "anon";
GRANT ALL ON TABLE "public"."allowed_emails" TO "authenticated";
GRANT ALL ON TABLE "public"."allowed_emails" TO "service_role";
GRANT ALL ON TABLE "public"."gap_analysis_results" TO "anon";
GRANT ALL ON TABLE "public"."gap_analysis_results" TO "authenticated";
GRANT ALL ON TABLE "public"."gap_analysis_results" TO "service_role";
GRANT ALL ON TABLE "public"."organization_regulations" TO "anon";
GRANT ALL ON TABLE "public"."organization_regulations" TO "authenticated";
GRANT ALL ON TABLE "public"."organization_regulations" TO "service_role";
GRANT ALL ON TABLE "public"."organizations" TO "anon";
GRANT ALL ON TABLE "public"."organizations" TO "authenticated";
GRANT ALL ON TABLE "public"."organizations" TO "service_role";
GRANT ALL ON TABLE "public"."regulations" TO "anon";
GRANT ALL ON TABLE "public"."regulations" TO "authenticated";
GRANT ALL ON TABLE "public"."regulations" TO "service_role";
GRANT ALL ON TABLE "public"."request_interactions" TO "anon";
GRANT ALL ON TABLE "public"."request_interactions" TO "authenticated";
GRANT ALL ON TABLE "public"."request_interactions" TO "service_role";
GRANT ALL ON TABLE "public"."sops" TO "anon";
GRANT ALL ON TABLE "public"."sops" TO "authenticated";
GRANT ALL ON TABLE "public"."sops" TO "service_role";
GRANT ALL ON TABLE "public"."user_requests" TO "anon";
GRANT ALL ON TABLE "public"."user_requests" TO "authenticated";
GRANT ALL ON TABLE "public"."user_requests" TO "service_role";
GRANT ALL ON TABLE "public"."users" TO "anon";
GRANT ALL ON TABLE "public"."users" TO "authenticated";
GRANT ALL ON TABLE "public"."users" TO "service_role";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";
RESET ALL;
