# .github/workflows/deploy-latent-frontend.yml
# Unified workflow for Latent Frontend (dev, demo, main)

name: Deploy to Azure Static Web App – Latent frontend

on:
  push:
    branches: [dev, demo, main]      # promotion branches
  workflow_dispatch:

jobs:
  build_and_deploy:
    runs-on: ubuntu-latest
    # Pick GitHub Environment that matches the branch name
    environment: ${{ github.ref_name }}     # dev | demo | main

    env:
      REACT_APP_SUPABASE_URL:      ${{ secrets.REACT_APP_SUPABASE_URL }}
      REACT_APP_SUPABASE_ANON_KEY: ${{ secrets.REACT_APP_SUPABASE_ANON_KEY }}
      REACT_APP_API_URL:           ${{ secrets.REACT_APP_API_URL }}
      CI: false

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Install dependencies
        run: npm install

      - name: Build React app
        run: npm run build

      - name: Deploy to Azure Static Web App
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN }}
          repo_token: ${{ secrets.GITHUB_TOKEN }} # Used for GitHub integrations (i.e., PR comments)
          action: "upload"
          app_location: "/"
          output_location: "build"
