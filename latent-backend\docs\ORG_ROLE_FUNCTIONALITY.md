# Organization Role (org_role) Functionality

This document describes the organization role functionality that has been added to the allowed_emails table and user creation process.

## Overview

The `org_role` field has been added to both the `allowed_emails` and `users` tables to support role-based access control within organizations. When a user is created, their `org_role` is automatically copied from the `allowed_emails` table to the `users` table.

## Database Schema Changes

### allowed_emails Table

The `allowed_emails` table now includes an `org_role` column:

```sql
CREATE TABLE allowed_emails (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT UNIQUE NOT NULL,
    organization_id UUID REFERENCES organizations(id),
    is_active BOOLEAN DEFAULT true,
    expiry_date TIMESTAMPTZ,
    added_at TIMESTAMPTZ DEFAULT now(),
    added_by TEXT,
    org_role TEXT NOT NULL DEFAULT 'admin'  -- NEW COLUMN
);
```

### users Table

The `users` table already had the `org_role` column:

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    first_name TEX<PERSON>,
    last_name TEX<PERSON>,
    created_at TIMESTAMPTZ DEFAULT now(),
    last_login TIMESTAMPTZ,
    is_admin BOOLEAN DEFAULT false,
    new_user BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}',
    organization_id UUID,
    org_role TEXT NOT NULL DEFAULT 'admin'
);
```

## User Creation Process

The `handle_new_user()` trigger function has been updated to copy the `org_role` from `allowed_emails` to `users`:

```sql
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
DECLARE
  org_id UUID;
  fname TEXT;
  lname TEXT;
  user_org_role TEXT;
BEGIN
  -- Get organization ID and org_role for this email from allowed_emails table
  SELECT organization_id, org_role INTO org_id, user_org_role
  FROM public.allowed_emails
  WHERE email = NEW.email AND is_active = true
  LIMIT 1;

  -- Extract first and last name from raw_user_meta_data
  fname := NEW.raw_user_meta_data ->> 'first_name';
  lname := NEW.raw_user_meta_data ->> 'last_name';

  -- Insert into public.users with all required fields including org_role
  INSERT INTO public.users (id, email, organization_id, first_name, last_name, org_role)
  VALUES (NEW.id, NEW.email, org_id, fname, lname, COALESCE(user_org_role, 'admin'));

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Supported Roles

The system currently supports the following organization roles:

- **admin**: Full administrative access within the organization
- **user**: Standard user access
- **viewer**: Read-only access

Additional roles can be added by updating the validation logic in the management scripts.

## Managing Allowed Emails with Roles

### Adding Emails with Roles

Use the updated `manage_allowlist.py` script to add emails with specific roles:

```bash
# Add an admin user (default)
python scripts/manage_allowlist.<NAME_EMAIL> --org ORG_ID

# Add a user with specific role
python scripts/manage_allowlist.<NAME_EMAIL> --org ORG_ID --role user

# Add a viewer
python scripts/manage_allowlist.<NAME_EMAIL> --org ORG_ID --role viewer
```

### Command Line Options

```bash
python scripts/manage_allowlist.py add --help

usage: manage_allowlist.py add [-h] --org ORGANIZATION_ID [--role ORG_ROLE] email

positional arguments:
  email                 Email address to add

optional arguments:
  -h, --help            show this help message and exit
  --org ORGANIZATION_ID, -o ORGANIZATION_ID
                        Organization ID (required)
  --role ORG_ROLE, -r ORG_ROLE
                        Organization role for the user (default: admin). 
                        Valid roles: admin, user, viewer
```

### Listing Emails with Roles

The list command now shows the org_role for each email:

```bash
python scripts/manage_allowlist.py list
```

Output example:
```
Allowed Emails:
========================================================================================================================
ID                                   | Email                          | Organization ID                      | Role       | Is Active
------------------------------------------------------------------------------------------------------------------------
12345678-1234-5678-9012-123456789012 | <EMAIL>              | 87654321-4321-8765-2109-876543210987 | admin      | True
23456789-2345-6789-0123-234567890123 | <EMAIL>               | 87654321-4321-8765-2109-876543210987 | user       | True
34567890-3456-7890-1234-345678901234 | <EMAIL>             | 87654321-4321-8765-2109-876543210987 | viewer     | True

Total: 3 emails
```

## Database Migration

To apply the org_role functionality to an existing database, run the migration:

```sql
-- Add org_role column to allowed_emails table
ALTER TABLE allowed_emails ADD COLUMN org_role TEXT NOT NULL DEFAULT 'admin';

-- Update the handle_new_user function
-- (See the complete function definition in sql/migrations/add_org_role_to_allowed_emails.sql)
```

## Testing

A comprehensive test script is provided to verify the functionality:

```bash
python scripts/test_org_role_functionality.py
```

This script tests:
1. Adding emails with different org_roles to allowed_emails
2. Verifying that org_role is stored correctly in allowed_emails
3. Simulating user creation to verify org_role transfer

## API Integration

The org_role functionality integrates with existing APIs:

### User Management API

The user management endpoints will now return and work with the org_role field:

```json
{
  "id": "user-id",
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "organization_id": "org-id",
  "org_role": "user",
  "is_admin": false,
  "new_user": false
}
```

### Row Level Security (RLS)

The existing RLS policies already use org_role for access control:

```sql
CREATE POLICY "Only org admins can insert allowed emails"
ON allowed_emails
FOR INSERT
WITH CHECK (
  organization_id = (
    SELECT organization_id FROM users WHERE id = auth.uid()
  ) AND
  EXISTS (
    SELECT 1 FROM users
    WHERE id = auth.uid() AND org_role = 'admin'
  )
);
```

## Best Practices

1. **Default Role**: Always use 'admin' as the default role for backward compatibility
2. **Role Validation**: Validate roles before inserting into allowed_emails
3. **Consistent Naming**: Use lowercase role names for consistency
4. **Documentation**: Document any new roles added to the system

## Troubleshooting

### Common Issues

1. **Invalid Role Error**: Ensure the role is one of the supported values (admin, user, viewer)
2. **Missing org_role in users**: Check that the allowed_emails record exists and is active
3. **Permission Denied**: Verify the user has the correct org_role for the operation

### Debugging

To debug org_role issues:

1. Check the allowed_emails table:
   ```sql
   SELECT email, org_role, is_active FROM allowed_emails WHERE email = '<EMAIL>';
   ```

2. Check the users table:
   ```sql
   SELECT email, org_role FROM users WHERE email = '<EMAIL>';
   ```

3. Verify the trigger function is working:
   ```sql
   SELECT proname FROM pg_proc WHERE proname = 'handle_new_user';
   ```

## Future Enhancements

Potential improvements to consider:

1. **Dynamic Roles**: Allow organizations to define custom roles
2. **Role Hierarchy**: Implement role inheritance and permissions
3. **Role History**: Track role changes over time
4. **Bulk Role Updates**: Support updating roles for multiple users
5. **Role-based UI**: Customize frontend based on user roles

## Security Considerations

1. **Role Validation**: Always validate roles on both client and server side
2. **Privilege Escalation**: Prevent users from assigning higher privileges than they have
3. **Audit Trail**: Log all role changes for security auditing
4. **Default Permissions**: Use least-privilege principle for default roles
