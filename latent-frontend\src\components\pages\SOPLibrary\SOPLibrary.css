.sop-library {
  /* padding: 2rem; */
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  color: #666;
  font-size: 14px;
}

.separator {
  color: #999;
}

.page-header {
  display: flex;
  align-items: baseline;
  gap: 1rem;
  margin-bottom: 2rem;
}

.page-header h1 {
  font-size: 24px;
  color: #333;
  margin: 0;
}

.subtitle {
  color: #666;
  font-size: 14px;
}

.navigation-tabs {
  border-bottom: 1px solid #eee;
  margin-bottom: 2rem;
}

.tabs {
  display: flex;
  gap: 2rem;
}

.tab {
  padding: 1rem 0;
  color: #666;
  cursor: pointer;
  font-size: 14px;
  position: relative;
}

.tab.active {
  color: #333;
  border-bottom: 2px solid #333;
}

.search-bar {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-bottom: 2rem;
}

.search-section {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-bottom: 1rem;
}

.filter-sort-controls {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background-color: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-group label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
}

.sort-buttons {
  display: flex;
  gap: 0.5rem;
}

.sort-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #e2e8f0;
  background-color: white;
  color: #6b7280;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  transition: all 0.2s;
}

.sort-btn:hover {
  background-color: #f9fafb;
  border-color: #d1d5db;
}

.sort-btn.active {
  background-color: #6c63ff;
  color: white;
  border-color: #6c63ff;
}

.sort-direction {
  font-size: 12px;
  margin-left: 0.25rem;
}

.filter-select {
  padding: 0.5rem 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background-color: white;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  min-width: 120px;
}

.filter-select:focus {
  outline: none;
  border-color: #6c63ff;
  box-shadow: 0 0 0 3px rgba(108, 99, 255, 0.1);
}

.reset-filters-btn {
  padding: 0.5rem 1rem;
  background-color: #f3f4f6;
  color: #6b7280;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
  margin-left: auto;
}

.reset-filters-btn:hover:not(:disabled) {
  background-color: #e5e7eb;
  color: #374151;
}

.reset-filters-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.search-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #e1e1e1;
  border-radius: 6px;
  font-size: 14px;
  background-color: #f8f8f8;
}

.add-sop-btn {
  padding: 0.75rem 1.5rem;
  background-color: #6c63ff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.connect-qms-btn{
  padding: 0.75rem 1.5rem;
  background-color: #d2d2d2;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  cursor: not-allowed;
}

.sop-list h2 {
  font-size: 16px;
  color: #333;
  margin-bottom: 1rem;
}

.sop-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.sop-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eee;
}

.sop-info h3 {
  font-size: 14px;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.department {
  font-size: 14px;
  color: #666;
}

.sop-meta {
  display: flex;
  gap: 2rem;
  color: #666;
  font-size: 14px;
  align-items: center;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
  color: #666;
  font-size: 14px;
}

.pagination-btn {
  padding: 0.5rem 1rem;
  border: none;
  background: none;
  color: #6c63ff;
  cursor: pointer;
  font-size: 14px;
}

.pagination-btn:hover {
  text-decoration: underline;
}

.sop-content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 60px); /* Subtract header height */
  overflow: hidden;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.sop-list {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  margin-bottom: 1rem;
}

.sop-items {
  overflow-y: auto;
  flex: 1;
  padding-right: 0.5rem; /* Add some padding for the scrollbar */
  max-height: calc(100vh - 250px); /* Adjust based on your header, filters, and search bar heights */
}

/* Style the scrollbar for better appearance */
.sop-items::-webkit-scrollbar {
  width: 8px;
}

.sop-items::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.sop-items::-webkit-scrollbar-thumb {
  background: #c5c5c5;
  border-radius: 4px;
}

.sop-items::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Make sure the SOP items have proper spacing */
.sop-item {
  margin-bottom: 1rem;
  padding: 1rem;
  border: 1px solid #e1e1e1;
  border-radius: 6px;
  background-color: white;
}

.sop-item:last-child {
  margin-bottom: 0.5rem;
}

.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: #666;
  font-size: 14px;
}

.error-message {
  background-color: #ffe6e6;
  color: #d32f2f;
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1.5rem;
  font-size: 14px;
}

.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #666;
  font-size: 14px;
}

.view-icon-link {
  background: none;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 26px;
  height: 26px;
  min-width: 26px;
  min-height: 26px;
  border-radius: 50%;
  background-color: #f0f0f7;
  transition: all 0.2s ease;
  position: relative;
  cursor: pointer;
  padding: 0;
}

.view-icon-link:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.view-icon-link:hover:not(:disabled) {
  background-color: #e6e4ff;
}

.view-icon-link[data-tooltip]:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: -30px;
  right: -15px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 10;
}

.view-icon {
  width: 16px;
  height: 16px;
}

/* Add these styles to make the spinner properly round in the download button */
.view-icon-link .loading-spinner {
  width: 16px;
  height: 16px;
  border-width: 2px;
  margin: 0;
}

.sop-actions {
  display: flex;
  gap: 8px;
}

.delete-icon-link {
  background: none;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 26px;
  height: 26px;
  min-width: 26px;
  min-height: 26px;
  border-radius: 50%;
  background-color: #fff0f0;
  transition: all 0.2s ease;
  position: relative;
  cursor: pointer;
  padding: 0;
}

.delete-icon-link:hover:not(:disabled) {
  background-color: #ffe6e6;
}

.delete-icon-link:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.delete-icon-link[data-tooltip]:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: -30px;
  right: -15px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 10;
}

.delete-icon {
  width: 16px;
  height: 16px;
}

.play-icon-link {
  background: none;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 26px;
  height: 26px;
  min-width: 26px;
  min-height: 26px;
  border-radius: 50%;
  background-color: #f0f0f7;
  transition: all 0.2s ease;
  position: relative;
  cursor: pointer;
  padding: 0;
}

.play-icon-link:hover {
  background-color: #e6e4ff;
}

.play-icon-link[data-tooltip]:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: -30px;
  right: -15px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 10;
}

.play-icon {
  width: 16px;
  height: 16px;
  margin-left: 3px;
}

.play-icon-link:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f0f0f0;
}

.play-icon-link:disabled svg path {
  stroke: #aaa;
}

.play-icon-link:disabled:hover {
  background-color: #f0f0f0;
}

/* Prevent tooltip from showing when disabled */
.play-icon-link:disabled[data-tooltip]:hover::after {
  display: none;
}