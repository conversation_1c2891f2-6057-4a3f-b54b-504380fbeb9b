# Same as @sop_gap_steps_generator, but decoupled with the processes mapping output

from typing import List, Dict
from pydantic import BaseModel
import pandas as pd
from app.services.gap_analysis_utils.llm_interact import LLMInteract
from app.services.error_logger import log_interaction, log_interaction_sync

class GapStep(BaseModel):
    critique: str
    guidelines_reference: str
    sop_reference: str
    priority: str
    missing_step: str

class SOPGapStepGenerator:
    def __init__(self, llm: LLMInteract):
        self.llm = llm

    async def generate_missing_steps(
        self, gaps: List[Dict], sop_text: str, request_id=None
    ) -> List[GapStep]:
        """
        Generate missing steps based on process mapping and gap analysis.

        Args:
            process_mapping: List of process mapping steps from SOPProcessorRAG
            gaps: List of gaps identified by SOPGapAnalyzer
            sop_text: Original SOP text

        Returns:
            List of GapStep objects containing gaps and their missing steps
        """
        try:
            if request_id:
                import asyncio
                asyncio.create_task(log_interaction(request_id, "generate_missing_steps_start", "Starting generate_missing_steps", params={}))
            prompt = self._create_missing_steps_prompt(gaps, sop_text)

            # Since qna_gemini is synchronous, run it in a thread to avoid blocking
            import asyncio
            response = await asyncio.to_thread(self.llm.qna_gemini, prompt, request_id=request_id)
            steps = self._parse_llm_response(response)

            if request_id:
                import asyncio
                asyncio.create_task(log_interaction(request_id, "generate_missing_steps_end", "Finished generate_missing_steps", response={"steps": steps}))
            return steps
        except Exception as e:
            if request_id:
                import asyncio
                asyncio.create_task(log_interaction(request_id, "generate_missing_steps_exception", "Exception in generate_missing_steps", status="error", response={"error": str(e), "steps": steps if 'steps' in locals() else None}))
            raise Exception("A temporary issue occurred while analyzing the SOP.")

    def _create_missing_steps_prompt(
        self, gaps: List[Dict], sop_text: str
    ) -> str:
        return """You are a pharmaceutical quality assurance expert. Analyze the provided SOP, its process mapping, and identified gaps to suggest specific missing steps that should be added to the SOP.

Rules to follow:
- For each gap identified, suggest a specific step that should be added to the SOP
- The step should be detailed and actionable
- Indicate where in the process sequence the step should be added
- Format your response as a JSON array with the following structure for each gap:
  - Include the original gap information
  - Add a "missing_step" field with the specific step that should be added


Identified Gaps:
__identified_gaps__

Original SOP Text:
__original_sop_text__

Output your response in the following JSON format:
```json
[
{
"critique": "original gap critique",
"guidelines_reference": "original reference",
"sop_reference": "original sop reference",
"priority": "original priority",
"missing_step": "detailed description of the missing step"
}
]
```
""".replace("__identified_gaps__", pd.DataFrame(gaps).to_string()).replace("__original_sop_text__", sop_text)

    def _parse_llm_response(self, response: str) -> List[GapStep]:
        """Parse and validate LLM response into GapStep objects"""
        # Extract JSON from response (assuming it's wrapped in ```json ```)
        json_str = response.split("```json")[1].split("```")[0].strip()

        # Convert to list of GapStep objects
        steps = [GapStep(**step) for step in eval(json_str)]
        return steps


if __name__ == "__main__":
    import sys
    import os
    import json
    from app.services.gap_analysis_utils.config import Config
    from app.services.gap_analysis_utils.sop_gap_analysis import SOPGapAnalyzer
    
    # Initialize components
    current_dir = os.path.dirname(os.path.abspath(__file__))
    config_path = os.path.join(current_dir, "user_config.yaml")
    cfg = Config(config_path)
    llm = LLMInteract(config=cfg)
    
    # Example SOP text (or load from a file)
    with open(os.path.join(current_dir, "../demo_sops/sop.txt"), "r") as f:
        sop_text = f.read()
    
    # Gap analysis
    gap_analyzer = SOPGapAnalyzer(llm)
    documents = []
    guidelines_path = os.path.join(current_dir, "demo_guidelines")
    for file in os.listdir(guidelines_path):
        if file.endswith(".txt"):
            with open(os.path.join(guidelines_path, file), "r") as f:
                documents.append(f.read())
    gaps = gap_analyzer.analyze_sop_gaps(sop_text, documents)
    
    # Generate missing steps
    step_generator = SOPGapStepGenerator(llm)
    gap_steps = step_generator.generate_missing_steps(gaps, sop_text)
    
    # Print results
    print("Generated Missing Steps:")
    for i, step in enumerate(gap_steps, 1):
        print(f"\nStep {i}:")
        print(f"  Critique: {step.critique}")
        print(f"  Guidelines Reference: {step.guidelines_reference}")
        print(f"  SOP Reference: {step.sop_reference}")
        print(f"  Priority: {step.priority}")
        print(f"  Missing Step: {step.missing_step}")
    
    # Save results to file for inspection

