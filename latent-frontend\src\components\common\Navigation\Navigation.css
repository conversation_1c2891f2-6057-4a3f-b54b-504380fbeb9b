.navigation-container {
  width: 100%;
}

.nav-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  border-bottom: 1px solid var(--border-slate-200);
}

.nav-header h1 {
  font-size: 20px;
  color: var(--text-slate-900);
  margin: 0;
}

.user-profile {
  display: flex;
  align-items: center;
}

.profile-image_main {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  cursor: pointer;
}

.nav-tabs {
  display: flex;
  align-items: center;
  padding: 0 2rem;
  border-bottom: 1px solid var(--border-slate-200);
}

.nav-tab {
  padding: 1rem 0;
  color: var(--text-slate-600);
  text-decoration: none;
  font-size: 14px;
  position: relative;
}

.nav-separator {
  color: var(--text-slate-400);
  margin: 0 1.5rem;
  font-size: 8px;
  line-height: 1;
}

.nav-tab.active {
  color: var(--text-slate-900);
  border-bottom: 2px solid var(--text-slate-900);
}

.nav-tab:hover {
  color: var(--text-slate-900);
}

.profile-link {
  display: block;
  text-decoration: none;
  transition: opacity 0.2s;
}

.profile-link:hover {
  opacity: 0.8;
}

.profile-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logout-btn {
  padding: 0.5rem 1rem;
  background-color: transparent;
  color: var(--text-slate-600);
  border: 1px solid var(--border-slate-200);
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.logout-btn:hover {
  background-color: var(--stat-blue-bg);
  color: var(--primary-blue);
  border-color: var(--primary-blue);
}

.profile-dropdown {
  position: relative;
}

.profile-dropdown .dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  left: unset !important;
  margin-top: 0.5rem;
  background: var(--bg-white);
  border: 1px solid var(--border-slate-200);
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-width: 150px;
  z-index: 1000;
  overflow: hidden;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  text-align: left;
  background: none;
  border: none;
  font-size: 14px;
  color: var(--text-slate-900);
  cursor: pointer;
  text-decoration: none;
}

.dropdown-item:hover {
  background-color: var(--stat-blue-bg);
  color: var(--primary-blue);
}

.dropdown-item:not(:last-child) {
  border-bottom: 1px solid var(--border-slate-200);
}

.profile-image_main {
  cursor: pointer;
}

.loading-text {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.loading-dots {
  position: relative;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--primary-blue);
  animation: dots 1s infinite;
}

.loading-dots::before,
.loading-dots::after {
  content: '';
  position: absolute;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--primary-blue);
  animation: dots 1s infinite;
}

.loading-dots::before {
  left: -18px;
  animation-delay: -0.32s;
}

.loading-dots::after {
  left: 18px;
  animation-delay: 0.32s;
}

@keyframes dots {
  0%, 80%, 100% { 
    transform: scale(0);
  }
  40% { 
    transform: scale(1.0);
  }
}

.dropdown-item:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.settings-icon {
  color: var(--text-slate-600);
  width: 24px;
  height: 24px;
  transition: color 0.2s ease;
}

.profile-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.profile-button:hover {
  background-color: var(--hover-slate-50);
}

.profile-button:hover .settings-icon {
  color: var(--primary-blue);
}

/* Add styles for the logo image */
.logo-image {
  height: 40px; /* Adjust height as needed */
  width: auto;
  display: block;
}

/* You might need to adjust the nav-logo container */
.nav-logo {
  display: flex;
  align-items: center;
} 