import yaml
import os


class Config:
    def __init__(self, config_file):
        self.config_file = config_file
        self.config_data = self.load_config()

    def load_config(self):
        with open(self.config_file, "r") as file:
            return yaml.safe_load(file)

    def get(self, key, default=None):
        keys = key.split(".")
        value = self.config_data
        for k in keys:
            if isinstance(value, dict):
                value = value.get(k, default)
            else:
                return default
        return value


# Example usage:
# config = Config('path/to/config.yaml')
# value = config.get('some_key', 'default_value')


if __name__ == "__main__":
    config = Config("configs/user_config.yaml")
    print("Loaded Config:")
    print(config)
    print("=" * 100)
    print("Config loaded successfully")
