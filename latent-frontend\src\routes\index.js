import React from 'react';
import { Navigate } from 'react-router-dom';
import Login from '../components/pages/Login/Login';
import Dashboard from '../components/pages/Dashboard/Dashboard';
import ProfileSettings from '../components/pages/ProfileSettings/ProfileSettings';
import SOPLibrary from '../components/pages/SOPLibrary/SOPLibrary';
import GapAnalysis from '../components/pages/GapAnalysis/GapAnalysis';
import ProtectedRoute from '../components/common/ProtectedRoute/ProtectedRoute';
import NotFound from '../components/NotFound';
import Layout from '../components/common/Layout/Layout';
const routes = (user) => [
  {
    path: '/login',
    element: user ? <Navigate to="/dashboard" replace /> : <Login />
  },
  {
    path: '/dashboard',
    element: (
      <ProtectedRoute user={user}>
        <Layout >
          <Dashboard />
        </Layout>
      </ProtectedRoute>
    )
  },
  {
    path: '/profile',
    element: (
      <ProtectedRoute user={user}>
        <Layout>
          <ProfileSettings />
        </Layout>
      </ProtectedRoute>
    )
  },
  {
    path: '/sop-library',
    element: (
      <ProtectedRoute user={user}>
        <Layout>
          <SOPLibrary />
        </Layout>
      </ProtectedRoute>
    )
  },
  {
    path: '/gap-analysis',
    element: (
      <ProtectedRoute user={user}>
        <Layout>
          <GapAnalysis />
        </Layout>
      </ProtectedRoute>
    )
  },
  {
    path: '/',
    element: <Navigate to={user ? "/dashboard" : "/login"} replace />
  },
  {
    path: '*',
    element: <NotFound />
  }
];

export default routes; 