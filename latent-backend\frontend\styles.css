* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f5f5f5;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 1rem;
}

.container {
    max-width: 500px;
    width: 100%;
    background-color: #ffffff;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: 2rem;
}

h1 {
    text-align: center;
    margin-bottom: 1.5rem;
    color: #333;
}

h2 {
    margin-bottom: 1.5rem;
    color: #444;
}

.tab-container {
    display: flex;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid #ddd;
}

.tab-button {
    background: none;
    border: none;
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
    cursor: pointer;
    outline: none;
    flex: 1;
    transition: background-color 0.3s, color 0.3s;
}

.tab-button.active {
    border-bottom: 3px solid #4c6ef5;
    color: #4c6ef5;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.form-group {
    margin-bottom: 1.2rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

input {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    outline: none;
    transition: border-color 0.3s;
}

input:focus {
    border-color: #4c6ef5;
}

.btn {
    width: 100%;
    padding: 0.8rem;
    background-color: #4c6ef5;
    color: #fff;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s;
    margin-top: 0.5rem;
}

.btn:hover {
    background-color: #3b5bdb;
}

.message {
    margin-top: 1.5rem;
    text-align: center;
    padding: 0.8rem;
    border-radius: 5px;
    display: none;
}

.message.error {
    background-color: #ffe3e3;
    color: #e03131;
    display: block;
}

.message.success {
    background-color: #d3f9d8;
    color: #2b8a3e;
    display: block;
}

.info-text {
    margin-top: 1rem;
    font-size: 0.9rem;
    color: #666;
    text-align: center;
}

#user-container {
    text-align: center;
}

#user-email {
    font-weight: bold;
    color: #4c6ef5;
}

/* Organization info card */
#org-info {
    margin-top: 20px;
    margin-bottom: 20px;
}

#org-info h3 {
    color: #333;
    margin-bottom: 10px;
}

.info-card {
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.info-card p {
    margin: 8px 0;
    font-size: 14px;
}

.info-card strong {
    color: #555;
    font-weight: 600;
}

/* Debug panel updates */
#debug-panel {
    max-height: 50vh;
    overflow-y: auto;
}

.debug-data {
    max-height: 200px;
    overflow-y: auto;
}

/* Delete Account Styles */
.btn-danger {
    background-color: #e74c3c;
    color: white;
    margin-top: 10px;
}

.btn-danger:hover {
    background-color: #c0392b;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: #fefefe;
    margin: auto;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    max-width: 400px;
}

.modal-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.modal-buttons button {
    width: 48%;
} 